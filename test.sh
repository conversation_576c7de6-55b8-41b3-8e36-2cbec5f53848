#!/bin/bash
set -e # stop on error

source .venv/bin/activate

echo "backend tests"
pytest backend_test --order-dependencies --verbose

echo "worker tests"
pytest worker_test --order-dependencies --verbose

echo "frontend tests"
opts="--project-directory=. --project-name=inherit --file=repository/compose.yaml --file=backend/compose.yaml --file=frontend/compose-db.yaml"
docker compose $opts up --detach
trap "docker compose $opts down" EXIT
export POSTGRES_HOST=localhost
export POSTGRES_USERNAME=postgres
export POSTGRES_PASSWORD=password
export POSTGRES_DATABASE=postgres
export POSTGRES_SSLMODE=disable
export POSTGRES_PORT=5433
export BACKEND_HOST=localhost
export BACKEND_USERNAME=postgres
export BACKEND_PASSWORD=password
export BACKEND_DATABASE=postgres
export BACKEND_SSLMODE=disable
export SECRET_KEY=foo
export DOCS_PASSWORD=bar
export TOKEN_ISSUER=beep
export TOKEN_AUDIENCE=boop
export NON_PROD_VERSION="2024-10-21"
export NON_PROD_OPENAI_ENDPOINT="https://inherit-nonproduction-openai.openai.azure.com/"
python frontend/manage.py test
docker compose $opts down
