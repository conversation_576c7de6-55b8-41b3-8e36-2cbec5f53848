from typing import Any
from collections.abc import Callable
from backend.config import LOG_STYLE, LOG_LEVEL
import structlog
from jinja2 import Environment, PackageLoader, select_autoescape
from asgi_correlation_id import correlation_id
import logging


def add_correlation(logger: logging.Logger, method_name: str, event_dict: dict[str, Any]) -> dict[str, Any]:
    """Add request id to log message."""
    if request_id := correlation_id.get():
        event_dict["request_id"] = request_id
    return event_dict


processors: list[Callable[[Any, Any, Any], Any]] = [
  structlog.contextvars.merge_contextvars,
  add_correlation,
  structlog.processors.add_log_level,
  structlog.processors.StackInfoRenderer(),
  structlog.processors.TimeStamper(fmt="iso"),
  structlog.processors.CallsiteParameterAdder(
    parameters={
      structlog.processors.CallsiteParameter.FUNC_NAME,
      structlog.processors.CallsiteParameter.LINENO,
    },
  ),
]

if LOG_STYLE == "json":
  processors.append(structlog.processors.dict_tracebacks)
  processors.append(structlog.processors.ExceptionRenderer())
  processors.append(structlog.processors.JSONRenderer())
else:
  processors.append(structlog.dev.ConsoleRenderer())

structlog.configure(
  cache_logger_on_first_use=True,
  wrapper_class=structlog.make_filtering_bound_logger(LOG_LEVEL),
  logger_factory=structlog.PrintLoggerFactory(),
  processors=processors,
)

env = Environment(
  loader=PackageLoader(package_name="backend"),
  autoescape=select_autoescape()
)
