from backend.repository import queries, models
from backend.db import Store
import structlog

logger = structlog.get_logger(module=__name__)


async def award_reward_point(store: Store, sub: str, reason: str) -> None:
  """Add one reward point to the user for the provided reason"""
  user = await store.get_user_by_sub(sub=sub)
  assert isinstance(user, models.User)
  result = None
  match reason:
    case models.UserRewardPointLogReason.PERSONALHEALTHSURVEY:
      result = await store.add_user_reward_point_for_personal_health_survey(sub=sub)
    case models.UserRewardPointLogReason.REPRODUCTIVEHEALTHSURVEY:
      result = await store.add_user_reward_point_for_reproductive_health_survey(sub=sub)
    case models.UserRewardPointLogReason.LIFESTYLESURVEY:
      result = await store.add_user_reward_point_for_lifestyle_survey(sub=sub)
    case models.UserRewardPointLogReason.SHARING:
      result = await store.add_user_reward_point_for_sharing(sub=sub)
    case models.UserRewardPointLogReason.SURVEY | models.UserRewardPointLogReason.REFERRAL:
      result = await store.add_user_reward_point(sub=sub)
    case _:
      raise RuntimeError(f"unsupported reward point reason: {reason}")
  if result is None:
    return  # indicates the user already had the reward point
  else:
    await store.create_user_reward_point_log(queries.CreateUserRewardPointLogParams(
      sub=sub,
      reason=reason,
      points=1
    ))


async def referral_completion_check(store: Store, sub: str) -> None:
  """Check if the user has completed all their surveys, and award their referrer a reward point if they have"""
  user = await store.get_user_by_sub(sub=sub)
  assert isinstance(user, models.User)
  if user.reward_point_awarded_for_personal_health_survey and user.reward_point_awarded_for_reproductive_health_survey and user.reward_point_awarded_for_lifestyle_survey:
    result = await store.mark_user_reward_point_for_referral(sub=sub)
    if result is not None and user.referrer is not None:
      referrer = await store.get_user_by_referral_code(referral_code=user.referrer)
      if referrer is None:
        logger.warning("referrer not found", referrer=user.referrer)
        return
      assert isinstance(referrer, models.User)
      await award_reward_point(store=store, sub=referrer.sub, reason=models.UserRewardPointLogReason.REFERRAL)
