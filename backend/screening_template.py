from backend.repository import models, queries
from backend.db import Store
from datetime import date, timedelta
import pydantic
import enum
import structlog

logger = structlog.get_logger(module=__name__)


class UserAllSurveyData(pydantic.BaseModel):
  """A container for all the survey data for a user"""
  personal_health: models.UserSurveyResponsePersonalHealth | None
  reproductive_health: models.UserSurveyResponseReproductiveHealth | None
  lifestyle: models.UserSurveyResponseLifestyle | None


class UserScreeningIndication(enum.StrEnum):
  """Indications are criteria such as age that determine whether a screening template should be applied to the user"""
  AGE = "age"
  SEX_ASSIGNED_AT_BIRTH = "sex_assigned_at_birth"
  COUNTRY = "country"
  RISKFACTOR = "risk_factor"
  BMI = "body_mass_index"
  ETHNICITY = "ethnicity"


class UserScreeningComparison(enum.StrEnum):
  """Comparison operators for the indicators"""
  BETWEEN = "between"
  EQUALS = "equals"
  IN = "in"
  ABOVE = "above"
  BELOW = "below"


class UserScreeningIndicator(pydantic.BaseModel):
  """A screening template has one or more indicators that determine whether the template should be applied to the user"""
  indication: UserScreeningIndication
  comparison: UserScreeningComparison
  lower_bound: int | None = None
  upper_bound: int | None = None
  value: models.UserSexAssignedAtBirth | models.UserRiskFactors | float | list[models.UserCountryOfResidence | models.UserEthnicity] | None = None


class UserScreeningTemplate(pydantic.BaseModel):
  """A screening template has a set of indicators (see [UserScreeningIndicator]) and template data for the created screening object.
  All the indicators must be matched for the template to be applied.
  If months_between_appointments is None, the screening is a once-off event.
  """
  type: models.UserScreeningType
  subtype: str | None = None
  months_between_appointments: int = 12
  indicators: list[UserScreeningIndicator]
  alert: bool = True
  notes: str = "This was created based on the standard NHS screening schedule based on the information you have provided. You can edit this entry to set the actual date of your next screening if you know it. If you don't know, ask your healthcare provider at your next appointment."


async def get_all_survey_data(store: Store, sub: str) -> UserAllSurveyData:
  """Returns all the survey data for the user."""
  personal_health = await store.get_user_survey_response_personal_health(sub=sub)
  reproductive_health = await store.get_user_survey_response_reproductive_health(sub=sub)
  lifestyle = await store.get_user_survey_response_lifestyle(sub=sub)
  return UserAllSurveyData(
    personal_health=personal_health,
    reproductive_health=reproductive_health,
    lifestyle=lifestyle,
  )


async def get_all_health_events(store: Store, sub: str) -> list[models.UserHealthEvent]:
  """Returns all the health events for the user."""
  result = store.list_user_health_events(sub=sub)
  return [row async for row in result]


async def get_all_shared_health_events(store: Store, sub: str) -> list[models.UserHealthEvent]:
  """Returns all the shared health events for the user."""
  result = store.list_shared_health_events(sub=sub)
  return [models.UserHealthEvent(
    event_id=row.event_id,
    user_id=row.user_id,
    type=row.type,
    details=row.details,
    notes=row.notes,
    start_date=row.start_date,
    end_date=row.end_date,
    ongoing=row.ongoing,
    genetic=row.genetic,
    is_reviewed=row.is_reviewed,
    created_date=row.created_date,
    is_deleted=row.is_deleted,
    deleted_date=row.deleted_date,
  ) async for row in result]


async def get_all_screenings_and_screening_alerts(store: Store, sub: str) -> list[models.UserScreening | models.UserScreeningAlert]:
  """Returns all the screenings and screening alerts for the user."""
  _screenings = store.list_user_screenings(sub=sub)
  screenings = [row async for row in _screenings]
  _screening_alerts = store.list_user_screening_alerts(sub=sub)
  screening_alerts = [row async for row in _screening_alerts]
  return screenings + screening_alerts


async def create_user_screening_schedule_by_id(store: Store, user_id: str) -> None:
  """Creates a user screening based on standard templates from SCREENING_TEMPLATE and what we know about the user.
  This function is safe to call multiple times, as it will only create new screenings if they do not already exist.
  """
  user = await store.get_user_by_id(user_id=user_id)
  assert isinstance(user, models.User)
  await create_user_screening_schedule(store=store, user=user)


async def create_user_screening_schedule_by_sub(store: Store, sub: str) -> None:
  """Creates a user screening based on standard templates from SCREENING_TEMPLATE and what we know about the user.
  This function is safe to call multiple times, as it will only create new screenings if they do not already exist.
  """
  user = await store.get_user_by_sub(sub=sub)
  assert isinstance(user, models.User)
  await create_user_screening_schedule(store=store, user=user)


async def create_user_screening_schedule(store: Store, user: models.User) -> None:
  """Creates a user screening based on standard templates from SCREENING_TEMPLATE and what we know about the user.
  This function is safe to call multiple times, as it will only create new screenings if they do not already exist.
  """
  survey_data = await get_all_survey_data(store=store, sub=user.sub)
  health_events = await get_all_health_events(store=store, sub=user.sub)
  shared_health_events = await get_all_shared_health_events(store=store, sub=user.sub)
  existing_screenings = await get_all_screenings_and_screening_alerts(store=store, sub=user.sub)
  for template in SCREENING_TEMPLATE:
    logger.debug(f"checking template {template.type} ({template.subtype})")
    match = True
    for indicator in template.indicators:
      if not await user_matches_indicator(user=user, indicator=indicator, survey_data=survey_data, health_events=health_events, shared_health_events=shared_health_events):
        match = False
        logger.debug(f"mismatch on indicator {indicator.indication}")
        break
    if not match:
      continue
    if not matching_screening_in_list(screening=template, existing_screenings=existing_screenings):
      if template.alert:
        new_screening_alert = await store.create_user_screening_alert(queries.CreateUserScreeningAlertParams(
          sub=user.sub,
          type=template.type,
          subtype=template.subtype,
          next_date=date.today() + timedelta(weeks=26),
          suggested_months_between_appointments=template.months_between_appointments,
          notes=template.notes,
        ))
        assert isinstance(new_screening_alert, models.UserScreeningAlert)
        existing_screenings.append(new_screening_alert)
        logger.debug(f"created screening alert {new_screening_alert.alert_id} based on template {template.type} ({template.subtype})")
      else:
        new_screening = await store.create_user_screening(queries.CreateUserScreeningParams(
          sub=user.sub,
          type=template.type,
          subtype=template.subtype,
          next_date=date.today() + timedelta(weeks=26),
          months_between_appointments=template.months_between_appointments,
          status=models.UserScreeningStatus.NOTYETATTENDED,
          user_managed_schedule=False,
          notes=template.notes,
        ))
        assert isinstance(new_screening, models.UserScreening)
        existing_screenings.append(new_screening)
        logger.debug(f"created screening {new_screening.screening_id} based on template {template.type} ({template.subtype})")
    else:
      logger.debug("user already has a matching screening")


def get_user_age(user: models.User) -> int:
  """Returns the age of the user in years"""
  today = date.today()
  return today.year - user.birth_year - (today.month < user.birth_month)  # this relies on the fact that n - True == n - 1


def matching_diagnosis_in_health_events(health_events: list[models.UserHealthEvent], diagnoses: list[str]) -> bool:
  for health_event in health_events:
    if health_event.type == models.UserHealthEventType.DIAGNOSIS:
      for diagnosis in diagnoses:
        if health_event.details.lower() == diagnosis.lower():
          return True
  return False


def get_user_bmi(survey_data: UserAllSurveyData) -> float | None:
  if (survey_data.lifestyle is None
      or survey_data.lifestyle.height is None
      or survey_data.lifestyle.weight is None
      or survey_data.lifestyle.height_weight_unit_type is None):
    return None
  match survey_data.lifestyle.height_weight_unit_type:
    case models.HeightWeightUnitType.IMPERIAL:
      height_in_metres = survey_data.lifestyle.height / 39.37  # inches to metres
      weight_in_kilograms = survey_data.lifestyle.weight / 2.205  # pounds to kilograms
    case models.HeightWeightUnitType.METRIC:
      height_in_metres = survey_data.lifestyle.height / 100  # centimetres to metres
      weight_in_kilograms = survey_data.lifestyle.weight
    case _:
      raise ValueError("unknown height/weight unit type")
  return weight_in_kilograms / (height_in_metres * height_in_metres)


async def user_matches_indicator(
  user: models.User,
  indicator: UserScreeningIndicator,
  survey_data: UserAllSurveyData,
  health_events: list[models.UserHealthEvent],
  shared_health_events: list[models.UserHealthEvent],
) -> bool:
  """Returns True if the user matches the indicator, or False if they do not or if the required data is not available"""
  # First confirm we have the required data
  match indicator.indication:
    case UserScreeningIndication.AGE:
      user_age = get_user_age(user=user)
      match indicator.comparison:
        case UserScreeningComparison.BETWEEN:
          if indicator.lower_bound is None or indicator.upper_bound is None:
            raise ValueError(f"comparison operator {indicator.comparison} requires lower_bound and upper_bound")
          if user_age < indicator.lower_bound or user_age > indicator.upper_bound:
            return False
          return True
        case UserScreeningComparison.EQUALS:
          return user_age == indicator.value
        case _:
          raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication}")
    case UserScreeningIndication.SEX_ASSIGNED_AT_BIRTH:
      match indicator.comparison:
        case UserScreeningComparison.EQUALS:
          assert isinstance(indicator.value, models.UserSexAssignedAtBirth)
          return user.sex_assigned_at_birth == indicator.value
        case _:
          raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication}")
    case UserScreeningIndication.COUNTRY:
      if survey_data.personal_health is None or survey_data.personal_health.country is None:
        return False
      user_country = models.UserCountryOfResidence(survey_data.personal_health.country)  # TODO use the enum in the survey schema
      match indicator.comparison:
        case UserScreeningComparison.EQUALS:
          assert isinstance(indicator.value, models.UserCountryOfResidence)
          return user_country == indicator.value
        case UserScreeningComparison.IN:
          if indicator.value is None or not isinstance(indicator.value, list):
            raise ValueError(f"comparison operator {indicator.comparison} requires a list")
          return user_country in indicator.value
        case _:
          raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication}")
    case UserScreeningIndication.RISKFACTOR:
      match indicator.value:
        case models.UserRiskFactors.DIABETESDIAGNOSIS:
          if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Diabetes", "Type 1 Diabetes", "Type 2 Diabetes"]):  # TODO use a more robust comparison
            return True
          return False
        case models.UserRiskFactors.HISTORYOFSMOKING:
          if survey_data.lifestyle is None or survey_data.lifestyle.nicotine_use is None:
            return False
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              logger.info("assuming that nicotine use implies a history of smoking")
              return survey_data.lifestyle.nicotine_use != ""
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.FAMILYHISTORYOFTYPE2DIABETES:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=shared_health_events, diagnoses=["Type 2 Diabetes"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
          return False
        case models.UserRiskFactors.HISTORYOFGESTATIONALDIABETES:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Gestational Diabetes"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.HYPERTENSION:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Hypertension", "High Blood Pressure"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.PCOS:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["PCOS", "Polycystic Ovary Syndrome", "Poly-cystic Ovary Syndrome"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.PREDIABETES:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Prediabetes", "Pre-diabetes"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.HISTORYOFHEARTDISEASE:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Heart Attack"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case models.UserRiskFactors.HISTORYOFSTROKE:
          match indicator.comparison:
            case UserScreeningComparison.EQUALS:
              if matching_diagnosis_in_health_events(health_events=health_events, diagnoses=["Stroke"]):  # TODO use a more robust comparison
                return True
              return False
            case _:
              raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication} {indicator.value}")
        case _:
          raise ValueError(f"unknown user risk factor: {indicator.value}")
    case UserScreeningIndication.BMI:
      user_bmi = get_user_bmi(survey_data=survey_data)
      if user_bmi is None:
        return False
      match indicator.comparison:
        case UserScreeningComparison.ABOVE:
          assert isinstance(indicator.value, float)
          return user_bmi > indicator.value
        case _:
          raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication}")
    case UserScreeningIndication.ETHNICITY:
      if survey_data.personal_health is None or survey_data.personal_health.ethnicity is None:
        return False
      user_ethnicity = models.UserEthnicity(survey_data.personal_health.ethnicity)  # TODO use the enum in the survey schema
      match indicator.comparison:
        case UserScreeningComparison.EQUALS:
          assert isinstance(indicator.value, models.UserEthnicity)
          return user_ethnicity == indicator.value
        case UserScreeningComparison.IN:
          if indicator.value is None or not isinstance(indicator.value, list):
            raise ValueError(f"comparison operator {indicator.comparison} requires a list")
          return user_ethnicity in indicator.value
        case _:
          raise ValueError(f"comparison operator {indicator.comparison} not implemented for {indicator.indication}")
    case _:
      raise ValueError(f"unknown user screening indication: {indicator.indication}")


def matching_screening_in_list(screening: UserScreeningTemplate, existing_screenings: list[models.UserScreening | models.UserScreeningAlert]) -> bool:
  """Returns True if the screening type and subtype match any of the screenings in the list, or False if they do not"""
  for existing_screening in existing_screenings:
    if existing_screening.type == screening.type:
      if existing_screening.subtype == screening.subtype:
        return True
  return False


SCREENING_TEMPLATE = [
  UserScreeningTemplate(
    type=models.UserScreeningType.CERVICALSMEAR,
    months_between_appointments=36,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=25,
        upper_bound=49,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.SEX_ASSIGNED_AT_BIRTH,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserSexAssignedAtBirth.FEMALE,
      ),
    ],
    notes="The NHS recommends that people with a cervix aged 25-49 have a cervical screening every 3 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.CERVICALSMEAR,
    months_between_appointments=60,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=50,
        upper_bound=64,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.SEX_ASSIGNED_AT_BIRTH,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserSexAssignedAtBirth.FEMALE
      ),
    ],
    notes="The NHS recommends that people with a cervix aged 50-64 have a cervical screening every 5 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.MAMMOGRAM,
    months_between_appointments=36,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=50,
        upper_bound=70,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.SEX_ASSIGNED_AT_BIRTH,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserSexAssignedAtBirth.FEMALE
      ),
    ],
    notes="The NHS recommends that women aged 50-70 have a mammogram every 3 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BOWELSCREENING,
    months_between_appointments=24,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=50,
        upper_bound=74,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.COUNTRY,
        comparison=UserScreeningComparison.IN,
        value=[
          models.UserCountryOfResidence.SCOTLAND,
        ],
      ),
    ],
    notes="NHS Scotland recommends that people aged 50-74 have a bowel screening every 2 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BOWELSCREENING,
    months_between_appointments=24,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=60,
        upper_bound=74,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.COUNTRY,
        comparison=UserScreeningComparison.IN,
        value=[
          models.UserCountryOfResidence.ENGLAND,
          models.UserCountryOfResidence.WALES,
          models.UserCountryOfResidence.NORTHERNIRELAND,
        ],
      ),
    ],
    notes="The NHS recommends that people aged 60-74 have a bowel screening every 2 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.ABDOMINALAORTICANEURYSMSCREENING,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.EQUALS,
        value=65,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.SEX_ASSIGNED_AT_BIRTH,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserSexAssignedAtBirth.MALE,
      ),
    ],
    notes="The NHS recommends that men aged 65 have a one-off abdominal aortic aneurysm screening.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.DIABETICEYESCREENING,
    months_between_appointments=24,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.DIABETESDIAGNOSIS,
      ),
    ],
    notes="The NHS recommends that people with diabetes are screened for diabetic retinopathy every 2 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.ROUTINEHEALTHCHECK,
    months_between_appointments=60,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=40,
        upper_bound=74,
      ),
    ],
    notes="The NHS recommends that people aged 40-74 have a routine NHS health check every 5 years.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.LUNGCANCERSCREENING,
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.AGE,
        comparison=UserScreeningComparison.BETWEEN,
        lower_bound=55,
        upper_bound=74,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.HISTORYOFSMOKING
      ),
    ],
    notes="The NHS recommends that people aged 55-74 who have a history of smoking are screened for lung cancer.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.BMI,
        comparison=UserScreeningComparison.ABOVE,
        value=30.0,
      ),
    ],
    notes="Based on your survey answers, you may wish to speak to your healthcare provider about having a bloodtest for HbA1c. a blood test that provides an estimate of your average blood sugar levels over the past 2-3 months. It's a crucial tool for diagnosing and monitoring diabetes. It is offered to those at higher risk of developing diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.BMI,
        comparison=UserScreeningComparison.ABOVE,
        value=27.5,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.ETHNICITY,
        comparison=UserScreeningComparison.IN,
        value=[
          models.UserEthnicity.ASIANORASIANBRITISH_BANGLADESHI,
          models.UserEthnicity.ASIANORASIANBRITISH_PAKISTANI,
          models.UserEthnicity.ASIANORASIANBRITISH_INDIAN,
          models.UserEthnicity.ASIANORASIANBRITISH_CHINESE,
          models.UserEthnicity.ANYOTHERASIANBACKGROUND,
          models.UserEthnicity.BLACKBLACKBRITISHCARIBBEANORAFRICAN_CARIBBEAN,
          models.UserEthnicity.BLACKBLACKBRITISHCARIBBEANORAFRICAN_AFRICAN,
          models.UserEthnicity.ANYOTHERBLACKBLACKBRITISHORCARIBBEANBACKGROUND,
        ],
      ),
    ],
    notes="Based on your survey answers, you may wish to speak to your healthcare provider about having a bloodtest for HbA1c. a blood test that provides an estimate of your average blood sugar levels over the past 2-3 months. It's a crucial tool for diagnosing and monitoring diabetes. It is offered to those at higher risk of developing diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.FAMILYHISTORYOFTYPE2DIABETES,
      ),
    ],
    notes="The NHS recommends that people with a strong family history of type 2 diabetes have a HbA1c blood test to check for signs of diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.HISTORYOFGESTATIONALDIABETES,
      ),
    ],
    notes="The NHS recommends that people with a history of gestational diabetes have a HbA1c blood test to check for signs of diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.HYPERTENSION,
      ),
    ],
    notes="Based on your survey answers, you may wish to speak to your healthcare provider about having a bloodtest for HbA1c. a blood test that provides an estimate of your average blood sugar levels over the past 2-3 months. It's a crucial tool for diagnosing and monitoring diabetes. It is offered to those at higher risk of developing diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.PCOS,
      ),
      UserScreeningIndicator(
        indication=UserScreeningIndication.BMI,
        comparison=UserScreeningComparison.ABOVE,
        value=25.0,
      ),
    ],
    notes="Based on your survey answers, you may wish to speak to your healthcare provider about having a bloodtest for HbA1c. a blood test that provides an estimate of your average blood sugar levels over the past 2-3 months. It's a crucial tool for diagnosing and monitoring diabetes. It is offered to those at higher risk of developing diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.PREDIABETES,
      ),
    ],
    notes="The NHS recommends that people with prediabetes have a HbA1c blood test to check for signs of diabetes. Discuss with your healthcare provider whether this should be something you have at regular intervals to assess whether your status has changed.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.HISTORYOFHEARTDISEASE,
      ),
    ],
    notes="The NHS recommends that people with a history of heart disease have a HbA1c blood test to check for signs of diabetes.",
  ),
  UserScreeningTemplate(
    type=models.UserScreeningType.BLOODTEST,
    subtype="HbA1c",
    indicators=[
      UserScreeningIndicator(
        indication=UserScreeningIndication.RISKFACTOR,
        comparison=UserScreeningComparison.EQUALS,
        value=models.UserRiskFactors.HISTORYOFSTROKE,
      ),
    ],
    notes="The NHS recommends that people with a history of stroke have a HbA1c blood test to check for signs of diabetes.",
  ),
]
