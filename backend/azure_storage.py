from azure.storage.blob import Container<PERSON>lient as AzureContainerClient
from azure.storage.queue import QueueClient as AzureQueueClient, TextBase64EncodePolicy
from fastapi import Depends
from typing import Annotated, Any
from collections.abc import AsyncGenerator
import pydantic
from backend.config import AZURE_STORAGE_ACCOUNT_ACCESS_KEY, AZURE_STORAGE_ACCOUNT_NAME, AZURE_STORAGE_CONTAINER_USERDOCS_NAME, AZURE_STORAGE_QUEUE_USERDOCS_NAME, AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME, AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME


class AzureContainerParams(pydantic.BaseModel):
  account_name: str
  account_key: str
  container_name: str


async def get_userdocs_container_params() -> AsyncGenerator[Any, Any]:
  params = AzureContainerParams(
    account_name=AZURE_STORAGE_ACCOUNT_NAME,
    account_key=AZURE_STORAGE_ACCOUNT_ACCESS_KEY,
    container_name=AZURE_STORAGE_CONTAINER_USERDOCS_NAME
  )
  yield params


async def get_userdocs_container_client() -> AsyncGenerator[Any, Any]:
  client = AzureContainerClient(
    account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
    container_name=AZURE_STORAGE_CONTAINER_USERDOCS_NAME,
    credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY
  )
  yield client


async def get_userdocs_queue_client() -> AsyncGenerator[Any, Any]:
  client = AzureQueueClient(
    account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.queue.core.windows.net",
    queue_name=AZURE_STORAGE_QUEUE_USERDOCS_NAME,
    credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY,
    message_encode_policy=TextBase64EncodePolicy()
  )
  yield client


async def get_instagramcheck_queue_client() -> AsyncGenerator[Any, Any]:
  client = AzureQueueClient(
    account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.queue.core.windows.net",
    queue_name=AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME,
    credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY,
    message_encode_policy=TextBase64EncodePolicy()
  )
  yield client


async def get_usersharechange_queue_client() -> AsyncGenerator[Any, Any]:
  client = AzureQueueClient(
    account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.queue.core.windows.net",
    queue_name=AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME,
    credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY,
    message_encode_policy=TextBase64EncodePolicy()
  )
  yield client


UserdocsContainerParams = Annotated[AzureContainerParams, Depends(get_userdocs_container_params)]
UserdocsContainerClient = Annotated[AzureContainerClient, Depends(get_userdocs_container_client)]
UserdocsQueueClient = Annotated[AzureQueueClient, Depends(get_userdocs_queue_client)]
InstagramcheckQueueClient = Annotated[AzureQueueClient, Depends(get_instagramcheck_queue_client)]
UsersharechangeQueueClient = Annotated[AzureQueueClient, Depends(get_usersharechange_queue_client)]
