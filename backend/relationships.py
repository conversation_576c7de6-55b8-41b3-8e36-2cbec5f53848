from backend.repository import models

RELATIONSHIP_MIRROR = {
  models.UserRelationship.MOTHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.DAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.SON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.CHILD,
  },
  models.UserRelationship.FATHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.DAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.SON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.CHILD,
  },
  models.UserRelationship.PARENT: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.DAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.SON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.CHILD,
  },
  models.UserRelationship.GRANDMOTHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDDAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDSON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDCHILD,
  },
  models.UserRelationship.GRANDFATHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDDAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDSON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDCHILD,
  },
  models.UserRelationship.GRANDPARENT: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDDAUGHTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDSON,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDCHILD,
  },
  models.UserRelationship.DAUGHTER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.MOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.FATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENT,
  },
  models.UserRelationship.SON: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.MOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.FATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENT,
  },
  models.UserRelationship.CHILD: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.MOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.FATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENT,
  },
  models.UserRelationship.SISTER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLING,
  },
  models.UserRelationship.BROTHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLING,
  },
  models.UserRelationship.SIBLING: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLING,
  },
  models.UserRelationship.NIECE: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.AUNT,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.UNCLE,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENTSSIBLING,
  },
  models.UserRelationship.NEPHEW: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.AUNT,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.UNCLE,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENTSSIBLING,
  },
  models.UserRelationship.SIBLINGSCHILD: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.AUNT,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.UNCLE,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.PARENTSSIBLING,
  },
  models.UserRelationship.AUNT: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.NIECE,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.NEPHEW,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLINGSCHILD,
  },
  models.UserRelationship.UNCLE: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.NIECE,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.NEPHEW,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLINGSCHILD,
  },
  models.UserRelationship.PARENTSSIBLING: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.NIECE,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.NEPHEW,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.SIBLINGSCHILD,
  },
  models.UserRelationship.GRANDDAUGHTER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDMOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDFATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDPARENT,
  },
  models.UserRelationship.GRANDSON: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDMOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDFATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDPARENT,
  },
  models.UserRelationship.GRANDCHILD: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.GRANDMOTHER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.GRANDFATHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.GRANDPARENT,
  },
  models.UserRelationship.COUSIN: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.COUSIN,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.COUSIN,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.COUSIN,
  },
  models.UserRelationship.HALF_BROTHER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.HALF_SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.HALF_BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.HALF_SIBLING,
  },
  models.UserRelationship.HALF_SISTER: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.HALF_SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.HALF_BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.HALF_SIBLING,
  },
  models.UserRelationship.HALF_SIBLING: {
    models.UserSexAssignedAtBirth.FEMALE: models.UserRelationship.HALF_SISTER,
    models.UserSexAssignedAtBirth.MALE: models.UserRelationship.HALF_BROTHER,
    models.UserSexAssignedAtBirth.INTERSEX: models.UserRelationship.HALF_SIBLING,
  },
}

RELATIONSHIP_DEGREE = {
  models.UserRelationship.MOTHER: "First Degree",
  models.UserRelationship.FATHER: "First Degree",
  models.UserRelationship.GRANDMOTHER: "Second Degree",
  models.UserRelationship.GRANDFATHER: "Second Degree",
  models.UserRelationship.DAUGHTER: "First Degree",
  models.UserRelationship.SON: "First Degree",
  models.UserRelationship.SISTER: "First Degree",
  models.UserRelationship.BROTHER: "First Degree",
  models.UserRelationship.NIECE: "Second Degree",
  models.UserRelationship.NEPHEW: "Second Degree",
  models.UserRelationship.AUNT: "Second Degree",
  models.UserRelationship.UNCLE: "Second Degree",
  models.UserRelationship.GRANDDAUGHTER: "Second Degree",
  models.UserRelationship.GRANDSON: "Second Degree",
  models.UserRelationship.COUSIN: "Third Degree",
  models.UserRelationship.PARENT: "First Degree",
  models.UserRelationship.CHILD: "First Degree",
  models.UserRelationship.SIBLING: "First Degree",
  models.UserRelationship.GRANDPARENT: "Second Degree",
  models.UserRelationship.GRANDCHILD: "Second Degree",
  models.UserRelationship.PARENTSSIBLING: "Second Degree",
  models.UserRelationship.SIBLINGSCHILD: "Second Degree",
  models.UserRelationship.HALF_BROTHER: "Second Degree",
  models.UserRelationship.HALF_SISTER: "Second Degree",
  models.UserRelationship.HALF_SIBLING: "Second Degree",
}
