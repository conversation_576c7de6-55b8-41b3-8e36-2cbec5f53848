import os


def getenv_or_panic(env: str) -> str:
  value = os.getenv(env)
  if value is None:
    raise RuntimeError(f"{env} is required")
  return value


# Required configuration
SECRET_KEY = getenv_or_panic("SECRET_KEY")
DOCS_PASSWORD = getenv_or_panic("DOCS_PASSWORD")
AZURE_OPENAI_API_KEY = getenv_or_panic("AZURE_OPENAI_API_KEY")
AZURE_STORAGE_ACCOUNT_ACCESS_KEY = getenv_or_panic("AZURE_STORAGE_ACCOUNT_ACCESS_KEY")
AZURE_COMMUNICATION_CONNECTION_STRING = getenv_or_panic("AZURE_COMMUNICATION_CONNECTION_STRING")
TOKEN_ISSUER = getenv_or_panic("TOKEN_ISSUER")
TOKEN_AUDIENCE = getenv_or_panic("TOKEN_AUDIENCE")

# Optional configuration
POSTGRES_USERNAME = os.getenv("POSTGRES_USERNAME", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DATABASE = os.getenv("POSTGRES_DATABASE", "postgres")
POSTGRES_SSLMODE = os.getenv("POSTGRES_SSLMODE", "disable")
DOCS_USERNAME = os.getenv("DOCS_USERNAME", "docs")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://inherit-nonproduction-openai.openai.azure.com/")
AZURE_OPENAI_MODEL = os.getenv("AZURE_OPENAI_MODEL", "nonproduction-openai-gpt-4o-mini")
AZURE_STORAGE_ACCOUNT_NAME = os.getenv("AZURE_STORAGE_ACCOUNT_NAME", "inheritdevelopment")
AZURE_STORAGE_CONTAINER_USERDOCS_NAME = os.getenv("AZURE_STORAGE_CONTAINER_USERDOCS_NAME", "userdocs")
AZURE_STORAGE_QUEUE_USERDOCS_NAME = os.getenv("AZURE_STORAGE_QUEUE_USERDOCS_NAME", "userdocs")
AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME = os.getenv("AZURE_STORAGE_QUEUE_INSTAGRAM_CHECK_NAME", "instagramcheck")
AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME = os.getenv("AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME", "usersharechange")
LOG_STYLE = os.getenv("LOG_STYLE", "json")
LOG_LEVEL = os.getenv("LOG_LEVEL", "debug")
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
TOKEN_LIFETIME_SECONDS = int(os.getenv("TOKEN_LIFETIME_SECONDS", "3600"))
TOKEN_LEEWAY_SECONDS = int(os.getenv("TOKEN_LEEWAY_SECONDS", "60"))
TEST_ENVIRONMENT = os.getenv("TEST_ENVIRONMENT", "false") == "true"
