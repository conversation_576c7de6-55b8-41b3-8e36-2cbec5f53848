services:
  backend:
    build:
      context: backend
    pull_policy: build
    ports:
    - '8000:8000'
    environment:
      POSTGRES_HOST: backend-db
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DATABASE: postgres
      POSTGRES_SSLMODE: disable
      SECRET_KEY: foo
      DOCS_USERNAME: docs
      DOCS_PASSWORD: bar
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT:-https://inherit-nonproduction-openai.openai.azure.com/}
      AZURE_OPENAI_MODEL: ${AZURE_OPENAI_MODEL:-nonproduction-openai-gpt-4o-mini}
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY}
      AZURE_STORAGE_ACCOUNT_ACCESS_KEY: ${AZURE_STORAGE_ACCOUNT_ACCESS_KEY}
      AZURE_STORAGE_ACCOUNT_NAME: ${AZURE_STORAGE_ACCOUNT_NAME:-inheritdevelopment}
      AZURE_STORAGE_CONTAINER_USERDOCS_NAME: ${AZURE_STORAGE_CONTAINER_USERDOCS_NAME:-userdocs}
      AZURE_STORAGE_QUEUE_USERDOCS_NAME: ${AZURE_STORAGE_QUEUE_USERDOCS_NAME:-userdocs}
      AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME: ${AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME:-userdocs}
      AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME: ${AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME:-usersharechange}
      AZURE_COMMUNICATION_CONNECTION_STRING: ${AZURE_COMMUNICATION_CONNECTION_STRING:-endpoint=https://fake.azure.com/;accesskey=zaphodbeeblebrox}
      LOG_STYLE: ${LOG_STYLE:-json}
      TOKEN_ISSUER: ${TOKEN_ISSUER}
      TOKEN_AUDIENCE: ${TOKEN_AUDIENCE}
      TEST_ENVIRONMENT: ${TEST_ENVIRONMENT:-false}
