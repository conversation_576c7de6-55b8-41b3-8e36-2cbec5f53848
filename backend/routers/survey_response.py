from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries
from backend.repository import models
from backend.db import Store
from backend.routers.auth import Actor
from datetime import datetime
from backend.util import any_is_not_none
from backend.rewards import award_reward_point, referral_completion_check
from backend.screening_template import create_user_screening_schedule_by_sub
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


# Personal Health Survey

@router.get('/user/{sub}/survey_response/personal_health', response_model=models.UserSurveyResponsePersonalHealth)
async def get_user_survey_response_personal_health(store: Store, actor: Actor, sub: str) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_survey_response_personal_health(sub=sub)
    if result is None:
      user = await store.get_user_by_sub(sub=sub)
      assert isinstance(user, models.User)
      result = models.UserSurveyResponsePersonalHealth(
        response_id="null",
        user_id=user.user_id,
        current_health_condition=False,
        historic_health_condition=False,
        injuries=False,
        allergies=False,
        medications=False,
        created_date=datetime.fromtimestamp(0),
      )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  return result


async def post_update_actions_personal_health(store: Store, sub: str) -> None:
  if await user_survey_response_personal_health_completion(store=store, sub=sub) == 1.0:
    await award_reward_point(store=store, sub=sub, reason=models.UserRewardPointLogReason.PERSONALHEALTHSURVEY)
    await referral_completion_check(store=store, sub=sub)
  await create_user_screening_schedule_by_sub(store=store, sub=sub)


@router.post('/user/{sub}/survey_response/personal_health/1', response_model=models.UserSurveyResponsePersonalHealth)
async def update_user_survey_response_personal_health_one(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyPersonalHealthOneParams) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_personal_health_one(input)
    assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_personal_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/personal_health/2', response_model=models.UserSurveyResponsePersonalHealth)
async def update_user_survey_response_personal_health_two(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyPersonalHealthTwoParams) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_personal_health_two(input)
    assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_personal_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/personal_health/3', response_model=models.UserSurveyResponsePersonalHealth)
async def update_user_survey_response_personal_health_three(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyPersonalHealthThreeParams) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_personal_health_three(input)
    assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_personal_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


class UpdateUserSurveyPersonalHealthFourParams(pydantic.BaseModel):
  sub: str
  family_health_notes: str


@router.post('/user/{sub}/survey_response/personal_health/4', response_model=models.UserSurveyResponsePersonalHealth)
async def update_user_survey_response_personal_health_four(store: Store, actor: Actor, sub: str, input: UpdateUserSurveyPersonalHealthFourParams) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_personal_health_four(sub=input.sub, family_health_notes=input.family_health_notes)
    assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_personal_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/personal_health/5', response_model=models.UserSurveyResponsePersonalHealth)
async def update_user_survey_response_personal_health_five(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyPersonalHealthFiveParams) -> models.UserSurveyResponsePersonalHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_personal_health_five(input)
    assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_personal_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


async def user_survey_response_personal_health_completion(store: Store, sub: str) -> float:
  result = await store.get_user_survey_response_personal_health(sub=sub)
  if result is None:
    return 0.0
  assert isinstance(result, models.UserSurveyResponsePersonalHealth)
  completion = 0.0
  # part 1: Basic Information
  if any_is_not_none(
    result.gp_postcode,
    result.ethnicity,
    result.gender,
    result.country,
  ):
    completion += 0.2
  # part 2: Health Events
  if result.current_health_condition:
    completion += 0.1
  if result.historic_health_condition:
    completion += 0.1
  if result.injuries:
    completion += 0.1
  if result.allergies:
    completion += 0.1
  if result.medications:
    completion += 0.1
  # part 3: Vaccinations
  if any_is_not_none(
    result.routine_vaccines,
    result.vaccines,
    result.childhood_vaccinations_status,
    result.additional_vaccine_notes,
  ):
    completion += 0.3
  # part 4: Family - no points
  # part 5: Disability - no points
  return completion


# Reproductive Health Survey

@router.get('/user/{sub}/survey_response/reproductive_health', response_model=models.UserSurveyResponseReproductiveHealth)
async def get_user_survey_response_reproductive_health(store: Store, actor: Actor, sub: str) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_survey_response_reproductive_health(sub=sub)
    if result is None:
      user = await store.get_user_by_sub(sub=sub)
      assert isinstance(user, models.User)
      result = models.UserSurveyResponseReproductiveHealth(
        response_id="null",
        user_id=user.user_id,
        created_date=datetime.fromtimestamp(0),
      )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


async def post_update_actions_reproductive_health(store: Store, sub: str) -> None:
  if await user_survey_response_reproductive_health_completion(store=store, sub=sub) == 1.0:
    await award_reward_point(store=store, sub=sub, reason=models.UserRewardPointLogReason.REPRODUCTIVEHEALTHSURVEY)
    await referral_completion_check(store=store, sub=sub)
  await create_user_screening_schedule_by_sub(store=store, sub=sub)


@router.post('/user/{sub}/survey_response/reproductive_health/1', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_one(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthOneParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_one(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/reproductive_health/2', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_two(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthTwoParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_two(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/reproductive_health/3', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_three(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthThreeParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_three(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/reproductive_health/4', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_four(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthFourParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_four(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/reproductive_health/5', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_five(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthFiveParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_five(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/reproductive_health/6', response_model=models.UserSurveyResponseReproductiveHealth)
async def update_user_survey_response_reproductive_health_six(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyReproductiveHealthSixParams) -> models.UserSurveyResponseReproductiveHealth:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_reproductive_health_six(input)
    assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_reproductive_health(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


async def user_survey_response_reproductive_health_completion(store: Store, sub: str) -> float:
  result = await store.get_user_survey_response_reproductive_health(sub=sub)
  if result is None:
    return 0.0
  assert isinstance(result, models.UserSurveyResponseReproductiveHealth)
  completion = 0.0
  if any_is_not_none(
    result.reproductive_organs,
    result.reproductive_organ_details,
    result.reproductive_surgeries,
    result.surgery_other_details,
  ):
    completion += 0.1  # part 1: Reproductive Anatomy
  if any_is_not_none(
    result.ever_menstruated,
    result.menarche_age,
    result.menstruated_last_12_months,
    result.last_period_date,
    result.cycle_length,
    result.menstrual_symptoms,
    result.menstrual_symptoms_other,
  ):
    completion += 0.2  # part 2: Menstrual & Hormonal Health
  if any_is_not_none(
    result.currently_using_contraception,
    result.current_contraception_details,
    result.ever_used_contraception,
    result.past_contraception_details,
    result.currently_using_hrt,
    result.current_hrt_details,
    result.menstrual_status_at_hrt_start,
  ):
    completion += 0.1  # part 3: Hormonal Contraception & HRT
  if any_is_not_none(
    result.currently_pregnant,
    result.pregnancies_total,
    result.pregnancies_live_births,
    result.pregnancies_stillbirths,
    result.pregnancies_ectopics,
    result.pregnancies_miscarriages,
    result.pregnancies_terminations,
    result.currently_breastfeeding,
    result.tried_to_conceive_12_months,
    result.fertility_testing,
    result.fertility_testing_details,
  ):
    completion += 0.1  # part 4: Pregnancy & Birth History
  if any_is_not_none(
    result.diagnosed_conditions,
    result.other_conditions_details,
    result.pcos_screener_irregular_periods,
    result.pcos_screener_excessive_hair_growth,
    result.pcos_screener_overweight_16_40,
    result.pcos_screener_nipple_discharge,
    result.endopain_period_pain,
    result.endopain_pain_between_periods,
    result.endopain_worsening_pain,
    result.endopain_prolonged_period_pain,
    result.endopain_stabbing_pain,
    result.endopain_radiating_back_pain,
    result.endopain_hip_or_leg_pain,
    result.endopain_limits_daily_activities,
    result.endopain_disabling_pain,
    result.endopain_sexual_severe_pain,
    result.endopain_sexual_position_specific_pain,
    result.endopain_sexual_interrupts_sex,
    result.endopain_bowel_and_bladder_pain_bowel_movements,
    result.endopain_bowel_and_bladder_diarrhoea_constipation,
    result.endopain_bowel_and_bladder_bowel_cramps,
    result.endopain_bowel_and_bladder_urination_pain,
    result.endopain_bowel_and_bladder_bladder_discomfort,
  ):
    completion += 0.3  # part 5: Gynaecological Conditions
  if any_is_not_none(
    result.cycles_irregular_past_12_months,
    result.symptoms,
    result.menopause_status,
  ):
    completion += 0.2  # part 6: Peri & Menopause
  return completion


# Lifestyle Survey

@router.get('/user/{sub}/survey_response/lifestyle', response_model=models.UserSurveyResponseLifestyle)
async def get_user_survey_response_lifestyle(store: Store, actor: Actor, sub: str) -> models.UserSurveyResponseLifestyle:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_survey_response_lifestyle(sub=sub)
    if result is None:
      user = await store.get_user_by_sub(sub=sub)
      assert isinstance(user, models.User)
      result = models.UserSurveyResponseLifestyle(
        response_id="null",
        user_id=user.user_id,
        created_date=datetime.fromtimestamp(0),
      )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


async def post_update_actions_lifestyle(store: Store, sub: str) -> None:
  if await user_survey_response_lifestyle_completion(store=store, sub=sub) == 1.0:
    await award_reward_point(store=store, sub=sub, reason=models.UserRewardPointLogReason.LIFESTYLESURVEY)
    await referral_completion_check(store=store, sub=sub)
  await create_user_screening_schedule_by_sub(store=store, sub=sub)


@router.post('/user/{sub}/survey_response/lifestyle/1', response_model=models.UserSurveyResponseLifestyle)
async def update_user_survey_response_lifestyle_one(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyLifestyleOneParams) -> models.UserSurveyResponseLifestyle:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_lifestyle_one(input)
    assert isinstance(result, models.UserSurveyResponseLifestyle)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_lifestyle(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/lifestyle/2', response_model=models.UserSurveyResponseLifestyle)
async def update_user_survey_response_lifestyle_two(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyLifestyleTwoParams) -> models.UserSurveyResponseLifestyle:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_lifestyle_two(input)
    assert isinstance(result, models.UserSurveyResponseLifestyle)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_lifestyle(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/lifestyle/3', response_model=models.UserSurveyResponseLifestyle)
async def update_user_survey_response_lifestyle_three(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyLifestyleThreeParams) -> models.UserSurveyResponseLifestyle:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_lifestyle_three(input)
    assert isinstance(result, models.UserSurveyResponseLifestyle)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_lifestyle(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.post('/user/{sub}/survey_response/lifestyle/4', response_model=models.UserSurveyResponseLifestyle)
async def update_user_survey_response_lifestyle_four(store: Store, actor: Actor, sub: str, input: queries.UpdateUserSurveyLifestyleFourParams) -> models.UserSurveyResponseLifestyle:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_survey_lifestyle_four(input)
    assert isinstance(result, models.UserSurveyResponseLifestyle)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await post_update_actions_lifestyle(store=store, sub=sub)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


async def user_survey_response_lifestyle_completion(store: Store, sub: str) -> float:
  result = await store.get_user_survey_response_lifestyle(sub=sub)
  if result is None:
    return 0.0
  assert isinstance(result, models.UserSurveyResponseLifestyle)
  completion = 0.0
  if any_is_not_none(
    result.general_health,
    result.health_vs_last_year,
    result.height,
    result.weight,
    result.height_weight_unit_type,
  ):
    completion += 0.2  # part 1: General Health
  if any_is_not_none(
    result.daily_routine_activity,
    result.strength_training,
    result.cardio_exercise,
    result.brisk_walking,
    result.hours_sitting_per_day,
  ):
    completion += 0.3  # part 2: Exercise
  if any_is_not_none(
    result.special_diet,
    result.special_diet_other,
    result.regular_diet_quality,
    result.supplements,
    result.supplements_details,
  ):
    completion += 0.2  # part 3: Nutrition
  if any_is_not_none(
    result.sleep_hours,
    result.stress_level,
    result.alcohol_frequency,
    result.nicotine_use,
    result.nicotine_details,
    result.mindfulness_practice,
  ):
    completion += 0.3  # part 4: Lifestyle & Wellbeing
  return completion
