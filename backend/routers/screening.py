from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries
from backend.repository import models
from backend.db import Store
from backend.routers.auth import Actor
from backend.util import nullable_date, nullable_str, nullable_int
import pydantic
import datetime
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class UserScreening(models.UserScreening):  # used instead of models.UserScreening because we will generate label in-house
  label: str


def extend_user_screening(input: models.UserScreening) -> UserScreening:
  """Converts models.UserScreening to UserScreening by adding the label member"""
  return UserScreening(
    label=input.type if input.subtype is None or input.subtype == "" else f"{input.type} ({input.subtype})",
    **input.model_dump(),
  )


@router.get('/user/{sub}/screening', response_model=list[UserScreening])
async def list_user_screenings(store: Store, actor: Actor, sub: str) -> list[UserScreening]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_user_screenings(sub=sub)
    return [extend_user_screening(row) async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/screening/{screening_id}', response_model=UserScreening)
async def get_user_screening(store: Store, actor: Actor, sub: str, screening_id: str) -> UserScreening:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_screening(sub=sub, screening_id=screening_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return extend_user_screening(result)


class CreateUserScreeningParams(pydantic.BaseModel):  # used instead of queries.CreateUserScreeningParams because of date format issues
  sub: str
  type: models.UserScreeningType
  subtype: str | None = None
  next_date: datetime.date | str | None = None
  last_date: datetime.date | str | None = None
  attended_date: datetime.date | str | None = None
  months_between_appointments: int | None = None
  notes: str | None = None
  status: models.UserScreeningStatus
  user_managed_schedule: bool


@router.post('/user/{sub}/screening', response_model=UserScreening)
async def create_user_screening(store: Store, actor: Actor, sub: str, input: CreateUserScreeningParams) -> UserScreening:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    # If the user is creating a NOTYETATTENDED screening, set any existing NOTYETATTENDED screenings to ATTENDED
    # with attended_date set to the last_date of the new screening
    if input.status == models.UserScreeningStatus.NOTYETATTENDED:
      matching_screenings_ = store.list_matching_screenings(queries.ListMatchingScreeningsParams(
        sub=sub,
        type=input.type,
        subtype=nullable_str(input.subtype, nulls=["None"]),
        status=models.UserScreeningStatus.NOTYETATTENDED,
      ))
      matching_screenings = [row async for row in matching_screenings_]
      if len(matching_screenings) > 0:
        await store.update_user_screening(queries.UpdateUserScreeningParams(
          sub=sub,
          screening_id=matching_screenings[0].screening_id,
          next_date=None,  # unset next_date
          last_date=matching_screenings[0].last_date,  # no change
          attended_date=nullable_date(input.last_date),  # set attended_date to last_date of new screening
          months_between_appointments=matching_screenings[0].months_between_appointments,  # no change
          notes=matching_screenings[0].notes,  # no change
          status=models.UserScreeningStatus.ATTENDED,  # set status to ATTENDED
          user_managed_schedule=matching_screenings[0].user_managed_schedule,  # no change
        ))
    result = await store.create_user_screening(queries.CreateUserScreeningParams(
      sub=sub,
      type=input.type,
      subtype=nullable_str(input.subtype),
      next_date=nullable_date(input.next_date, "next_date"),
      last_date=nullable_date(input.last_date, "last_date"),
      attended_date=nullable_date(input.attended_date, "attended_date"),
      months_between_appointments=nullable_int(input.months_between_appointments),
      notes=nullable_str(input.notes),
      status=input.status,
      user_managed_schedule=input.user_managed_schedule
    ))
    assert isinstance(result, models.UserScreening)
    return extend_user_screening(result)
  except ValueError as err:
    logger.warning(f"action blocked due to invalid input: {err}")
    raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(err.args[0]))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateUserScreeningParams(pydantic.BaseModel):  # used instead of queries.UpdateUserScreeningParams due to date format issues
  sub: str
  screening_id: str
  next_date: datetime.date | str | None = None
  last_date: datetime.date | str | None = None
  attended_date: datetime.date | str | None = None
  months_between_appointments: int | None = None
  notes: str | None = None
  status: models.UserScreeningStatus
  user_managed_schedule: bool


@router.patch('/user/{sub}/screening/{screening_id}', response_model=UserScreening)
async def update_user_screening(store: Store, actor: Actor, sub: str, screening_id: str, input: UpdateUserScreeningParams) -> UserScreening:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  if screening_id != input.screening_id:
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE, detail=f"screening_id in query parameters (${screening_id}) differs from screening_id in body (${input.screening_id})")
  try:
    result = await store.update_user_screening(queries.UpdateUserScreeningParams(
      sub=sub,
      screening_id=screening_id,
      next_date=nullable_date(input.next_date, "next_date"),
      last_date=nullable_date(input.last_date, "last_date"),
      attended_date=nullable_date(input.attended_date, "attended_date"),
      months_between_appointments=nullable_int(input.months_between_appointments),
      notes=nullable_str(input.notes),
      status=input.status,
      user_managed_schedule=input.user_managed_schedule
    ))
  except ValueError as err:
    logger.warning(f"action blocked due to invalid input: {err}")
    raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(err.args[0]))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return extend_user_screening(result)


class UpdateUserScreeningAttendedParams(pydantic.BaseModel):  # used instead of queries.UpdateUserScreeningAttendedParams because we well set status in-house
  sub: str
  screening_id: str
  attended_date: datetime.date
  notes: str | None = None


@router.post('/user/{sub}/screening/{screening_id}/attended', response_model=UserScreening)
async def update_user_screening_attended(store: Store, actor: Actor, sub: str, screening_id: str, input: UpdateUserScreeningAttendedParams) -> UserScreening:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  if screening_id != input.screening_id:
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE, detail=f"screening_id in query parameters (${screening_id}) differs from screening_id in body (${input.screening_id})")
  try:
    result = await store.update_user_screening_attended(queries.UpdateUserScreeningAttendedParams(
      sub=sub,
      screening_id=screening_id,
      attended_date=input.attended_date,
      notes=nullable_str(input.notes),
      status=models.UserScreeningStatus.ATTENDED,
    ))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return extend_user_screening(result)


@router.delete('/user/{sub}/screening/{screening_id}', response_model=UserScreening)
async def delete_user_screening(store: Store, actor: Actor, sub: str, screening_id: str) -> UserScreening:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.delete_user_screening(sub=sub, screening_id=screening_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return extend_user_screening(result)
