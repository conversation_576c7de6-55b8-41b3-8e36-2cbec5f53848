from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from typing import Annotated
from backend.repository import models, queries
from backend.db import Store
from backend.routers.auth import Actor
from backend.util import nullable_date, is_genetic
from backend.screening_template import create_user_screening_schedule_by_sub
from backend.azure_storage import UsersharechangeQueueClient
from pydantic import AfterValidator
import datetime
import enum
import json
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class SortOrder(enum.StrEnum):
  ASC = "asc"
  DESC = "desc"


def check_sort(sort: str) -> str:
  options = list(models.UserHealthEvent.model_fields.keys())
  if sort is not None and sort not in options:
    raise ValueError(f"Invalid sort field: {sort}. Must be one of {options}")
  return sort


@router.get('/user/{sub}/health_event', response_model=list[models.UserHealthEvent])
async def list_user_health_events(
  store: Store,
  actor: Actor,
  sub: str,
  sort: Annotated[str, AfterValidator(check_sort)] = "created_date",
  order: SortOrder = SortOrder.ASC,
) -> list[models.UserHealthEvent]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results_ = store.list_user_health_events(sub=sub)
    results = [row async for row in results_]
    reverse = order == SortOrder.DESC
    results.sort(key=lambda x: getattr(x, sort), reverse=reverse)
    return results
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/health_event/{event_id}', response_model=models.UserHealthEvent)
async def get_user_health_event(store: Store, actor: Actor, sub: str, event_id: str) -> models.UserHealthEvent:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_health_event(sub=sub, event_id=event_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


class CreateUserHealthEventParams(pydantic.BaseModel):  # used instead of queries.CreateUserScreeningParams because end_date needs to accept str
  sub: str
  type: models.UserHealthEventType
  details: str
  notes: list[dict[str, str]] | None = None
  start_date: datetime.date
  end_date: datetime.date | str | None = None
  ongoing: bool


@router.post('/user/{sub}/health_event', response_model=models.UserHealthEvent)
async def create_user_health_event(store: Store, queue_client: UsersharechangeQueueClient, actor: Actor, sub: str, input: CreateUserHealthEventParams) -> models.UserHealthEvent:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.create_user_health_event(queries.CreateUserHealthEventParams(
      sub=input.sub,
      type=input.type,
      details=input.details,
      notes=json.dumps(input.notes),
      start_date=input.start_date,
      end_date=nullable_date(input.end_date, "end_date"),
      ongoing=input.ongoing,
      genetic=is_genetic(input.type)
    ))
    assert isinstance(result, models.UserHealthEvent)
  except ValueError as err:
    logger.warning(f"action blocked due to invalid input: {err}")
    raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(err.args[0]))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await create_user_screening_schedule_by_sub(store=store, sub=sub)
    queue_client.send_message(content=result.user_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


class UpdateUserHealthEventParams(pydantic.BaseModel):  # used instead of queries.CreateUserScreeningParams because end_date needs to accept str
  sub: str
  event_id: str
  type: models.UserHealthEventType
  details: str
  notes: list[dict[str, str]] | None = None
  start_date: datetime.date
  end_date: datetime.date | str | None = None
  ongoing: bool


@router.patch('/user/{sub}/health_event/{event_id}', response_model=models.UserHealthEvent)
async def update_user_health_event(store: Store, queue_client: UsersharechangeQueueClient, actor: Actor, sub: str, event_id: str, input: UpdateUserHealthEventParams) -> models.UserHealthEvent:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  if event_id != input.event_id:
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE, detail=f"event_id query parameter (${event_id}) differs from event_id in body (${input.event_id})")
  try:
    event = await store.get_user_health_event(sub=input.sub, event_id=input.event_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if event is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    if event.notes is None:
      notes = input.notes
    elif input.notes is None:
      notes = event.notes
    else:
      notes = event.notes + input.notes
    result = await store.update_user_health_event(queries.UpdateUserHealthEventParams(
      sub=input.sub,
      event_id=input.event_id,
      type=input.type,
      details=input.details,
      notes=json.dumps(notes),
      start_date=input.start_date,
      end_date=nullable_date(input.end_date, "end_date"),
      ongoing=input.ongoing
    ))
    assert isinstance(result, models.UserHealthEvent)
  except ValueError as err:
    logger.warning(f"action blocked due to invalid input: {err}")
    raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(err.args[0]))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await create_user_screening_schedule_by_sub(store=store, sub=sub)
    queue_client.send_message(content=result.user_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return result


@router.delete('/user/{sub}/health_event/{event_id}', response_model=models.UserHealthEvent)
async def delete_user_health_event(store: Store, actor: Actor, sub: str, event_id: str) -> models.UserHealthEvent:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.delete_user_health_event(sub=sub, event_id=event_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


@router.get('/health_event/type', response_model=list[str])
async def list_health_event_types() -> list[str]:
  try:
    return [k for k in models.UserHealthEventType.__members__.values()]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
