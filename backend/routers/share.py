from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries, models
from backend.db import Store
from backend.routers.auth import Actor
from backend.relationships import RELATIONSHIP_MIRROR
from backend.rewards import award_reward_point
from backend import env
from backend.config import API_BASE_URL
from backend.email import send_email
from backend.screening_template import create_user_screening_schedule_by_id
from backend.azure_storage import UsersharechangeQueueClient
import structlog
import json
import secrets


logger = structlog.get_logger(module=__name__)
router = APIRouter()
email_html_template = env.get_template("share_request.html")
email_plaintext_template = env.get_template("share_request.txt")


class UserShare(models.UserShare):  # used instead of models.UserShare because we need to generate sharer_relationship_to_sharee
  sharer_relationship_to_sharee: models.UserRelationship


async def extend_user_share(input: models.UserShare, store: Store) -> UserShare:
  """Converts models.UserShare to UserShare by adding the sharer_relationship_to_sharee member"""
  sharer = await store.get_user_by_id(user_id=input.sharer_id)
  assert isinstance(sharer, models.User)
  return UserShare(
    sharer_relationship_to_sharee=RELATIONSHIP_MIRROR[input.sharee_relationship_to_sharer][sharer.sex_assigned_at_birth],
    **input.model_dump(),
  )


@router.get('/user/{sub}/share/outgoing', response_model=list[UserShare])
async def list_outgoing_user_shares(store: Store, actor: Actor, sub: str) -> list[UserShare]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_outgoing_user_shares(sub=sub)
    return [await extend_user_share(row, store) async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/share/incoming', response_model=list[UserShare])
async def list_incoming_user_shares(store: Store, actor: Actor, sub: str) -> list[UserShare]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_incoming_user_shares(sub=sub)
    return [await extend_user_share(row, store) async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/share/{share_id}', response_model=UserShare)
async def get_user_share(store: Store, actor: Actor, sub: str, share_id: str) -> UserShare:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    _result = await store.get_user_share(sub=sub, share_id=share_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if _result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return await extend_user_share(_result, store)


async def create_magic_link(store: Store, share: models.UserShare) -> str:
  token = secrets.token_urlsafe(nbytes=32)
  await store.create_magic_link(token=token, metadata=json.dumps({"type": "UserShare", "payload": share.model_dump(mode="json")}))
  return f"{API_BASE_URL}/magic?token={token}"


@router.post('/user/{sub}/share', response_model=UserShare)
async def create_user_share(store: Store, actor: Actor, sub: str, input: queries.CreateUserShareParams) -> models.UserShare:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    sharer = await store.get_user_by_sub(sub=sub)
    assert isinstance(sharer, models.User)
    if sharer.email == input.email or sharer.username == input.username:
      raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="cannot share with yourself")
    _result = await store.create_user_share(input)
    assert isinstance(_result, models.UserShare)
    result = await extend_user_share(_result, store)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    sharee = await store.get_user_by_id(user_id=result.sharee_id)
    assert isinstance(sharee, models.User)
    sharer = await store.get_user_by_id(user_id=result.sharer_id)
    assert isinstance(sharer, models.User)
    email_template_input = {
      "base_url": API_BASE_URL,
      "magic_link": await create_magic_link(store, result),
      "sharer_relationship_to_sharee": RELATIONSHIP_MIRROR[result.sharee_relationship_to_sharer][sharer.sex_assigned_at_birth],
      "label_for_sharee": result.label_for_sharee,
    }
    html = email_html_template.render(email_template_input)
    plaintext = email_plaintext_template.render(email_template_input)
    send_email(
      to=sharee.email,
      subject="A family member wants to share their health experience with you",
      html=html,
      plaintext=plaintext,
    )
  except Exception as err:
    logger.warning(f"unexpected error while sending email: {err}")
  return result


@router.get('/test_outbound_email')
def test_outbound_email() -> None:
  email_template_input = {
    "base_url": "https://nonproduction.api.inherit.healthcare",
    "magic_link": "https://example.com",
    "sharer_relationship_to_sharee": "Mother",
    "label_for_sharee": "mumsie",
  }
  html = email_html_template.render(email_template_input)
  plaintext = email_plaintext_template.render(email_template_input)
  send_email(
    to="<EMAIL>",
    subject="A family member wants to share their health experience with you",
    html=html,
    plaintext=plaintext,
  )


@router.put('/user/{sub}/share/{share_id}/approve', status_code=status.HTTP_204_NO_CONTENT)
async def approve_user_share(store: Store, queue_client: UsersharechangeQueueClient, actor: Actor, sub: str, share_id: str) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.approve_user_share(sub=sub, share_id=share_id)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    sharer = await store.get_user_by_id(user_id=result.sharer_id)
    assert isinstance(sharer, models.User)
    await award_reward_point(store=store, sub=sharer.sub, reason=models.UserRewardPointLogReason.SHARING)
    await create_user_screening_schedule_by_id(store=store, user_id=result.sharee_id)
    queue_client.send_message(content=result.sharer_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)


@router.put('/user/{sub}/share/{share_id}/label/{label}', status_code=status.HTTP_204_NO_CONTENT)
async def label_user_share(store: Store, actor: Actor, sub: str, share_id: str, label: str) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    user = await store.get_user_by_sub(sub=sub)
    assert isinstance(user, models.User)
    share = await store.get_user_share(sub=sub, share_id=share_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if share is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    if share.sharer_id == user.user_id:
      await store.update_user_share_sharer_label(queries.UpdateUserShareSharerLabelParams(
        sub=sub,
        share_id=share_id,
        label_for_sharer=label
      ))
      return
    elif share.sharee_id == user.user_id:
      await store.update_user_share_sharee_label(queries.UpdateUserShareShareeLabelParams(
        sub=sub,
        share_id=share_id,
        label_for_sharee=label
      ))
      return
    else:
      raise RuntimeError(f"share_id {share_id}: user_id {user.user_id} did not match sharer_id {share.sharer_id} or sharee_id {share.sharee_id}")
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.delete('/user/{sub}/share/{share_id}', response_model=UserShare)
async def delete_user_share(store: Store, actor: Actor, sub: str, share_id: str) -> UserShare:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    _result = await store.delete_user_share(sub=sub, share_id=share_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if _result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return await extend_user_share(_result, store)
