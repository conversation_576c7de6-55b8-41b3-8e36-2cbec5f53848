from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries, models
from backend.openai import Client
from backend.db import Store
from backend.prompts import Prompts
from backend.routers.auth import Actor
from backend.util import nullable_str, nullable_int
from backend.config import AZURE_OPENAI_MODEL
from backend.relationships import RELATIONSHIP_DEGREE, RELATIONSHIP_MIRROR
from backend.screening_template import get_all_survey_data
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class CreateAppointmentSupportRequestParams(pydantic.BaseModel):  # used instead of queries.CreateAppointmentSupportRequestParams because we will generate response_output
  sub: str
  appointment_type: str
  appointment_details: str | None = None
  rating: int | None = None


async def shared_health_event_to_string(e: queries.ListSharedHealthEventsRow, store: Store) -> str:
  sharer = await store.get_user_by_id(user_id=e.sharer_id)
  assert isinstance(sharer, models.User)
  sharer_relationship_to_sharee = RELATIONSHIP_MIRROR[e.sharee_relationship_to_sharer][sharer.sex_assigned_at_birth]
  string = f"My {sharer_relationship_to_sharee} ({RELATIONSHIP_DEGREE[sharer_relationship_to_sharee]})"
  if e.type == models.UserHealthEventType.DIAGNOSIS:
    string += f" had a diagnosis of {e.details} on {e.start_date}"
  elif e.type == models.UserHealthEventType.LIFEEVENT:
    string += f" had a life event {e.details} on {e.start_date}"
  else:
    string += f" had a health event of type {e.type}: {e.details} on {e.start_date}"
    logger.warning(f"shared health event type {e.type} is unsupported")
  if e.notes is not None and len(e.notes) > 0:
    string += " with notes as follows:\n" + "\n".join([f"{k["date"]}: {k["text"]}" for k in e.notes])
  return string


def health_event_to_string(e: models.UserHealthEvent) -> str:
  string = f"{e.type} {e.details} {e.start_date} {e.end_date} {e.ongoing}"
  if e.notes is not None and len(e.notes) > 0:
    string += " with notes as follows:\n" + "\n".join([f"{k["date"]}: {k["text"]}" for k in e.notes])
  return string


@router.post('/user/{sub}/appointment_support_request', response_model=models.UserAppointmentSupportRequest)
async def create_appointment_support_request(store: Store, client: Client, prompts: Prompts, actor: Actor, sub: str, input: CreateAppointmentSupportRequestParams) -> models.UserAppointmentSupportRequest:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  if sub != input.sub:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  logger.info("call to OpenAI service", actor=actor)  # TODO convert to metric
  try:
    system_prompt = prompts["create_appointment_support_request"]
    logger.debug("system prompt retrieved", system_prompt=system_prompt)
    _shared_health_events = store.list_shared_health_events(sub=sub)
    shared_health_events = [await shared_health_event_to_string(e=row, store=store) async for row in _shared_health_events]
    _health_events = store.list_user_health_events(sub=sub)
    health_events = [health_event_to_string(row) async for row in _health_events]
    user_prompt = f"The primary reason for my appointment is {input.appointment_type}\n"
    if input.appointment_details is not None and input.appointment_details != "":
      user_prompt += f"Here are some additional details: {input.appointment_details}\n\n"
    user_prompt += "My medical history:\n"
    user_prompt += "n/a\n" if len(health_events) == 0 else "\n".join(health_events)
    user_prompt += "\nFamily history:\n"
    user_prompt += "n/a\n" if len(shared_health_events) == 0 else "\n".join(shared_health_events)
    all_survey_data = await get_all_survey_data(store, sub)
    user_prompt += "Finally, here are the results of my surveys in JSON format:\n"
    user_prompt += all_survey_data.model_dump_json()
    logger.debug("user prompt constructed", user_prompt=user_prompt)
    openai_response = client.chat.completions.create(
      model=AZURE_OPENAI_MODEL,
      messages=[
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
      ]
    )
    logger.debug("openai response", openai_response=openai_response)
    assert isinstance(openai_response.choices[0].message.content, str)
    result = await store.create_appointment_support_request(queries.CreateAppointmentSupportRequestParams(
      sub=input.sub,
      appointment_type=input.appointment_type,
      appointment_details=nullable_str(input.appointment_details),
      rating=nullable_int(input.rating),
      response_output=openai_response.choices[0].message.content
    ))
    assert isinstance(result, models.UserAppointmentSupportRequest)
    return result
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/appointment_support_request', response_model=list[models.UserAppointmentSupportRequest])
async def list_appointment_support_request(store: Store, actor: Actor, sub: str) -> list[models.UserAppointmentSupportRequest]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_user_appointment_support_requests(sub=sub)
    return [row async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/appointment_support_request/{request_id}', response_model=models.UserAppointmentSupportRequest)
async def get_appointment_support_request(store: Store, actor: Actor, sub: str, request_id: str) -> models.UserAppointmentSupportRequest:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_appointment_support_request(sub=sub, request_id=request_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


@router.delete('/user/{sub}/appointment_support_request/{request_id}', response_model=models.UserAppointmentSupportRequest)
async def delete_appointment_support_request(store: Store, actor: Actor, sub: str, request_id: str) -> models.UserAppointmentSupportRequest:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.delete_appointment_support_request(sub=sub, request_id=request_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


@router.put('/user/{sub}/appointment_support_request/{request_id}/rating/{rating}', status_code=status.HTTP_204_NO_CONTENT)
async def update_appointment_support_request_rating(store: Store, actor: Actor, sub: str, request_id: str, rating: int) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_appointment_support_request_rating(queries.UpdateAppointmentSupportRequestRatingParams(sub=sub, request_id=request_id, rating=rating))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
