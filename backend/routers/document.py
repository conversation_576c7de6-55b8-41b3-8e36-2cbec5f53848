from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries
from backend.repository import models
from backend.db import Store
from backend.routers.auth import Actor
from backend.azure_storage import UserdocsContainerParams, UserdocsContainerClient, UserdocsQueueClient
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from azure.core.exceptions import ResourceNotFoundError
from datetime import datetime, timedelta
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class StartUserDocumentResponse(pydantic.BaseModel):
  document_id: str
  blob_url: str


def blob_name(document: models.UserDocument) -> str:
  return f"{document.user_id}/{document.document_id}"


# Start document upload: generate and return document_id and blob_url
@router.get('/user/{sub}/document/start', response_model=StartUserDocumentResponse)
async def start_user_document_upload(store: Store, container_params: UserdocsContainerParams, actor: Actor, sub: str) -> StartUserDocumentResponse:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    document = await store.create_user_document(sub=sub)
    assert isinstance(document, models.UserDocument)
    blob_sas = generate_blob_sas(
      account_name=container_params.account_name,
      container_name=container_params.container_name,
      blob_name=blob_name(document),
      account_key=container_params.account_key,
      permission=BlobSasPermissions(read=False, write=True),
      expiry=datetime.now() + timedelta(hours=1)
    )
    return StartUserDocumentResponse(
      document_id=document.document_id,
      blob_url=f"https://{container_params.account_name}.blob.core.windows.net/{container_params.container_name}/{blob_name(document)}?{blob_sas}"
    )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Finish document upload: confirm blob is present, store metadata, send message to document processor
@router.post('/user/{sub}/document/finish', response_model=models.UserDocument)
async def finish_user_document_upload(store: Store, container_client: UserdocsContainerClient, queue_client: UserdocsQueueClient, actor: Actor, sub: str, input: queries.UpdateUserDocumentParams) -> models.UserDocument:
  if sub != actor or input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    document = await store.get_user_document(sub=sub, document_id=input.document_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if document is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    blob_client = container_client.get_blob_client(blob_name(document))
    blob_client.get_blob_properties()  # return value ignored, we just want to confirm the blob exists
    blob_client.close()  # type: ignore[no-untyped-call]
  except ResourceNotFoundError:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="file not found, please upload file and try again")
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    result = await store.update_user_document(input)
    assert isinstance(result, models.UserDocument)
    queue_client.send_message(content=result.document_id)
    return result
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/document', response_model=list[models.UserDocument])
async def list_user_documents(store: Store, actor: Actor, sub: str) -> list[models.UserDocument]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_user_documents(sub=sub)
    return [row async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/document/{document_id}', response_model=models.UserDocument)
async def get_user_document(store: Store, actor: Actor, sub: str, document_id: str) -> models.UserDocument:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_document(sub=sub, document_id=document_id)
    assert isinstance(result, models.UserDocument)
    return result
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.delete('/user/{sub}/document/{document_id}', response_model=models.UserDocument)
async def delete_user_document(store: Store, actor: Actor, sub: str, document_id: str) -> models.UserDocument:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.delete_user_document(sub=sub, document_id=document_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return result


@router.get('/document/type', response_model=list[str])
async def list_document_types() -> list[str]:
  try:
    return [k for k in models.UserDocumentType.__members__.values()]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
