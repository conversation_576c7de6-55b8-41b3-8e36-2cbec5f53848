from fastapi import APIRouter, HTTPException, status
from fastapi.responses import HTMLResponse
from backend.repository import models
from backend.db import Store
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from backend import env
from backend.rewards import award_reward_point
from backend.screening_template import create_user_screening_schedule_by_id
from backend.azure_storage import UsersharechangeQueueClient
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()
page_template = env.get_template("share_result.html")


@router.get('/magic', response_class=HTMLResponse)
async def get_magic_link(store: Store, queue_client: UsersharechangeQueueClient, token: str) -> HTMLResponse:
  try:
    magic_link = await store.get_magic_link(token=token)
    if magic_link is None:
      return approve_user_share_response(status.HTTP_404_NOT_FOUND)
    if datetime.now() > magic_link.expires_at:
      return approve_user_share_response(status.HTTP_410_GONE)
    if magic_link.metadata["type"] == "UserShare":
      share = models.UserShare(**magic_link.metadata["payload"])
      return await approve_user_share(store=store, queue_client=queue_client, share=share)
    raise RuntimeError("unknown metadata type")
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


async def approve_user_share(store: Store, queue_client: UsersharechangeQueueClient, share: models.UserShare) -> HTMLResponse:
  try:
    result = await store.magic_link_approve_user_share(share_id=share.share_id)
    if result is None:
      return approve_user_share_response(status.HTTP_404_NOT_FOUND)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    return approve_user_share_response(status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    return approve_user_share_response(status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    sharer = await store.get_user_by_id(user_id=result.sharer_id)
    assert isinstance(sharer, models.User)
    await award_reward_point(store=store, sub=sharer.sub, reason=models.UserRewardPointLogReason.SHARING)
    await create_user_screening_schedule_by_id(store=store, user_id=result.sharee_id)
    queue_client.send_message(content=result.sharer_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return approve_user_share_response(status.HTTP_200_OK)


def approve_user_share_response(status_code: int) -> HTMLResponse:
  content = page_template.render({
    "status_code": status_code,
  })
  return HTMLResponse(content=content, status_code=status_code)
