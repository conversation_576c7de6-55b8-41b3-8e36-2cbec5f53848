from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries, models
from backend.openai import Client
from backend.db import Store
from backend.prompts import Prompts
from backend.routers.auth import Actor
from backend.util import nullable_int
from backend.config import AZURE_OPENAI_MODEL
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class CreateUserQuestionSupportRequestParams(pydantic.BaseModel):  # used instead of queries.CreateUserQuestionSupportRequestParams because we will generate response_output
    sub: str
    question: str
    response_output: str | None = None
    rating: int | None = None


@router.post('/user/{sub}/question_support_request', response_model=models.UserQuestionSupportRequest)
async def create_user_question_support_request(
    store: Store,
    client: Client,
    prompts: Prompts,
    actor: Actor,
    sub: str,
    input: CreateUserQuestionSupportRequestParams
) -> models.UserQuestionSupportRequest:
    logger.info("Endpoint for creating user question support requests initialized")
    if sub != actor:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    if sub != input.sub:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

    logger.info("call to OpenAI service", actor=actor)  # TODO convert to metric
    try:
        system_prompt = prompts["create_user_question_support_request"]
        logger.debug("system prompt retrieved", system_prompt=system_prompt)

        response = client.chat.completions.create(
          model=AZURE_OPENAI_MODEL,
          messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": input.question}
          ]
        )
        logger.debug("OpenAI response received", response=response)
        assert isinstance(response.choices[0].message.content, str)
        result = await store.create_question_support_request(
          queries.CreateQuestionSupportRequestParams(
            sub=input.sub,
            question=input.question,
            response_output=response.choices[0].message.content,
            rating=nullable_int(input.rating),
          )
        )
        assert isinstance(result, models.UserQuestionSupportRequest)
        return result

    except IntegrityError as err:
        logger.error(f"action blocked by database integrity error: {err}")
        raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
    except Exception as err:
        logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/question_support_request/{request_id}', response_model=models.UserQuestionSupportRequest)
async def get_question_support_request(
    store: Store,
    actor: Actor,
    sub: str,
    request_id: str
) -> models.UserQuestionSupportRequest:
    if sub != actor:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

    try:
        result = await store.get_question_support_request(sub=sub, request_id=request_id)
    except Exception as err:
        logger.error(f"unexpected error: {err}", exc_info=err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    if result is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    return result


@router.get('/user/{sub}/question_support_request', response_model=list[models.UserQuestionSupportRequest])
async def list_question_support_requests(
    store: Store,
    actor: Actor,
    sub: str
) -> list[models.UserQuestionSupportRequest]:
    if sub != actor:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

    try:
        results = store.list_question_support_requests(sub=sub)
        return [row async for row in results]
    except Exception as err:
        logger.error(f"unexpected error: {err}", exc_info=err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.put('/user/{sub}/question_support_request/{request_id}/rating/{rating}', status_code=status.HTTP_204_NO_CONTENT)
async def update_question_support_request_rating(store: Store, actor: Actor, sub: str, request_id: str, rating: int) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_question_support_request_rating(queries.UpdateQuestionSupportRequestRatingParams(sub=sub, request_id=request_id, rating=rating))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
