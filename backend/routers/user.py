from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries, models
from backend.db import Store
from backend.routers.auth import Actor, NewActor
from backend.util import nullable_str
from backend.azure_storage import Instagramcheck<PERSON>ueueClient
from backend.routers.survey_response import user_survey_response_personal_health_completion, user_survey_response_reproductive_health_completion, user_survey_response_lifestyle_completion
from backend.screening_template import create_user_screening_schedule
from backend.config import TEST_ENVIRONMENT
import json
import pydantic
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class User(models.User):  # used instead of models.User because we will generate reward_progress in-house
  reward_progress: float


def extend_user(input: models.User) -> User:
  """Converts models.User to User by adding the reward_progress member"""
  return User(
    reward_progress=0.0 if input.reward_points == 0 else min(1.0, input.reward_points / 10.0),
    **input.model_dump(),
  )


class CreateUserParams(pydantic.BaseModel):  # used instead of queries.CreateUserParams because we will generate username and referral code in-house
  sub: str
  email: str
  birth_month: int
  birth_year: int
  sex_assigned_at_birth: models.UserSexAssignedAtBirth
  referrer: str | None = None


async def assign_username(store: Store) -> models.Username:
  username = await store.assign_username()
  if username is None:
    raise RuntimeError("unable to assign a username")
  return username


@router.post('/user', response_model=User)
async def create_user(store: Store, actor: NewActor, input: CreateUserParams) -> User:
  if input.sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    username: models.Username = await assign_username(store)
    result = await store.create_user(queries.CreateUserParams(
      sub=actor,
      username=username.username,
      email=input.email,
      birth_month=input.birth_month,
      birth_year=input.birth_year,
      sex_assigned_at_birth=input.sex_assigned_at_birth,
      referral_code=username.referral_code,
      referrer=nullable_str(input.referrer, nulls=["--", "NoReferal"]),  # TODO handle this better in the app
    ))
    assert isinstance(result, models.User)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    await create_user_screening_schedule(store=store, user=result)
    # Award a reward point if the user was referred
    if nullable_str(input.referrer) is not None:
      await store.add_user_reward_point(sub=actor)
      await store.create_user_reward_point_log(queries.CreateUserRewardPointLogParams(
        sub=actor,
        points=1,
        reason=models.UserRewardPointLogReason.BEINGREFERRED
      ))
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
  return extend_user(result)


@router.get('/user/{sub}', response_model=User)
async def get_user(store: Store, actor: Actor, sub: str) -> User:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_by_sub(sub=sub)
    assert isinstance(result, models.User)
    return extend_user(result)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.delete('/user/{sub}', response_model=User)
async def delete_user(store: Store, actor: Actor, sub: str) -> User:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    store.delete_all_user_shares(sub=sub)
    result = await store.delete_user(sub=sub)
    assert isinstance(result, models.User)
    return extend_user(result)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.put('/user/{sub}/instagram/{instagram_handle}', status_code=status.HTTP_204_NO_CONTENT)
async def update_user_instagram_handle(store: Store, queue_client: InstagramcheckQueueClient, actor: Actor, sub: str, instagram_handle: str) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_instagram_handle(sub=sub, instagram_handle=instagram_handle)
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    if TEST_ENVIRONMENT:
      return
    queue_client.send_message(
      content=json.dumps({
        "user_id": result.user_id,
        "instagram_handle": result.instagram_handle
      }),
    )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserHome(pydantic.BaseModel):
  reward_points: int
  count_outgoing_user_shares: int
  count_new_user_screening_alerts: int
  completion_percentage_personal_health_survey: float
  completion_percentage_reproductive_health_survey: float
  completion_percentage_lifestyle_survey: float
  count_user_referrals: int
  impact_points: int
  reward_point_awarded_for_instagram: bool
  instagram_handle: str | None
  reward_progress: float


@router.get('/user/{sub}/home', response_model=UserHome)
async def get_user_home(store: Store, actor: Actor, sub: str) -> UserHome:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    user = await store.get_user_by_sub(sub=sub)
    assert isinstance(user, models.User)
    count_outgoing_user_shares = await store.count_outgoing_user_shares(sub=sub)
    assert isinstance(count_outgoing_user_shares, int)
    count_new_user_screening_alerts = await store.count_new_user_screening_alerts(sub=sub)
    assert isinstance(count_new_user_screening_alerts, int)
    count_user_referrals = await store.count_user_referrals(sub=sub)
    assert isinstance(count_user_referrals, int)
    result = UserHome(
      reward_points=user.reward_points,
      count_outgoing_user_shares=count_outgoing_user_shares,
      count_new_user_screening_alerts=count_new_user_screening_alerts,
      completion_percentage_personal_health_survey=await user_survey_response_personal_health_completion(store=store, sub=sub),
      completion_percentage_reproductive_health_survey=await user_survey_response_reproductive_health_completion(store=store, sub=sub),
      completion_percentage_lifestyle_survey=await user_survey_response_lifestyle_completion(store=store, sub=sub),
      count_user_referrals=count_user_referrals,
      impact_points=user.impact_points,
      reward_point_awarded_for_instagram=user.reward_point_awarded_for_instagram,
      instagram_handle=user.instagram_handle,
      reward_progress=0.0 if user.reward_points == 0 else min(1.0, user.reward_points / 10.0)
    )
    return result
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserReferral(pydantic.BaseModel):
  referral_code: str
  count_user_referrals: int


@router.get('/user/{sub}/referral', response_model=UserReferral)
async def get_user_referral(store: Store, actor: Actor, sub: str) -> UserReferral:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    user = await store.get_user_by_sub(sub=sub)
    assert isinstance(user, models.User)
    count_user_referrals = await store.count_user_referrals(sub=sub)
    assert isinstance(count_user_referrals, int)
    result = UserReferral(
      referral_code=user.referral_code,
      count_user_referrals=count_user_referrals
    )
    return result
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserImpact(pydantic.BaseModel):
  count_research_projects: int
  count_research_queries: int


@router.get('/user/{sub}/impact', response_model=UserImpact)
async def get_user_impact(store: Store, actor: Actor, sub: str) -> UserImpact:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    user = await store.get_user_by_sub(sub=sub)
    assert isinstance(user, models.User)
    result = UserImpact(
      count_research_projects=user.impact_points,
      count_research_queries=0,  # placeholder for future implementation
    )
    return result
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
