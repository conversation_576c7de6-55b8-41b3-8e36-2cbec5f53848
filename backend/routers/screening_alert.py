from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
from backend.repository import queries, models
from backend.db import Store
from backend.routers.auth import Actor
import structlog

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class UserScreeningAlert(models.UserScreeningAlert):  # used instead of models.UserScreeningAlert because we will generate label in-house
  label: str


def extend_user_screening_alert(input: models.UserScreeningAlert) -> UserScreeningAlert:
  """Converts models.UserScreeningAlert to UserScreeningAlert by adding the label member"""
  return UserScreeningAlert(
    label=input.type if input.subtype is None or input.subtype == "" else f"{input.type} ({input.subtype})",
    **input.model_dump(),
  )


@router.get('/user/{sub}/screening_alert', response_model=list[UserScreeningAlert])
async def list_user_screening_alerts(store: Store, actor: Actor, sub: str) -> list[UserScreeningAlert]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_user_screening_alerts(sub=sub)
    return [extend_user_screening_alert(row) async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/screening_alert/new', response_model=list[UserScreeningAlert])
async def list_new_user_screening_alerts(store: Store, actor: Actor, sub: str) -> list[UserScreeningAlert]:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    results = store.list_new_user_screening_alerts(sub=sub)
    return [extend_user_screening_alert(row) async for row in results]
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/screening_alert/{alert_id}', response_model=UserScreeningAlert)
async def get_user_screening_alert(store: Store, actor: Actor, sub: str, alert_id: str) -> UserScreeningAlert:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.get_user_screening_alert(sub=sub, alert_id=alert_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  return extend_user_screening_alert(result)


@router.put('/user/{sub}/screening_alert/{alert_id}/status/{input_status}', status_code=status.HTTP_204_NO_CONTENT)
async def update_user_screening_alert_status(store: Store, actor: Actor, sub: str, alert_id: str, input_status: models.UserScreeningAlertStatus) -> None:
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    result = await store.update_user_screening_alert_status(queries.UpdateUserScreeningAlertStatusParams(sub=sub, alert_id=alert_id, status=input_status))
  except IntegrityError as err:
    logger.warning(f"action blocked by database integrity error: {err}")
    raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if result is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
