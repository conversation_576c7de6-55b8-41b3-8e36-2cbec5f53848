from fastapi import APIRouter, HTTPException, status
from sqlalchemy.exc import IntegrityError
import random
import pydantic
import structlog
from backend.repository import queries, models
from backend.db import Store
from backend.routers.auth import Actor

logger = structlog.get_logger(module=__name__)
router = APIRouter()


class ResponseValue(pydantic.BaseModel):
  selected_options: list[str] | None = None
  answer: str | None = None


class Response(pydantic.BaseModel):
  """Model for question response submission"""
  question_id: str
  value: ResponseValue


class Survey(pydantic.BaseModel):
  """Model for survey with progress information"""
  survey_id: str
  title: str
  description: str
  criteria: str
  progress_percentage: float
  answered_count: int
  question_count: int


class Question(pydantic.BaseModel):
  """Model for question response"""
  question_id: str | None = None
  key: str | None = None
  question: str | None = None
  type: models.QuestionType = models.QuestionType.NONE
  options: list[str] | None = None


@router.get('/user/{sub}/survey', response_model=list[Survey])
async def list_surveys(store: Store, actor: Actor, sub: str) -> list[Survey]:
  """List all surveys the user is eligible to take"""
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    surveys = []
    async for survey in store.get_all_surveys():
      progress = await store.get_user_survey_progress(
        survey_id=survey.survey_id,
        sub=sub
      )
      assert isinstance(progress, queries.GetUserSurveyProgressRow)
      surveys.append(Survey(
        survey_id=survey.survey_id,
        title=survey.title,
        description=survey.description,
        criteria=survey.criteria,
        progress_percentage=progress.answered_count / progress.total_count if progress.total_count > 0 else 0,
        answered_count=progress.answered_count,
        question_count=progress.total_count
      ))
    return surveys
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.get('/user/{sub}/survey/{survey_id}', response_model=Survey)
async def get_survey(store: Store, actor: Actor, sub: str, survey_id: str) -> Survey:
  """Get a survey by ID"""
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  try:
    survey = await store.get_survey_by_id(survey_id=survey_id)
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  if survey is None:
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
  try:
    progress = await store.get_user_survey_progress(
      survey_id=survey_id,
      sub=sub
    )
    assert isinstance(progress, queries.GetUserSurveyProgressRow)
    return Survey(
      survey_id=survey.survey_id,
      title=survey.title,
      description=survey.description,
      criteria=survey.criteria,
      progress_percentage=progress.answered_count / progress.total_count if progress.total_count > 0 else 0,
      answered_count=progress.answered_count,
      question_count=progress.total_count
    )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.post('/user/{sub}/survey/{survey_id}/next', response_model=Question)
async def survey_response(store: Store, actor: Actor, sub: str, survey_id: str, input: Response | None = None) -> Question:
  """Accept a response to a survey question and ask another question"""
  if sub != actor:
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
  if input is not None:
    try:
      question = await store.valid_survey_question(survey_id=survey_id, question_id=input.question_id)
    except Exception as err:
      logger.error(f"unexpected error: {err}", exc_info=err)
      raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    if question is None:
      raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="question does not belong to the specified survey, or the survey or question does not exist",
      )
    match question.type:
      case models.QuestionType.ARRAY | models.QuestionType.MULTIARRAY:
        assert question.options is not None
        if input.value.selected_options is None:
          raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="selected_options is required for array and multiarray question types"
          )
        else:
          for selected_option in input.value.selected_options:
            if selected_option not in question.options:
              raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"invalid option {selected_option}: valid options are [{", ".join(question.options)}]",
              )
          if "Other" in input.value.selected_options and input.value.answer is None:
            raise HTTPException(
              status_code=status.HTTP_400_BAD_REQUEST,
              detail="answer is required when 'Other' is selected for array and multiarray question types"
            )
          elif "other" not in input.value.selected_options and input.value.answer is not None:
            raise HTTPException(
              status_code=status.HTTP_400_BAD_REQUEST,
              detail="answer is allowed only when 'Other' is selected for array and multiarray question types"
            )
          if question.type == models.QuestionType.ARRAY and len(input.value.selected_options) > 1:
            raise HTTPException(
              status_code=status.HTTP_400_BAD_REQUEST,
              detail="only one selected option is allowed for array question type"
            )
      case models.QuestionType.TEXT:
        if input.value.selected_options is not None and len(input.value.selected_options) > 0:
          raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="selected_options is not allowed for text question type"
          )
        if input.value.answer is None:
          raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="answer is required for text question type"
          )
      case _:
        logger.error(f"unknown question type: {question.type}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    try:
      await store.create_user_survey_response(
        queries.CreateUserSurveyResponseParams(
          sub=sub,
          survey_id=survey_id,
          question_id=input.question_id,
          selected_options=input.value.selected_options,
          answer=input.value.answer
        )
      )
    except IntegrityError as err:
      logger.warning(f"action blocked by database integrity error: {err}")
      raise HTTPException(status_code=status.HTTP_406_NOT_ACCEPTABLE)
    except Exception as err:
      logger.error(f"unexpected error: {err}", exc_info=err)
      raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
  try:
    unanswered_questions: list[models.Question] = []
    async for question in store.get_unanswered_questions_for_survey(survey_id=survey_id, sub=sub):
      unanswered_questions.append(question)
    if not unanswered_questions:
      return Question()
    next_question = random.choice(unanswered_questions)
    return Question(
      question_id=next_question.question_id,
      key=next_question.key,
      question=next_question.question,
      type=next_question.type,
      options=next_question.options
    )
  except Exception as err:
    logger.error(f"unexpected error: {err}", exc_info=err)
    raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
