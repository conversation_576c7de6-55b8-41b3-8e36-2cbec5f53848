from typing import Any
from backend.repository import models
import datetime


def nullable_str(input: str | None, nulls: list[str] = []) -> str | None:
  """Work around FlutterFlow's inability to send null by translating empty string to null. Optional argument nulls is a list of strings that should be considered null."""
  return None if input is None or input == "" or input in nulls else input


def nullable_int(input: int | None) -> int | None:
  """Work around FlutterFlow's inability to send null by translating 0 to null"""
  return None if input == 0 else input


def nullable_date(input: datetime.date | str | None, field_name: str | None = None) -> datetime.date | None:
  """Work around FlutterFlow's inability to send null by translating the string "null" to None and any other string to datetime.date"""
  if isinstance(input, datetime.date):
    return input
  if input == "null" or input is None:
    return None
  try:
    value = datetime.date.fromisoformat(input)
    return value
  except ValueError as err:
    if field_name is not None:
      raise ValueError(f"field: {field_name}, error: {str(err.args[0])}")
    raise err


def is_genetic(type: str) -> bool:
  """Determine whether a health event is genetic or not based on it's type"""
  if type in [models.UserHealthEventType.DIAGNOSIS, models.UserHealthEventType.LIFEEVENT]:
    return True
  return False


def any_is_not_none(*args: Any | None) -> bool:
  """Checks if any of the arguments are not None"""
  for arg in args:
    if arg is not None:
      return True
  return False
