from typing import Annotated, Any
from collections.abc import AsyncGenerator
from openai import AzureOpenAI
from fastapi import Depends
from backend.config import AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT


async def get_client() -> AsyncGenerator[Any, Any]:
  client = AzureOpenAI(
    api_key=AZURE_OPENAI_API_KEY,
    api_version="2024-10-21",
    azure_endpoint=AZURE_OPENAI_ENDPOINT
  )
  print(AZURE_OPENAI_API_KEY)
  yield client


Client = Annotated[AzureOpenAI, Depends(get_client)]
