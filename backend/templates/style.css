#wrapper {
  max-width: 350px;
  width: 80%;
  margin: 0 auto;
}
p, span, a.button {
  font-family: "Poppins", sans-serif;
  color: #2d346b;
  margin-top: 10px;
  margin-bottom: 0px;
  margin-left: auto;
  margin-right: auto;
}
#main {
  background-color: white;
  text-align: center;
  padding: 5px 5px;
}
#main img {
  width: 150px;
}
#main p {
  font-size: 1rem;
  font-weight: 600;
}
#main span.relationship {
  text-transform: lowercase;
}
#cta {
  background-color: white;
  text-align: center;
}
#cta p {
  font-size: 0.7rem;
}
#cta a.button {
  text-transform: uppercase;
  text-decoration: none;
  font-weight: 700;
  background-color: #2d346b;
  color: white;
  padding: 10px 50px;
  display: inline-block;
  border-radius: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}
#footer a:hover, a:active {
  text-decoration: none;
}
#testimonial {
  background-color: #f9f3ff;
  padding-top: 5px;
  padding-bottom: 10px;
  padding-left: 40px;
  padding-right: 40px;
  text-align: center;
}
#testimonial p {
  font-size: 0.5rem;
  font-style: italic;
}
#testimonial div.rating {
  padding-top: 15px;
}
#testimonial div.rating img {
  width: 20px;
  padding: 0 2px;
}
#howto {
  background-color: white;
  text-align: center;
  padding: 5px 5px;
}
#howto p.title {
  font-size: 1.0rem;
  font-weight: 600;
  margin-top: 20px;
}
#howto p.body {
  font-size: 0.5rem;
  text-align: left;
}
#footer {
  background-color: white;
  text-align: center;
  padding: 5px 5px;
}
#footer div.separator {
  background-color: #2d346b;
  height: 2px;
  border-radius: 1px;
  width: 60%;
  max-width: 200px;
  display: inline-block;
  margin: 10px auto;
}
#footer a {
  text-decoration: none;
  padding: 0 20px;
}
#footer img {
  width: 20px;
}
