from backend.config import A<PERSON>URE_COMMUNICATION_CONNECTION_STRING, TEST_ENVIRONMENT
from azure.communication.email import EmailClient
import structlog

logger = structlog.get_logger(module=__name__)
email_client = EmailClient.from_connection_string(AZURE_COMMUNICATION_CONNECTION_STRING)


def send_email(to: str, subject: str, html: str, plaintext: str) -> None:
  if TEST_ENVIRONMENT:
    return
  message = {
    "content": {
      "subject": subject,
      "html": html,
      "plainText": plaintext,
    },
    "recipients": {
      "to": [
        {
          "address": to,
        },
      ]
    },
    "senderAddress": "<EMAIL>",
    "replyTo": [
      {
        "address": "<EMAIL>",
        "displayName": "Inherit Healthcare",
      },
    ]
  }
  poller = email_client.begin_send(message)
  while not poller.done():
    poller.wait(10)
