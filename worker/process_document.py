from document_intelligence import document_intelligence_client
from db import database
from azure_storage import storage_client, account_key
from open_ai import client as openai_client, model
from prompts import prompts
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from azure.ai.documentintelligence.models import Ana<PERSON>zeResult, AnalyzeDocumentRequest
from datetime import datetime, timedelta
import json
from repository import queries, models
import logging


def blob_name(document: models.UserDocument) -> str:
  return f"{document.user_id}/{document.document_id}"


def get_blob_url(document_id: str) -> str:
  """Lock document and generate blob url"""
  logging.info("get_blob_url")
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    document = store.worker_get_user_document_by_id(document_id=document_id)
  if document is None:
    raise RuntimeError(f"unable to find document {document_id}")
  blob_sas = generate_blob_sas(
    account_name=storage_client.account_name,
    container_name=storage_client.container_name,
    blob_name=blob_name(document),
    account_key=account_key,
    permission=BlobSasPermissions(read=True),
    expiry=datetime.now() + timedelta(hours=1)
  )
  return f"https://{storage_client.account_name}.blob.core.windows.net/{storage_client.container_name}/{blob_name(document)}?{blob_sas}"


def get_ocr_text(blob_url: str) -> str:
  """Extracts document text from a blob"""
  logging.info("get_ocr_text")
  # start OCR process
  poller = document_intelligence_client.begin_analyze_document(
    model_id="prebuilt-layout",
    body=AnalyzeDocumentRequest(
      url_source=blob_url
    )
  )
  # poll for results
  timeout = 300
  logging.info(f"polling (timeout={timeout})")
  result: AnalyzeResult = poller.result(timeout=timeout)
  # return result or raise error
  logging.info("done")
  return result.content


def transform_text_to_structured_data(document_text: str) -> dict:
  """Transforms document text to structured data"""
  logging.info("transform_text_to_structured_data")
  system_prompt = prompts["transform_text_to_structured_data"]
  openai_response = openai_client.chat.completions.create(
    model=model,
    messages=[
      {"role": "system", "content": system_prompt},
      {"role": "user", "content": document_text},
    ]
  )
  if len(openai_response.choices) > 1:
    logging.warning(f"openai returned {len(openai_response.choices)} choices, defaulting to the first")
  try:
    result = json.loads(openai_response.choices[0].message.content)
    return result
  except json.decoder.JSONDecodeError as err:
    logging.info(openai_response.choices[0].message.content)
    logging.error(f"{type(err)}: {str(err)}")


def store_structured_data(structured_data: dict, document_id: str):
  """Store structured data and unlock document"""
  logging.info("store_structured_data")
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    document = store.worker_update_user_document_by_id(
      document_id=document_id,
      json_output=json.dumps(structured_data)
    )
  if document is None:
    raise RuntimeError(f"unable to store structured data for document {document_id}")
