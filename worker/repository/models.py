# Default value None added to Optional class members by fix.py
# Code generated by sqlc. DO NOT EDIT.
# versions:
#   sqlc v1.29.0
import datetime
import enum
import pydantic
from typing import Any, List, Optional


class HeightWeightUnitType(enum.StrEnum):
    METRIC = "Metric"
    IMPERIAL = "Imperial"


class QuestionType(enum.StrEnum):
    TEXT = "text"
    ARRAY = "array"
    MULTIARRAY = "multiarray"
    NONE = "none"


class UserCountryOfResidence(enum.StrEnum):
    ENGLAND = "England"
    SCOTLAND = "Scotland"
    WALES = "Wales"
    NORTHERNIRELAND = "Northern Ireland"
    OTHER = "Other"


class UserDocumentMimeType(enum.StrEnum):
    APPLICATION_PDF = "application/pdf"


class UserDocumentType(enum.StrEnum):
    TESTRESULTS = "Test Results"
    CLINICIANLETTER = "Clinician Letter"
    OTHER = "Other"


class UserEthnicity(enum.StrEnum):
    WHITE_ENGLISHWELSHSCOTTISHNORTHERNIRISHORBRITISH = "White - English, Welsh, Scottish, Northern Irish, or British"
    WHITE_IRISH = "White - Irish"
    WHITE_GYPSYORIRISHTRAVELLER = "White - Gypsy or Irish Traveller"
    WHITE_ROMA = "White - Roma"
    ANYOTHERWHITEBACKGROUND = "Any other White background"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDBLACKCARIBBEAN = "Mixed or Multiple Ethnic Groups - White and Black Caribbean"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDBLACKAFRICAN = "Mixed or Multiple Ethnic Groups - White and Black African"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDASIAN = "Mixed or Multiple Ethnic Groups - White and Asian"
    ANYOTHERMIXEDORMULTIPLEETHNICBACKGROUND = "Any other Mixed or Multiple ethnic background"
    ASIANORASIANBRITISH_INDIAN = "Asian or Asian British - Indian"
    ASIANORASIANBRITISH_PAKISTANI = "Asian or Asian British - Pakistani"
    ASIANORASIANBRITISH_BANGLADESHI = "Asian or Asian British - Bangladeshi"
    ASIANORASIANBRITISH_CHINESE = "Asian or Asian British - Chinese"
    ANYOTHERASIANBACKGROUND = "Any other Asian background"
    BLACKBLACKBRITISHCARIBBEANORAFRICAN_CARIBBEAN = "Black, Black British, Caribbean, or African - Caribbean"
    BLACKBLACKBRITISHCARIBBEANORAFRICAN_AFRICAN = "Black, Black British, Caribbean, or African - African"
    ANYOTHERBLACKBLACKBRITISHORCARIBBEANBACKGROUND = "Any other Black, Black British, or Caribbean background"
    OTHERETHNICGROUP_ARAB = "Other Ethnic Group - Arab"
    ANYOTHERETHNICGROUP = "Any other ethnic group"


class UserHealthEventType(enum.StrEnum):
    DIAGNOSIS = "Diagnosis"
    INJURY = "Injury"
    PRESCRIPTION = "Prescription"
    LIFEEVENT = "Life Event"
    PROCEDURE = "Procedure"
    APPOINTMENT = "Appointment"
    HEALTHUPDATE = "Health Update"
    VACCINATION = "Vaccination"
    SCREENING = "Screening"
    HEALTHCHECK = "Health Check"
    MENTALHEALTHEVENT = "Mental Health Event"
    ALLERGY = "Allergy"
    OTHER = "Other"


class UserImpactProject(enum.StrEnum):
    FEMALE = "Female"
    LIVEBIRTHS = "LiveBirths"
    MISCARRIAGES = "Miscarriages"
    PCOS = "PCOS"
    ENDOMETRIOSIS = "Endometriosis"
    TRYINGTOCONCEIVE = "TryingToConceive"
    POSSIBLEUNDIAGNOSEDPCOS = "PossibleUndiagnosedPCOS"
    POSSIBLEUNDIAGNOSEDENDOMETRIOSIS = "PossibleUndiagnosedEndometriosis"


class UserQuestionModerationStatus(enum.StrEnum):
    PENDING = "Pending"
    APPROVED = "Approved"
    ANSWERED = "Answered"
    REJECTED = "Rejected"


class UserRelationship(enum.StrEnum):
    MOTHER = "Mother"
    FATHER = "Father"
    GRANDMOTHER = "Grandmother"
    GRANDFATHER = "Grandfather"
    DAUGHTER = "Daughter"
    SON = "Son"
    SISTER = "Sister"
    BROTHER = "Brother"
    NIECE = "Niece"
    NEPHEW = "Nephew"
    AUNT = "Aunt"
    UNCLE = "Uncle"
    GRANDDAUGHTER = "Granddaughter"
    GRANDSON = "Grandson"
    COUSIN = "Cousin"
    PARENT = "Parent"
    CHILD = "Child"
    SIBLING = "Sibling"
    GRANDPARENT = "Grandparent"
    GRANDCHILD = "Grandchild"
    PARENTSSIBLING = "Parent's Sibling"
    SIBLINGSCHILD = "Sibling's Child"
    HALF_BROTHER = "Half-Brother"
    HALF_SISTER = "Half-Sister"
    HALF_SIBLING = "Half-Sibling"


class UserRewardPointLogReason(enum.StrEnum):
    INSTAGRAM = "Instagram"
    PERSONALHEALTHSURVEY = "Personal Health Survey"
    REPRODUCTIVEHEALTHSURVEY = "Reproductive Health Survey"
    LIFESTYLESURVEY = "Lifestyle Survey"
    SURVEY = "Survey"
    SHARING = "Sharing"
    REFERRAL = "Referral"
    BEINGREFERRED = "Being Referred"
    REDEMPTION = "Redemption"


class UserRiskFactors(enum.StrEnum):
    DIABETESDIAGNOSIS = "Diabetes Diagnosis"
    HISTORYOFSMOKING = "History of Smoking"
    FAMILYHISTORYOFTYPE2DIABETES = "Family History of Type 2 Diabetes"
    HISTORYOFGESTATIONALDIABETES = "History of Gestational Diabetes"
    HYPERTENSION = "Hypertension"
    PCOS = "PCOS"
    PREDIABETES = "Prediabetes"
    HISTORYOFHEARTDISEASE = "History of Heart Disease"
    HISTORYOFSTROKE = "History of Stroke"


class UserScreeningAlertStatus(enum.StrEnum):
    NEW = "New"
    MERGED = "Merged"
    IGNORED = "Ignored"


class UserScreeningStatus(enum.StrEnum):
    NOTYETATTENDED = "Not yet attended"
    ATTENDED = "Attended"
    REQUIRESUPDATE = "Requires update"


class UserScreeningType(enum.StrEnum):
    CERVICALSMEAR = "Cervical Smear"
    BOWELSCREENING = "Bowel Screening"
    MAMMOGRAM = "Mammogram"
    SCAN = "Scan"
    BLOODTEST = "Blood Test"
    BLOODPRESSURE = "Blood Pressure"
    CONSULTATION = "Consultation"
    ABDOMINALAORTICANEURYSMSCREENING = "Abdominal Aortic Aneurysm Screening"
    DIABETICEYESCREENING = "Diabetic Eye Screening"
    ROUTINEHEALTHCHECK = "Routine Health Check"
    LUNGCANCERSCREENING = "Lung Cancer Screening"
    OTHER = "Other"


class UserSexAssignedAtBirth(enum.StrEnum):
    FEMALE = "Female"
    MALE = "Male"
    INTERSEX = "Intersex"


class DailyUserCount(pydantic.BaseModel):
    date_id: str
    date: datetime.date
    d_new_user_count: int
    d_total_user_count: int
    d_growth_rate: float
    wk_new_user_count: int
    wk_growth_rate: float
    wk_total_user_count: int
    mth_new_user_count: int
    mth_total_user_count: int
    mth_growth_rate: float
    created_date: datetime.datetime


class Expert(pydantic.BaseModel):
    expert_id: str
    sub: str
    name: str
    qualifications: str
    email: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class ExpertAnswer(pydantic.BaseModel):
    answer_id: str
    expert_id: str
    question_text: str
    answer_text: str
    parent_answer_id: Optional[str] = None
    position_in_tree: int
    answer_tag: Optional[str] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class ExpertAnswerTag(pydantic.BaseModel):
    tag_id: str
    expert_id: str
    tag_name: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class MagicLink(pydantic.BaseModel):
    link_id: str
    token: str
    metadata: Any
    expires_at: datetime.datetime


class Question(pydantic.BaseModel):
    question_id: str
    key: str
    question: str
    type: QuestionType
    options: Optional[List[str]] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class Survey(pydantic.BaseModel):
    survey_id: str
    title: str
    description: str
    criteria: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class SurveyQuestion(pydantic.BaseModel):
    survey_id: str
    question_id: str
    created_date: datetime.datetime


class User(pydantic.BaseModel):
    user_id: str
    sub: str
    username: str
    email: str
    birth_month: int
    birth_year: int
    sex_assigned_at_birth: UserSexAssignedAtBirth
    reward_points: int
    reward_point_awarded_for_instagram: bool
    reward_point_awarded_for_personal_health_survey: bool
    reward_point_awarded_for_reproductive_health_survey: bool
    reward_point_awarded_for_lifestyle_survey: bool
    reward_point_awarded_for_sharing: bool
    reward_point_awarded_to_referrer: bool
    referral_code: str
    referrer: Optional[str] = None
    instagram_handle: Optional[str] = None
    impact_points: int
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserAppointmentSupportRequest(pydantic.BaseModel):
    request_id: str
    user_id: str
    response_output: str
    appointment_type: str
    appointment_details: Optional[str] = None
    rating: Optional[int] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserDocument(pydantic.BaseModel):
    document_id: str
    user_id: str
    label: Optional[str] = None
    type: Optional[UserDocumentType] = None
    mime_type: Optional[UserDocumentMimeType] = None
    json_output: Optional[Any] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserHealthEvent(pydantic.BaseModel):
    event_id: str
    user_id: str
    type: UserHealthEventType
    details: str
    notes: Optional[Any] = None
    start_date: datetime.date
    end_date: Optional[datetime.date] = None
    ongoing: bool
    genetic: bool
    is_reviewed: bool
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserHealthUpdate(pydantic.BaseModel):
    update_id: str
    user_id: str
    text: str
    json_output: Optional[Any] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserImpactLog(pydantic.BaseModel):
    impact_id: str
    user_id: str
    project: UserImpactProject
    points: int
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserQuestion(pydantic.BaseModel):
    question_id: str
    user_id: str
    question_text: str
    moderation_status: UserQuestionModerationStatus
    votes: int
    answer_id: Optional[str] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserQuestionSupportRequest(pydantic.BaseModel):
    request_id: str
    user_id: str
    question: str
    response_output: str
    rating: Optional[int] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserRewardPointLog(pydantic.BaseModel):
    log_id: str
    user_id: str
    points: int
    reason: UserRewardPointLogReason
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserScreening(pydantic.BaseModel):
    screening_id: str
    user_id: str
    type: UserScreeningType
    subtype: Optional[str] = None
    next_date: Optional[datetime.date] = None
    last_date: Optional[datetime.date] = None
    attended_date: Optional[datetime.date] = None
    months_between_appointments: Optional[int] = None
    notes: Optional[str] = None
    status: UserScreeningStatus
    user_managed_schedule: bool
    alert_id: Optional[str] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserScreeningAlert(pydantic.BaseModel):
    alert_id: str
    user_id: str
    type: UserScreeningType
    subtype: Optional[str] = None
    next_date: Optional[datetime.date] = None
    suggested_months_between_appointments: Optional[int] = None
    notes: Optional[str] = None
    status: UserScreeningAlertStatus
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserShare(pydantic.BaseModel):
    share_id: str
    sharer_id: str
    sharee_id: str
    sharee_relationship_to_sharer: UserRelationship
    label_for_sharer: str
    label_for_sharee: str
    approved: bool
    approved_date: Optional[datetime.datetime] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserSurveyResponse(pydantic.BaseModel):
    response_id: str
    user_id: str
    survey_id: str
    question_id: str
    selected_options: Optional[List[str]] = None
    answer: Optional[str] = None
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime] = None


class UserSurveyResponseLifestyle(pydantic.BaseModel):
    response_id: str
    user_id: str
    general_health: Optional[str] = None
    health_vs_last_year: Optional[str] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    height_weight_unit_type: Optional[HeightWeightUnitType] = None
    daily_routine_activity: Optional[str] = None
    strength_training: Optional[str] = None
    cardio_exercise: Optional[str] = None
    brisk_walking: Optional[str] = None
    hours_sitting_per_day: Optional[str] = None
    special_diet: Optional[str] = None
    special_diet_other: Optional[str] = None
    regular_diet_quality: Optional[str] = None
    supplements: Optional[str] = None
    supplements_details: Optional[str] = None
    sleep_hours: Optional[str] = None
    stress_level: Optional[str] = None
    alcohol_frequency: Optional[str] = None
    nicotine_use: Optional[str] = None
    nicotine_details: Optional[str] = None
    mindfulness_practice: Optional[str] = None
    created_date: datetime.datetime


class UserSurveyResponsePersonalHealth(pydantic.BaseModel):
    response_id: str
    user_id: str
    gp_postcode: Optional[str] = None
    ethnicity: Optional[str] = None
    gender: Optional[str] = None
    country: Optional[str] = None
    current_health_condition: bool
    historic_health_condition: bool
    injuries: bool
    allergies: bool
    medications: bool
    routine_vaccines: Optional[List[str]] = None
    vaccines: Optional[List[str]] = None
    childhood_vaccinations_status: Optional[str] = None
    additional_vaccine_notes: Optional[str] = None
    family_health_notes: Optional[str] = None
    disability: Optional[bool] = None
    disability_needs_details: Optional[str] = None
    created_date: datetime.datetime


class UserSurveyResponseReproductiveHealth(pydantic.BaseModel):
    response_id: str
    user_id: str
    reproductive_organs: Optional[str] = None
    reproductive_organ_details: Optional[str] = None
    reproductive_surgeries: Optional[List[str]] = None
    surgery_other_details: Optional[str] = None
    ever_menstruated: Optional[str] = None
    menarche_age: Optional[str] = None
    menstruated_last_12_months: Optional[str] = None
    last_period_date: Optional[str] = None
    cycle_length: Optional[str] = None
    menstrual_symptoms: Optional[List[str]] = None
    menstrual_symptoms_other: Optional[str] = None
    currently_using_contraception: Optional[str] = None
    current_contraception_details: Optional[str] = None
    ever_used_contraception: Optional[str] = None
    past_contraception_details: Optional[str] = None
    currently_using_hrt: Optional[str] = None
    current_hrt_details: Optional[str] = None
    menstrual_status_at_hrt_start: Optional[str] = None
    currently_pregnant: Optional[str] = None
    pregnancies_total: Optional[int] = None
    pregnancies_live_births: Optional[int] = None
    pregnancies_stillbirths: Optional[int] = None
    pregnancies_ectopics: Optional[int] = None
    pregnancies_miscarriages: Optional[int] = None
    pregnancies_terminations: Optional[int] = None
    currently_breastfeeding: Optional[str] = None
    tried_to_conceive_12_months: Optional[str] = None
    fertility_testing: Optional[str] = None
    fertility_testing_details: Optional[str] = None
    diagnosed_conditions: Optional[List[str]] = None
    other_conditions_details: Optional[str] = None
    pcos_screener_irregular_periods: Optional[bool] = None
    pcos_screener_excessive_hair_growth: Optional[bool] = None
    pcos_screener_overweight_16_40: Optional[bool] = None
    pcos_screener_nipple_discharge: Optional[bool] = None
    endopain_period_pain: Optional[bool] = None
    endopain_pain_between_periods: Optional[bool] = None
    endopain_worsening_pain: Optional[bool] = None
    endopain_prolonged_period_pain: Optional[bool] = None
    endopain_stabbing_pain: Optional[bool] = None
    endopain_radiating_back_pain: Optional[bool] = None
    endopain_hip_or_leg_pain: Optional[bool] = None
    endopain_limits_daily_activities: Optional[bool] = None
    endopain_disabling_pain: Optional[bool] = None
    endopain_sexual_severe_pain: Optional[bool] = None
    endopain_sexual_position_specific_pain: Optional[bool] = None
    endopain_sexual_interrupts_sex: Optional[bool] = None
    endopain_bowel_and_bladder_pain_bowel_movements: Optional[bool] = None
    endopain_bowel_and_bladder_diarrhoea_constipation: Optional[bool] = None
    endopain_bowel_and_bladder_bowel_cramps: Optional[bool] = None
    endopain_bowel_and_bladder_urination_pain: Optional[bool] = None
    endopain_bowel_and_bladder_bladder_discomfort: Optional[bool] = None
    cycles_irregular_past_12_months: Optional[str] = None
    symptoms: Optional[List[str]] = None
    menopause_status: Optional[str] = None
    created_date: datetime.datetime


class Username(pydantic.BaseModel):
    username: str
    referral_code: str
    assigned: bool
