from db import database
from repository import queries


def process_impact_scores():
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    # Update the impact score log for each project
    store.impact_female()
    store.impact_live_births()
    store.impact_miscarriages()
    store.impact_pcos()
    store.impact_endometriosis()
    store.impact_trying_to_conceive()
    store.impact_possible_undiagnosed_pcos()
    store.impact_possible_undiagnosed_endometriosis()
    # Update the user table with the totals from the log
    store.impact_update_points()
