from db import database
from open_ai import client as openai_client, model
from prompts import prompts
from repository import queries, models
from relationships import RELATIONSHIP_MIRROR, RELATIONSHIP_DEGREE
from datetime import date
import json
import logging
import pydantic


class UserPromptWithID(pydantic.BaseModel):
  user_id: str
  user_prompt: str


def list_shares_by_sharer_id(sharer_id: str) -> list[models.UserShare]:
  """List the approved shares for the sharer"""
  logging.info("list_shares_by_sharer_id")
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    result = store.worker_list_shares_by_sharer_id(sharer_id=sharer_id)
    return [row for row in result]


def assemble_user_prompt_with_id(share: models.UserShare) -> UserPromptWithID:
  """Assemble user prompt for the sharee"""
  logging.info("assemble_user_prompt")
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    _health_events = store.worker_list_user_health_events(user_id=share.sharee_id)
    health_events = [health_event_to_string(e=row) for row in _health_events]
    _shared_health_events = store.worker_list_shared_health_events(sharee_id=share.sharee_id)
    shared_health_events = [shared_health_event_to_string(e=row) for row in _shared_health_events]
    user_prompt = "My medical history:\n"
    user_prompt += "n/a\n" if len(health_events) == 0 else "\n".join(health_events)
    user_prompt += "\nFamily history:\n"
    user_prompt += "n/a\n" if len(shared_health_events) == 0 else "\n".join(shared_health_events)
    return UserPromptWithID(
      user_id=share.sharee_id,
      user_prompt=user_prompt,
    )


def generate_screening_alerts(user_prompt_with_id: UserPromptWithID) -> list[queries.WorkerCreateUserScreeningAlertParams]:
  """Generate screening alerts based on the user prompt"""
  logging.info("generate_screening_alerts")
  system_prompt = prompts["process_share_event"]
  openai_response = openai_client.chat.completions.create(
    model=model,
    messages=[
      {"role": "system", "content": system_prompt},
      {"role": "user", "content": user_prompt_with_id.user_prompt},
    ]
  )
  logging.info(openai_response.choices[0].message.content)
  if len(openai_response.choices) > 1:
    logging.warning(f"openai returned {len(openai_response.choices)} choices, defaulting to the first")
  try:
    response_content = json.loads(openai_response.choices[0].message.content)
    logging.info(f"decoded response:\n{response_content}")
  except json.decoder.JSONDecodeError as err:
    logging.info(f"raw response:\n{openai_response.choices[0].message.content}")
    logging.error(f"{type(err)}: {str(err)}")
  alerts = []
  logging.info("creating alerts")
  for e in response_content:
    try:
      logging.info(f"creating alert from {e} ({type(e)})")
      e["user_id"] = user_prompt_with_id.user_id
      e["next_date"] = date.today()
      alert = queries.WorkerCreateUserScreeningAlertParams.model_validate(e)
      alerts.append(alert)
    except pydantic.ValidationError as err:
      logging.error(f"{type(err)}: {str(err)}")
      continue
  return alerts


def create_screening_alerts(alerts: list[queries.WorkerCreateUserScreeningAlertParams]) -> None:
  """Create screening alerts in the database"""
  logging.info("create_screening_alerts")
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    for alert in alerts:
      try:
        store.worker_create_user_screening_alert(alert)
      except Exception as err:
        logging.error(f"{type(err)}: {str(err)}")
        continue


def shared_health_event_to_string(e: queries.WorkerListSharedHealthEventsRow) -> str:
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    sharer = store.get_user_by_id(user_id=e.sharer_id)
    assert isinstance(sharer, models.User)
    sharer_relationship_to_sharee = RELATIONSHIP_MIRROR[e.sharee_relationship_to_sharer][sharer.sex_assigned_at_birth]
    string = f"My {sharer_relationship_to_sharee} ({RELATIONSHIP_DEGREE[sharer_relationship_to_sharee]})"
    if e.type == models.UserHealthEventType.DIAGNOSIS:
      string += f" had a diagnosis of {e.details} on {e.start_date}"
    elif e.type == models.UserHealthEventType.LIFEEVENT:
      string += f" had a life event {e.details} on {e.start_date}"
    else:
      string += f" had a health event of type {e.type}: {e.details} on {e.start_date}"
      logging.warning(f"shared health event type {e.type} is unsupported")
    if e.notes is not None and len(e.notes) > 0:
      string += " with notes as follows:\n" + "\n".join([f"{k["date"]}: {k["text"]}" for k in e.notes])
    return string


def health_event_to_string(e: models.UserHealthEvent) -> str:
  string = f"{e.type} {e.details} {e.start_date} {e.end_date} {e.ongoing}"
  if e.notes is not None and len(e.notes) > 0:
    string += " with notes as follows:\n" + "\n".join([f"{k["date"]}: {k["text"]}" for k in e.notes])
  return string
