from azure.storage.blob import B<PERSON>bServiceClient
from config import AZURE_STORAGE_ACCOUNT_ACCESS_KEY, AZURE_STORAGE_ACCOUNT_NAME, AZURE_STORAGE_CONTAINER_USERDOCS_NAME

storage_client = BlobServiceClient(
  account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
  credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY
).get_container_client(AZURE_STORAGE_CONTAINER_USERDOCS_NAME)

account_key = AZURE_STORAGE_ACCOUNT_ACCESS_KEY
