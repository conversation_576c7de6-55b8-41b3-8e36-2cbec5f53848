from db import database
from repository import queries
import logging
import datetime


def calculate_growth(new_users: int, prior_total_users: int, label: str, current_date):
    if prior_total_users > 0:
        growth = round(new_users / prior_total_users * 100, 2)
    else:
        growth = 0

    return growth


def get_user_count() -> int:
    """Get the count of users"""
    logging.info("get_user_count")
    with database.engine.begin() as conn:
        store = queries.Querier(conn=conn)
        result = store.count_active_users()
        return result if result else 0


def overnight_user_count():
    """Get the count of users"""
    logging.info("overnight_user_count")
    with database.engine.begin() as conn:
        store = queries.Querier(conn=conn)
        result = store.get_last_processed_date()
        last_date = result

        if last_date is None:
            last_date = datetime.date(2025, 4, 28)

        # Step 2: Loop over each date between last_date + 1 and yesterday
        current_date = last_date + datetime.timedelta(days=1)
        yesterday = datetime.date.today() - datetime.timedelta(days=1)
        logging.info(f"Processing user counts from {current_date} to {yesterday}")

        records = []
        while current_date <= yesterday:
            # Step 3: Count the users created on the current_date
            next_date = current_date + datetime.timedelta(days=1)
            week_start = current_date - datetime.timedelta(days=6)
            month_start = current_date - datetime.timedelta(days=29)

            daily_new_user_count = store.count_users_for_date(start_date=current_date, end_date=next_date)
            weekly_new_user_count = store.count_users_for_date(start_date=week_start, end_date=next_date)
            monthly_new_user_count = store.count_users_for_date(start_date=month_start, end_date=next_date)

            # Step 4: Count the total users created before the current_date
            # and the total users 7 and 30 days ago

            cur_total_user_count = store.count_active_users_by_date(start_date=current_date)
            wk_user_count = store.count_active_users_by_date(start_date=week_start)
            mth_user_count = store.count_active_users_by_date(start_date=month_start)

            # Calculate growths using the function
            daily_growth = calculate_growth(daily_new_user_count, cur_total_user_count, 'daily', current_date)
            weekly_growth = calculate_growth(weekly_new_user_count, wk_user_count, 'weekly', current_date)
            monthly_growth = calculate_growth(monthly_new_user_count, mth_user_count, 'monthly', current_date)

            records.append(
                queries.InsertDailyUserCountParams(  # noqa: E126
                    date=current_date, # noqa E126
                    d_new_user_count=daily_new_user_count,
                    d_total_user_count=cur_total_user_count,
                    d_growth_rate=daily_growth,
                    wk_new_user_count=weekly_new_user_count,
                    wk_total_user_count=wk_user_count,
                    wk_growth_rate=weekly_growth,
                    mth_new_user_count=monthly_new_user_count,
                    mth_total_user_count=mth_user_count,
                    mth_growth_rate=monthly_growth
                )
            )
            logging.info(f'added record for {current_date}')

            current_date = next_date

        return records


def insert_user_counts(records):
    """Insert user counts into the database"""
    logging.info("insert_user_counts")
    with database.engine.begin() as conn:
        store = queries.Querier(conn=conn)
        for record in records:
            if not isinstance(record, queries.InsertDailyUserCountParams):
                logging.warning(f"Invalid record type: {type(record)}")
                continue
            store.insert_daily_user_count(record)
            logging.info(f"Inserted record for date: {record.date}, daily new user count: {record.d_new_user_count}")
