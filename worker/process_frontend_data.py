import pandas as pd
from datetime import datetime, timedelta
from repository import queries
from db import database
import logging


def age_cat_counts():
    """ GET birth year and Month for users """
    logging.info("fetching birth year and month for users")
    with database.engine.begin() as conn:
        store = queries.Querier(conn=conn)
        result = store.get_user_dob()
        user_dob_list = [row.dict() for row in result]

    bins = range(0, 101, 10)  # Bins for ages 0-10, 10-20, ..., 90-100
    labels = [f"{i} - {i + 10}" for i in bins[:-1]]  # Labels for each bin (e.g., "10-20", "20-30")

    # todays date for the year value today
    today = datetime.now()
    current_year = today.year
    current_month = today.month

    # Convert user_dob_list to a DataFrame
    df = pd.DataFrame(user_dob_list)
    df['Age'] = current_year - df['birth_year']
    # Subtract 1 if the birth month hasn't occurred yet this year
    df.loc[df['birth_month'] > current_month, 'Age'] -= 1

    # Create age categories
    df['Age Category'] = pd.cut(df['Age'], bins=bins, labels=labels, right=False)

    # Step 3: Count users in each age category
    age_category_counts = df['Age Category'].value_counts().sort_index()
    logging.info(f"Age Category Counts: {age_category_counts}")
    return age_category_counts

# def top_users_data():
#     # Extract data for the chart
#     # get top 5 users based on reward points
#     top_users = User.objects.order_by('-reward_points')[:5]
#     top_users = top_users.values('email', 'reward_points')
#     top_users_list = list(top_users)
#     emails = [user['email'] for user in top_users_list]
#     reward_points = [user['reward_points'] for user in top_users_list]

#     return emails, reward_points


def get_cumulative_user_data():
    """
    Retrieves cumulative user data for the graph, combining historical totals (d_total)
    with the current active user count for today.
    """
    logging.info("fetching user count data")
    today = datetime.now().date()
    tomorrow = today + timedelta(days=1)
    with database.engine.begin() as conn:
        store = queries.Querier(conn=conn)
        result = store.get_daily_user_count(date=today)
        active_user_count = store.count_active_users_by_date(start_date=tomorrow)
        records = [{'Date': record.date, 'CumulativeUsers': record.d_total_user_count} for record in result]

    df = pd.DataFrame(records)

    # Add today's data to the DataFrame
    if not df.empty and df['Date'].iloc[-1] == today:
        # If today's data already exists, update it
        df.loc[df['Date'] == today, 'CumulativeUsers'] = active_user_count
    else:
        # Otherwise, append today's data
        df = pd.concat([df, pd.DataFrame([{'Date': today, 'CumulativeUsers': active_user_count}])], ignore_index=True)

    # Ensure the DataFrame is sorted by date
    df = df.sort_values(by='Date')
    logging.info('uer_count data completed')
    return df
