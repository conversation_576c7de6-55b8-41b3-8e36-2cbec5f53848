services:
  worker:
    build:
      context: worker
    pull_policy: build
    environment:
      POSTGRES_HOST: backend-db
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DATABASE: postgres
      POSTGRES_SSLMODE: disable
      A<PERSON><PERSON>E_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT:-https://inherit-nonproduction-openai.openai.azure.com/}
      AZURE_OPENAI_MODEL: ${AZURE_OPENAI_MODEL:-nonproduction-openai-gpt-4o-mini}
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY}
      AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT: ${AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT:-https://inherit-nonproduction-document-intelligence.cognitiveservices.azure.com/}
      AZURE_DOCUMENT_INTELLIGENCE_API_KEY: ${AZURE_DOCUMENT_INTELLIGENCE_API_KEY}
      AZURE_STORAGE_ACCOUNT_ACCESS_KEY: ${AZURE_STORAGE_ACCOUNT_ACCESS_KEY}
      AZURE_STORAGE_CONNECTION_STRING: ${AZURE_STORAGE_CONNECTION_STRING}
      AZURE_STORAGE_ACCOUNT_NAME: ${AZURE_STORAGE_ACCOUNT_NAME:-inheritdevelopment}
      AZURE_STORAGE_CONTAINER_USERDOCS_NAME: ${AZURE_STORAGE_CONTAINER_USERDOCS_NAME:-userdocs}
      AZURE_STORAGE_QUEUE_USERDOCS_NAME: ${AZURE_STORAGE_QUEUE_USERDOCS_NAME:-userdocs}
      AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME: ${AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME:-usersharechange}
      AzureWebJobsStorage: ${AZURE_STORAGE_CONNECTION_STRING}
