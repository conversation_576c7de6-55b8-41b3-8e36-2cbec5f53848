from sqlalchemy import Engine, create_engine
from config import POSTGRES_USERNAME, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DATABASE, POSTGRES_SSLMODE


class PostgresDatabase:
  engine: Engine | None = None

  def __init__(self):
    if not self.engine:
      url = f"postgresql://{POSTGRES_USERNAME}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DATABASE}?sslmode={POSTGRES_SSLMODE}"
      self.engine = create_engine(url=url)


database = PostgresDatabase()
