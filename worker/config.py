import os


def getenv_or_panic(env: str) -> str:
  value = os.getenv(env)
  if value is None:
    raise RuntimeError(f"{env} is required")
  return value


# Required configuration
AZURE_OPENAI_API_KEY = getenv_or_panic("AZURE_OPENAI_API_KEY")
AZURE_DOCUMENT_INTELLIGENCE_API_KEY = getenv_or_panic("AZURE_DOCUMENT_INTELLIGENCE_API_KEY")
AZURE_STORAGE_ACCOUNT_ACCESS_KEY = getenv_or_panic("AZURE_STORAGE_ACCOUNT_ACCESS_KEY")

# Optional configuration
POSTGRES_USERNAME = os.getenv("POSTGRES_USERNAME", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DATABASE = os.getenv("POSTGRES_DATABASE", "postgres")
POSTGRES_SSLMODE = os.getenv("POSTGRES_SSLMODE", "disable")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://inherit-nonproduction-openai.openai.azure.com/")
AZURE_OPENAI_MODEL = os.getenv("AZURE_OPENAI_MODEL", "nonproduction-openai-gpt-4o-mini")
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT", "https://inherit-nonproduction-document-intelligence.cognitiveservices.azure.com/")
AZURE_STORAGE_ACCOUNT_NAME = os.getenv("AZURE_STORAGE_ACCOUNT_NAME", "inheritdevelopment")
AZURE_STORAGE_CONTAINER_USERDOCS_NAME = os.getenv("AZURE_STORAGE_CONTAINER_USERDOCS_NAME", "userdocs")
AZURE_STORAGE_QUEUE_USERDOCS_NAME = os.getenv("AZURE_STORAGE_QUEUE_USERDOCS_NAME", "userdocs")
AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME = os.getenv("AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME", "usersharechange")
