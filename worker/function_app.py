import azure.functions
import logging
import time
# import redis
# import os

import process_document
import process_share_event
import process_user_counts
import process_impact_scores
# import process_frontend_data
from config import AZURE_STORAGE_QUEUE_USERDOCS_NAME, AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME

app = azure.functions.FunctionApp()


@app.function_name(name="ProcessDocument")
@app.queue_trigger(
  arg_name="msg",
  queue_name=AZURE_STORAGE_QUEUE_USERDOCS_NAME,
  connection="AZURE_STORAGE_CONNECTION_STRING"
)
def main_process_document(msg: azure.functions.QueueMessage) -> None:
  """Takes a document_id, extracts document text from the blob, transforms it to structured data, and stores the result"""
  logging.info('processing queue item: %s', msg.get_body().decode("utf-8"))
  document_id = msg.get_body().decode("utf-8")
  blob_url = process_document.get_blob_url(document_id=document_id)
  document_text = process_document.get_ocr_text(blob_url=blob_url)
  structured_data = process_document.transform_text_to_structured_data(document_text=document_text)
  process_document.store_structured_data(structured_data=structured_data, document_id=document_id)


@app.function_name(name="UserShareChange")
@app.queue_trigger(
  arg_name="msg",
  queue_name=AZURE_STORAGE_QUEUE_USERSHARECHANGE_NAME,
  connection="AZURE_STORAGE_CONNECTION_STRING"
)
def main_user_share_change(msg: azure.functions.QueueMessage) -> None:
  """Receives a user_id when a user share is approved or a health event is created or changed, and generates screening alerts for the sharees of that user."""
  logging.info('processing queue item: %s', msg.get_body().decode("utf-8"))
  sharer_id = msg.get_body().decode("utf-8")
  shares = process_share_event.list_shares_by_sharer_id(sharer_id=sharer_id)
  for share in shares:
    user_prompt_with_id = process_share_event.assemble_user_prompt_with_id(share=share)
    alerts = process_share_event.generate_screening_alerts(user_prompt_with_id=user_prompt_with_id)
    process_share_event.create_screening_alerts(alerts=alerts)


@app.function_name(name="OvernightUserCounts")
@app.timer_trigger(
  arg_name="timer",
  schedule="*/1 * * * *"
  # "1 0 * * *",  Run at 00:01 UTC every day

)
def main_overnight_user_counts(timer: azure.functions.TimerRequest) -> None:
  """Aggregate counts of users each day"""
  logging.info('processing user counts: %s', timer.schedule_status)
  if "Last" in timer.schedule_status:
    logging.info(f"Last run time was {timer.schedule_status['Last']}")  # timer.schedule_status keys are ['Last', 'Next', 'LastUpdated']
  else:
    logging.info("Last run time is unknown")
  logging.info("start main_overnight_user_counts")

  # Step 1: Get user counts data
  records = process_user_counts.overnight_user_count()

  # Step 2: Insert data
  process_user_counts.insert_user_counts(records)

  logging.info("finish main_overnight_user_counts")


@app.function_name(name="UserImpactScores")
@app.timer_trigger(
  arg_name="timer",
  schedule="0 * * * *",  # Run at the top of every hour
)
def main_user_impact_scores(timer: azure.functions.TimerRequest) -> None:
  """Calculates the impact scores of users each hour"""
  start_time = time.perf_counter()
  logging.info('processing user impact scores')
  process_impact_scores.process_impact_scores()
  processing_time = time.perf_counter() - start_time
  logging.info(f"finished processing user impact scores in {processing_time:.2f} seconds")


# Initialize Redis connection

# Waiting on azure connections

# redis_client = redis.StrictRedis(
#   host=os.getenv("REDIS_HOST", '127.0.0.1'),
#   port=int(os.getenv("REDIS_PORT", 6379)),
#   decode_responses=True,
#   db=1,
# )


# @app.function_name(name="ProcessDataWithCache")
# @app.timer_trigger(
#   arg_name="timer",
#   schedule="*/1 * * * *",  # Run every minute
# )
# def main_process_data_with_cache(timer: azure.functions.TimerRequest) -> None:
#     """Processes front end data for caching"""
#     logging.info('Processing frontend_Data')
#     age_counts = process_frontend_data.age_cat_counts()
#     # logging.info(f"Processed data: {result}")

#     # Store the result in Redis, Django adds prefix to its calls so we use :1: to match
#     age_count_key = ':1:age_counts'

#     age_counts_json = age_counts.to_json()
#     redis_client.set(age_count_key, age_counts_json, ex=3600)
#     logging.info(f"Storedage_count data in Redis with key: {age_count_key}")

#     user_counts = process_frontend_data.get_cumulative_user_data()
#     user_count_key = ':1:user_counts'
#     user_counts_json = user_counts.to_json(orient='records')
#     redis_client.set(user_count_key, user_counts_json, ex=3600)
#     logging.info(f"Stored user counts in Redis with key: {user_count_key}")
