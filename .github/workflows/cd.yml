name: CD

on:
  workflow_dispatch: {}
  push:
    branches:
      - main

permissions:
  actions: read
  contents: read
  id-token: write  # write permission is required to fetch OIDC tokens
  pull-requests: write

concurrency: nonproduction

env:
  ARM_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  ARM_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
  ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  ARM_USE_OIDC: 'true'
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}

jobs:
  terraform:
    environment: nonproduction
    runs-on: ubuntu-latest
    concurrency: terraform
    steps:
      - uses: actions/checkout@v4
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: '1.10.5'
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - name: Run terraform apply core
        run: |
          terraform -chdir=infra/core init
          terraform -chdir=infra/core apply -auto-approve
      - name: Run terraform apply nonproduction
        run: |
          terraform -chdir=infra/nonproduction init
          terraform -chdir=infra/nonproduction apply -auto-approve
          terraform -chdir=infra/nonproduction output -json > tfoutput.json
      - uses: actions/upload-artifact@v4
        with:
          name: tfoutput.json
          path: tfoutput.json
          overwrite: true
  backend:
    needs:
      - terraform
    environment: nonproduction
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - uses: azure/container-apps-deploy-action@v1
        with:
          appSourcePath: backend
          acrName: inherit
          imageToBuild: inherit.azurecr.io/backend-api:${{ github.run_id }}-${{ github.run_number }}
          containerAppName: nonproduction-backend-api
          resourceGroup: nonproduction-backend
          disableTelemetry: 'true'
  worker:
    needs:
      - terraform
    environment: nonproduction
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - uses: azure/container-apps-deploy-action@v1
        with:
          appSourcePath: worker
          acrName: inherit
          imageToBuild: inherit.azurecr.io/worker:${{ github.run_id }}-${{ github.run_number }}
          containerAppName: nonproduction-worker
          resourceGroup: nonproduction-backend
          disableTelemetry: 'true'
  database:
    needs:
      - terraform
    environment: nonproduction
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ariga/setup-atlas@v0
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - uses: actions/download-artifact@v4
        with:
          name: tfoutput.json
      - name: Run apply database schema nonproduction
        run: |
          ip_address=$(curl -s https://api.ipify.org)
          resource_group=$(jq -r .azure.value.backend_resource_group tfoutput.json)
          postgres_server=$(jq -r .azure.value.backend_postgres_server tfoutput.json)
          rule_name="temporary-github-actions-runner-database"
          az postgres flexible-server firewall-rule create --rule-name=$rule_name --resource-group=$resource_group --name=$postgres_server --start-ip-address=$ip_address
          postgres_hostname=$(jq -r .azure.value.backend_postgres_hostname tfoutput.json)
          postgres_database=$(jq -r .azure.value.backend_postgres_database tfoutput.json)
          postgres_username=$(jq -r .azure.value.backend_postgres_username tfoutput.json)
          postgres_password=$(jq -r .azure.value.backend_postgres_password tfoutput.json)
          url="postgresql://${postgres_username}:${postgres_password}@${postgres_hostname}:5432/${postgres_database}?sslmode=require"
          atlas schema apply --auto-approve --url=$url --to=file://repository/schema/schema.sql --dev-url=docker://postgres/16
          psql --file="repository/testdata/testdata.sql" $url
          az postgres flexible-server firewall-rule delete --yes --rule-name=$rule_name --resource-group=$resource_group --name=$postgres_server
  frontend:
    needs:
      - terraform
    environment: nonproduction
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - uses: azure/container-apps-deploy-action@v1
        with:
          appSourcePath: frontend
          acrName: inherit
          imageToBuild: inherit.azurecr.io/frontend:${{ github.run_id }}-${{ github.run_number }}
          containerAppName: nonproduction-frontend
          resourceGroup: nonproduction-frontend
          disableTelemetry: 'true'
