name: CI

on:
  pull_request:
    branches:
      - main

permissions:
  actions: read
  contents: read
  id-token: write  # write permission is required to fetch OIDC tokens
  pull-requests: write

env:
  ARM_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  ARM_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
  ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  ARM_USE_OIDC: 'true'
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  RECAPTCHA_PRIVATE_KEY: ${{ secrets.RECAPTCHA_PRIVATE_KEY }}
  RECAPTCHA_PUBLIC_KEY: ${{ secrets.RECAPTCHA_PUBLIC_KEY }}
  AZURE_OPENAI_ENDPOINT: ${{ secrets.AZURE_OPENAI_ENDPOINT }}
  AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}

jobs:
  backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: '1.10.5'
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - uses: ariga/setup-atlas@v0
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - name: get nonproduction state
        run: |
          terraform -chdir=infra/nonproduction init
          terraform -chdir=infra/nonproduction output -json > tfoutput.json
      - name: pip install
        run: |
          pip install -r requirements.txt
          pip install -r backend/requirements.txt
      - name: flake8
        run: |
          flake8 backend
          flake8 backend_test
      - name: mypy
        run: |
          mypy backend
          mypy backend_test
      - uses: docker/setup-buildx-action@v3
      - name: docker build
        uses: docker/build-push-action@v6
        with:
          context: backend
          load: true
          tags: backend:latest
      - name: test
        run: |
          export AZURE_OPENAI_API_KEY=$(jq -r .azure.value.openai_primary_access_key tfoutput.json)
          export AZURE_STORAGE_ACCOUNT_ACCESS_KEY=$(jq -r .azure.value.storage_account_primary_access_key tfoutput.json)
          export AZURE_STORAGE_ACCOUNT_NAME=$(jq -r .azure.value.storage_account_name tfoutput.json)
          export AZURE_STORAGE_CONTAINER_USERDOCS_NAME=$(jq -r .azure.value.storage_container_userdocs_name tfoutput.json)
          export AZURE_STORAGE_QUEUE_USERDOCS_NAME=$(jq -r .azure.value.storage_queue_userdocs_name tfoutput.json)
          export AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME=$(jq -r .azure.value.storage_queue_instagramcheck_name tfoutput.json)
          export AZURE_COMMUNICATION_CONNECTION_STRING=$(jq -r .azure.value.communication_connection_string tfoutput.json)
          pytest backend_test --order-dependencies
  worker:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: '1.10.5'
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - uses: ariga/setup-atlas@v0
      - uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      - name: get nonproduction state
        run: |
          terraform -chdir=infra/nonproduction init
          terraform -chdir=infra/nonproduction output -json > tfoutput.json
      - name: pip install
        run: |
          pip install -r requirements.txt
          pip install -r worker/requirements.txt
      - name: flake8
        run: |
          flake8 worker
          flake8 worker_test
      - name: mypy
        run: |  # TODO get mypy working for worker
          #mypy worker
          mypy worker_test
      - uses: docker/setup-buildx-action@v3
      - name: docker build backend
        uses: docker/build-push-action@v6
        with:
          context: backend
          load: true
          tags: backend:latest
      - name: docker build worker
        uses: docker/build-push-action@v6
        with:
          context: worker
          load: true
          tags: worker:latest
      - name: test
        run: |
          export AZURE_STORAGE_CONNECTION_STRING=$(jq -r .azure.value.storage_account_primary_connection_string tfoutput.json)
          export AZURE_STORAGE_ACCOUNT_ACCESS_KEY=$(jq -r .azure.value.storage_account_primary_access_key tfoutput.json)
          export AZURE_STORAGE_ACCOUNT_NAME=$(jq -r .azure.value.storage_account_name tfoutput.json)
          export AZURE_STORAGE_CONTAINER_USERDOCS_NAME=$(jq -r .azure.value.storage_container_userdocs_name tfoutput.json)
          export AZURE_STORAGE_QUEUE_USERDOCS_NAME=$(jq -r .azure.value.storage_queue_userdocs_name tfoutput.json)
          export AZURE_OPENAI_API_KEY=$(jq -r .azure.value.openai_primary_access_key tfoutput.json)
          export AZURE_OPENAI_ENDPOINT=$(jq -r .azure.value.openai_endpoint tfoutput.json)
          export AZURE_OPENAI_MODEL=$(jq -r .azure.value.openai_model tfoutput.json)
          export AZURE_DOCUMENT_INTELLIGENCE_API_KEY=$(jq -r .azure.value.document_intelligence_primary_access_key tfoutput.json)
          export AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=$(jq -r .azure.value.document_intelligence_endpoint tfoutput.json)
          export AZURE_COMMUNICATION_CONNECTION_STRING=$(jq -r .azure.value.communication_connection_string tfoutput.json)
          pytest worker_test --order-dependencies
  manual:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: pip install
        run: |
          pip install -r requirements.txt
          pip install -r manual/instagramcheck/requirements.txt
      - name: flake8
        run: |
          flake8 manual/instagramcheck
      - name: mypy
        run: |
          mypy manual/instagramcheck
  codegen:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - uses: sqlc-dev/setup-sqlc@v4
        with:
          sqlc-version: '1.29.0'
      - uses: ariga/setup-atlas@v0
      - name: pip install
        run: |
          pip install -r requirements.txt
          pip install -r backend/requirements.txt
          pip install -r frontend/requirements.txt
          pip install -r worker/requirements.txt
          pip install -r manual/instagramcheck/requirements.txt
      - name: check codegen
        run: |
          ./gen.sh
          git diff --exit-code api/spec.json backend/repository backend/prompts.json frontend/core/psql_models.py frontend/static worker/repository worker/prompts.json manual/instagramcheck/repository
          ./migrate.sh
          git diff --exit-code repository/migrations
        env:  # these env vars are not actually used
          SECRET_KEY: foo
          DOCS_PASSWORD: bar
          AZURE_OPENAI_API_KEY: baz
          AZURE_STORAGE_ACCOUNT_ACCESS_KEY: boo
          RECAPTCHA_PRIVATE_KEY: woo
          RECAPTCHA_PUBLIC_KEY: gnu
          TOKEN_ISSUER: beep
          TOKEN_AUDIENCE: boop
          AZURE_COMMUNICATION_CONNECTION_STRING: endpoint=https://fake.azure.com/;accesskey=zaphodbeeblebrox
  testdata:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ariga/setup-atlas@v0
      - name: validate testdata
        run: |
          docker run --detach --env POSTGRES_PASSWORD=password --publish 127.0.0.1:5432:5432 postgres:16 && sleep 5
          url="postgresql://postgres:password@localhost:5432/postgres?sslmode=disable"
          atlas schema apply --auto-approve --url=$url --to=file://repository/schema/schema.sql --dev-url=docker://postgres/16
          psql -v ON_ERROR_STOP=1 --file="repository/testdata/generated.sql" $url
          psql -v ON_ERROR_STOP=1 --file="repository/testdata/testdata.sql" $url

  frontend:
    runs-on: ubuntu-latest
    services:
      db:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: password
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - uses: ariga/setup-atlas@v0
      - name: pip install
        run: |
          pip install -r requirements.txt
          pip install -r frontend/requirements.txt
      - name: flake8
        run: |
          flake8 frontend --extend-ignore=E126,MDA124,MDA123,MDA120,MDA131,MDA127,MDA126,MDA105,MDA400,MDA226,MDA227,MDA223,MDA231,MDA401,MDA220,MDA205
      - name: wait for database
        run: |
          until pg_isready -h localhost -p 5432; do
            echo "Waiting for database to be ready..."
            sleep 5
          done
      - name: apply database schema
        run: |
          url="postgresql://postgres:password@localhost:5432/postgres?sslmode=disable"
          atlas schema apply --auto-approve --url=$url --to=file://repository/schema/schema.sql --dev-url=docker://postgres/16
          psql -v ON_ERROR_STOP=1 --file="repository/testdata/generated.sql" $url
          psql -v ON_ERROR_STOP=1 --file="repository/testdata/testdata.sql" $url
      - name: test
        run: |
          cd /home/<USER>/work/inherit/inherit/frontend
          python manage.py test
          
