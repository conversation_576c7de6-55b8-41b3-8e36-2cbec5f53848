#!/usr/bin/env python
import json, argparse, glob, os


def main():
  parser = argparse.ArgumentParser("gen.py")
  parser.add_argument('folder', default='.', help="Directory to search")
  args = parser.parse_args()
  paths = glob.glob('**/*.txt', root_dir=args.folder)
  prompts = {}
  prompts["__generated"] = "This is a generated file. Do not edit."
  for path in paths:
    with open(os.path.join(args.folder, path), 'r') as file:
      content = file.readlines()
      prompts[os.path.basename(path).removesuffix('.txt')] = "".join(content)
  print(json.dumps(prompts, indent=2, sort_keys=True))


if __name__ == "__main__":
  main()
