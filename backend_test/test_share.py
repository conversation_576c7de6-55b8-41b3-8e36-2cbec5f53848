import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_outgoing_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any], user_session_data: dict[str, Any]) -> None:
  other_user = user_session_data["user"]
  url = f"{base_url}/user/{sub}/share"
  payload = {
    "sub": sub,
    "username": other_user["username"],
    "email": other_user["email"],
    "sharee_relationship_to_sharer": "<PERSON>",
    "label_for_sharer": "<PERSON>",
    "label_for_sharee": "Mum"
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert response.json()["approved"] is False
  shared_data["outgoing_share"] = response.json()


@pytest.mark.dependency(depends=["test_create_outgoing_share"])
def test_create_outgoing_share_again(base_url: str, sub: str, session: Session, shared_data: dict[str, Any], user_session_data: dict[str, Any]) -> None:
  other_user = user_session_data["user"]
  url = f"{base_url}/user/{sub}/share"
  payload = {
    "sub": sub,
    "username": other_user["username"],
    "email": other_user["email"],
    "sharee_relationship_to_sharer": "Daughter",
    "label_for_sharer": "Annie",
    "label_for_sharee": "Mum"
  }
  response = session.post(url, json=payload)
  assert response.status_code == 406


@pytest.mark.dependency(depends=["test_create_outgoing_share"])
def test_list_outgoing_shares(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/share/outgoing"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 1


@pytest.mark.dependency(depends=["test_create_outgoing_share"])
def test_list_incoming_shares(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/share/incoming"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 14
  for share in response.json():
    if share["approved"] is False and share["sharer_id"] != "mother":
      shared_data["incoming_share"] = share
      break


@pytest.mark.dependency(depends=["test_create_outgoing_share"])
def test_get_outgoing_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["outgoing_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert share_id == response.json()["share_id"]
  assert "Mum" == response.json()["label_for_sharee"]


@pytest.mark.dependency(depends=["test_list_incoming_shares"])
def test_get_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert share_id == response.json()["share_id"]


@pytest.mark.dependency(depends=["test_create_outgoing_share"])
def test_label_outgoing_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["outgoing_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}/label/Annabelle"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_get_incoming_share"])
def test_label_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}/label/Mummy"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_label_outgoing_share"])
def test_get_labelled_outgoing_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["outgoing_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert share_id == response.json()["share_id"]
  assert "Annabelle" == response.json()["label_for_sharer"]


@pytest.mark.dependency(depends=["test_label_incoming_share"])
def test_get_labelled_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert share_id == response.json()["share_id"]
  assert "Mummy" == response.json()["label_for_sharee"]
  assert response.json()["approved"] is False


@pytest.mark.dependency(depends=["test_get_labelled_incoming_share"])
def test_approve_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}/approve"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_approve_incoming_share"])
def test_get_approved_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "share_id" in response.json()
  assert share_id == response.json()["share_id"]
  assert "Mummy" == response.json()["label_for_sharee"]
  assert response.json()["approved"] is True


@pytest.mark.dependency(depends=["test_get_labelled_incoming_share"])
def test_reward_for_sharing(base_url: str, sub: str, sharer_sub: str, session: Session, sharer_session: Session, shared_data: dict[str, Any]) -> None:
  # Check the starting reward points for the mother
  url = f"{base_url}/user/{sharer_sub}"
  response = sharer_session.get(url)
  assert response.status_code == 200
  starting_reward_points = response.json()["reward_points"]
  # Get the list of incoming shares for main character
  url = f"{base_url}/user/{sub}/share/incoming"
  response = session.get(url)
  assert response.status_code == 200
  # Find the one from mother
  share = None
  for _share in response.json():
    if _share["sharer_id"] == "mother":
      share = _share
      break
  assert share is not None
  assert share["approved"] is False
  # Approve the share
  share_id = share["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}/approve"
  response = session.put(url)
  assert response.status_code == 204
  # Check the reward points for the mother
  url = f"{base_url}/user/{sharer_sub}"
  response = sharer_session.get(url)
  assert response.status_code == 200
  assert response.json()["reward_points"] == starting_reward_points + 1


@pytest.mark.dependency(depends=[
  "test_create_outgoing_share_again",
  "test_list_outgoing_shares",
  "test_get_outgoing_share",
  "test_label_outgoing_share",
  "test_get_labelled_outgoing_share"
])
def test_delete_outgoing_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["outgoing_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.delete(url)
  assert response.status_code == 200


@pytest.mark.dependency(depends=[
  "test_list_incoming_shares",
  "test_get_incoming_share",
  "test_label_incoming_share",
  "test_get_labelled_incoming_share",
  "test_get_approved_incoming_share"
])
def test_delete_incoming_share(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  share_id = shared_data["incoming_share"]["share_id"]
  url = f"{base_url}/user/{sub}/share/{share_id}"
  response = session.delete(url)
  assert response.status_code == 200
