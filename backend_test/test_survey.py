import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_list_survey(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/survey"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) > 0
  shared_data["survey_id"] = response.json()[0]["survey_id"]


@pytest.mark.dependency(depends=["test_list_survey"])
def test_get_survey(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  survey_id = shared_data["survey_id"]
  url = f"{base_url}/user/{sub}/survey/{survey_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "survey_id" in response.json()


@pytest.mark.dependency(depends=["test_list_survey"])
def test_fill_survey(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  survey_id = shared_data["survey_id"]
  url = f"{base_url}/user/{sub}/survey/{survey_id}/next"
  response = session.post(url, json=None)
  assert response.status_code == 200
  assert "question_id" in response.json()
  assert "type" in response.json()
  question_id = response.json()["question_id"]
  question_type = response.json()["type"]
  while question_type != "none":
    match question_type:
      case "array":
        payload = {
          "question_id": question_id,
          "value": {
            "selected_options": ["1 (Mild)"],
            "answer": None
          }
        }
      case "multiarray":
        payload = {
          "question_id": question_id,
          "value": {
            "selected_options": ["Italian", "Chinese"],
            "answer": None
          }
        }
      case "text":
        payload = {
          "question_id": question_id,
          "value": {
            "selected_options": None,
            "answer": "Pizza"
          }
        }
      case _:
        assert False, f"unknown question type: {question_type}"
    response = session.post(url, json=payload)
    assert response.status_code == 200
    assert "question_id" in response.json()
    assert "type" in response.json()
    question_id = response.json()["question_id"]
    question_type = response.json()["type"]
