import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_health_event(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/health_event"
  payload = {
    "sub": sub,
    "type": "Vaccination",
    "details": "foo",
    "notes": [
      {"date": "2025-02-17", "text": "foo"},
    ],
    "start_date": "2025-02-17",
    "end_date": "null",
    "ongoing": False
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "event_id" in response.json()
  assert response.json()["end_date"] is None
  assert response.json()["genetic"] is False
  assert len(response.json()["notes"]) == 1
  shared_data["event_id"] = response.json()["event_id"]


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_health_event_genetic(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/health_event"
  payload = {
    "sub": sub,
    "type": "Diagnosis",
    "details": "foo",
    "notes": [
      {"date": "2025-02-17", "text": "foo"},
    ],
    "start_date": "2025-02-17",
    "end_date": "null",
    "ongoing": False
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "event_id" in response.json()
  assert response.json()["end_date"] is None
  assert response.json()["genetic"] is True
  assert len(response.json()["notes"]) == 1


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_health_event_invalid_date(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/health_event"
  payload = {
    "sub": sub,
    "type": "Vaccination",
    "details": "foo",
    "notes": [
      {"date": "2025-02-17", "text": "foo"},
    ],
    "start_date": "2025-02-17",
    "end_date": "foo",
    "ongoing": False
  }
  response = session.post(url, json=payload)
  assert response.status_code == 422


@pytest.mark.dependency(depends=["test_create_health_event", "test_create_health_event_genetic"])
def test_list_health_events(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/health_event"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 2


@pytest.mark.dependency(depends=["test_create_health_event", "test_create_health_event_genetic"])
def test_list_health_events_sorted(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/health_event?sort=start_date&order=desc"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 2


@pytest.mark.dependency(depends=["test_create_health_event", "test_create_health_event_genetic"])
def test_list_health_events_invalid_sort(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/health_event?sort=banana&order=desc"
  response = session.get(url)
  assert response.status_code == 422


@pytest.mark.dependency(depends=["test_create_health_event", "test_create_health_event_genetic"])
def test_list_health_events_invalid_order(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/health_event?sort=created_date&order=apple"
  response = session.get(url)
  assert response.status_code == 422


@pytest.mark.dependency(depends=["test_create_health_event"])
def test_get_health_event(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  event_id = shared_data["event_id"]
  url = f"{base_url}/user/{sub}/health_event/{event_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "event_id" in response.json()


@pytest.mark.dependency(depends=["test_create_health_event"])
def test_update_health_event(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  event_id = shared_data["event_id"]
  url = f"{base_url}/user/{sub}/health_event/{event_id}"
  payload = {
    "sub": sub,
    "event_id": event_id,
    "type": "Vaccination",
    "details": "foo bar baz",
    "notes": [
      {"date": "2025-02-17", "text": "bar"},
    ],
    "start_date": "2025-02-17",
    "ongoing": False
  }
  response = session.patch(url, json=payload)
  assert response.status_code == 200
  assert "event_id" in response.json()
  assert "notes" in response.json()
  assert len(response.json()["notes"]) == 2
  assert response.json()["notes"][0]["text"] == "foo"
  assert response.json()["notes"][1]["text"] == "bar"


@pytest.mark.dependency(depends=[
  "test_list_health_events",
  "test_list_health_events_sorted",
  "test_list_health_events_invalid_sort",
  "test_list_health_events_invalid_order",
  "test_get_health_event",
  "test_update_health_event",
])
def test_delete_health_event(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  event_id = shared_data["event_id"]
  url = f"{base_url}/user/{sub}/health_event/{event_id}"
  response = session.delete(url)
  assert response.status_code == 200


@pytest.mark.dependency(depends=["test_delete_health_event"])
def test_get_health_event_after_delete(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  event_id = shared_data["event_id"]
  url = f"{base_url}/user/{sub}/health_event/{event_id}"
  response = session.get(url)
  assert response.status_code == 404


@pytest.mark.dependency()
def test_list_health_event_types(base_url: str, session: Session) -> None:
  url = f"{base_url}/health_event/type"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 13
  assert "Vaccination" in response.json()
