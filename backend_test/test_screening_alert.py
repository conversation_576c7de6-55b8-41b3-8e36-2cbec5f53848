import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency()
def test_list_screening_alerts(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/screening_alert"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 2


@pytest.mark.dependency()
def test_list_new_screening_alerts(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/screening_alert/new"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 2


@pytest.mark.dependency()
def test_get_screening_alert(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  alert_id = "main_character_screening_alert"
  url = f"{base_url}/user/{sub}/screening_alert/{alert_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "alert_id" in response.json()
  assert response.json()["alert_id"] == alert_id
  shared_data["alert_id"] = response.json()["alert_id"]


@pytest.mark.dependency(depends=["test_get_screening_alert"])
def test_update_screening_alert_status_merged(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  alert_id = shared_data["alert_id"]
  url = f"{base_url}/user/{sub}/screening_alert/{alert_id}/status/Merged"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_get_screening_alert"])
def test_update_screening_alert_status_ignored(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  alert_id = shared_data["alert_id"]
  url = f"{base_url}/user/{sub}/screening_alert/{alert_id}/status/Ignored"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_get_screening_alert"])
def test_update_screening_alert_status_invalid(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  alert_id = shared_data["alert_id"]
  url = f"{base_url}/user/{sub}/screening_alert/{alert_id}/status/foo"
  response = session.put(url)
  assert response.status_code == 422


@pytest.mark.dependency(depends=[
  "test_update_screening_alert_status_merged",
  "test_update_screening_alert_status_ignored",
  "test_update_screening_alert_status_invalid"
])
def test_list_screening_alerts_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/screening_alert/new"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 1
