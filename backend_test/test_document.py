import pytest
import requests
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency()
def test_create_document(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  label = "foo"
  # get upload url
  url = f"{base_url}/user/{sub}/document/start"
  response = session.get(url)
  assert response.status_code == 200
  blob_url = response.json()["blob_url"]
  document_id = response.json()["document_id"]
  assert blob_url.startswith("https://")
  # upload file to url
  response = requests.put(
    blob_url,
    data="bar",
    headers={
      'x-ms-blob-type': 'BlockBlob'
    },
  )
  assert response.status_code == 201
  # finish document creation
  payload = {
    "sub": sub,
    "document_id": document_id,
    "label": label,
    "type": "Clinician Letter",
    "mime_type": "application/pdf"
  }
  url = f"{base_url}/user/{sub}/document/finish"
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "document_id" in response.json()
  assert document_id == response.json()["document_id"]
  shared_data["document_id"] = response.json()["document_id"]


@pytest.mark.dependency(depends=["test_create_document"])
def test_create_document_repeat_label(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  label = "foo"
  # get upload url
  url = f"{base_url}/user/{sub}/document/start"
  response = session.get(url)
  assert response.status_code == 200
  blob_url = response.json()["blob_url"]
  document_id = response.json()["document_id"]
  assert blob_url.startswith("https://")
  # upload file to url
  response = requests.put(
    blob_url,
    data="bar",
    headers={
      'x-ms-blob-type': 'BlockBlob'
    },
  )
  assert response.status_code == 201
  # finish document creation
  payload = {
    "sub": sub,
    "document_id": document_id,
    "label": label,
    "type": "Clinician Letter",
    "mime_type": "application/pdf"
  }
  url = f"{base_url}/user/{sub}/document/finish"
  response = session.post(url, json=payload)
  assert response.status_code == 406


@pytest.mark.dependency()
def test_create_document_invalid_mime_type(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  label = "bar"
  # get upload url
  url = f"{base_url}/user/{sub}/document/start"
  response = session.get(url)
  assert response.status_code == 200
  blob_url = response.json()["blob_url"]
  document_id = response.json()["document_id"]
  assert blob_url.startswith("https://")
  # upload file to url
  response = requests.put(
    blob_url,
    data="bar",
    headers={
      'x-ms-blob-type': 'BlockBlob'
    },
  )
  assert response.status_code == 201
  # finish document creation
  payload = {
    "sub": sub,
    "document_id": document_id,
    "label": label,
    "type": "Clinician Letter",
    "mime_type": "baz"
  }
  url = f"{base_url}/user/{sub}/document/finish"
  response = session.post(url, json=payload)
  assert response.status_code == 422


@pytest.mark.dependency()
def test_create_document_missing_upload(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  label = "baz"
  # get upload url
  url = f"{base_url}/user/{sub}/document/start"
  response = session.get(url)
  assert response.status_code == 200
  blob_url = response.json()["blob_url"]
  document_id = response.json()["document_id"]
  assert blob_url.startswith("https://")
  # finish document creation without uploading a file
  payload = {
    "sub": sub,
    "document_id": document_id,
    "label": label,
    "type": "Clinician Letter",
    "mime_type": "application/pdf"
  }
  url = f"{base_url}/user/{sub}/document/finish"
  response = session.post(url, json=payload)
  assert response.status_code == 404


@pytest.mark.dependency(depends=[
  "test_create_document",
  "test_create_document_repeat_label",
  "test_create_document_invalid_mime_type",
  "test_create_document_missing_upload"
])
def test_list_documents(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/document"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 2  # the test data contains a preexisting document


@pytest.mark.dependency(depends=["test_list_documents"])
def test_delete_document(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  document_id = shared_data["document_id"]
  url = f"{base_url}/user/{sub}/document/{document_id}"
  response = session.delete(url)
  assert response.status_code == 200
