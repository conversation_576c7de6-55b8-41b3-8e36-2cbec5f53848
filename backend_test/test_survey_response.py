import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency()
def test_referrer_reward_point_before_update(base_url: str, referrer_sub: str, referrer_session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{referrer_sub}/home"
  response = referrer_session.get(url)
  assert response.status_code == 200
  assert "reward_points" in response.json()
  shared_data["referrer_reward_points"] = response.json()["reward_points"]


@pytest.mark.dependency()
def test_get_survey_response_personal_health_before_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()
  assert "gp_postcode" in response.json()
  assert response.json()["gp_postcode"] is None


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update"])
def test_get_survey_response_personal_health_completion_before_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "completion_percentage_personal_health_survey" in response.json()
  assert response.json()["completion_percentage_personal_health_survey"] == 0.0


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update", "test_get_survey_response_personal_health_completion_before_update"])
def test_update_survey_response_personal_health_one(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health/1"
  payload = {
    "sub": sub,
    "gp_postcode": "TW1 8GV",
    "ethnicity": "Caucasian",
    "country": "Wales",
    "gender": "Female",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update", "test_get_survey_response_personal_health_completion_before_update"])
def test_update_survey_response_personal_health_two(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health/2"
  payload = {
    "sub": sub,
    "current_health_condition": True,
    "historic_health_condition": True,
    "injuries": True,
    "allergies": True,
    "medications": True,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update", "test_get_survey_response_personal_health_completion_before_update"])
def test_update_survey_response_personal_health_three(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health/3"
  payload = {
    "sub": sub,
    "routine_vaccines": ["MMR", "Flu"],
    "vaccines": ["RSV"],
    "childhood_vaccinations_status": "Yes",
    "additional_vaccine_notes": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update", "test_get_survey_response_personal_health_completion_before_update"])
def test_update_survey_response_personal_health_four(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health/4"
  payload = {
    "sub": sub,
    "family_health_notes": "Helpful notes",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_before_update", "test_get_survey_response_personal_health_completion_before_update"])
def test_update_survey_response_personal_health_five(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health/5"
  payload = {
    "sub": sub,
    "disability": False,
    "disability_needs_details": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=[
  "test_update_survey_response_personal_health_one",
  "test_update_survey_response_personal_health_two",
  "test_update_survey_response_personal_health_three",
  "test_update_survey_response_personal_health_four",
  "test_update_survey_response_personal_health_five",
])
def test_get_survey_response_personal_health_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/personal_health"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_personal_health_after_update"])
def test_get_survey_response_personal_health_completion_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "completion_percentage_personal_health_survey" in response.json()
  assert response.json()["completion_percentage_personal_health_survey"] == 1.0


@pytest.mark.dependency()
def test_get_survey_response_reproductive_health_before_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()
  assert "reproductive_organs" in response.json()
  assert response.json()["reproductive_organs"] is None


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update"])
def test_get_survey_response_reproductive_health_completion_before_update(base_url: str, sub: str, session: Session) -> None:
    url = f"{base_url}/user/{sub}/home"
    response = session.get(url)
    assert response.status_code == 200
    assert "completion_percentage_reproductive_health_survey" in response.json()
    assert response.json()["completion_percentage_reproductive_health_survey"] == 0.0


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_one(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/1"
  payload = {
    "sub": sub,
    "reproductive_organs": "Ovaries",
    "reproductive_organ_details": None,
    "reproductive_surgeries": None,
    "surgery_other_details": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_two(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/2"
  payload = {
    "sub": sub,
    "ever_menstruated": "Yes",
    "menarche_age": "12",
    "menstruated_last_12_months": "Yes",
    "last_period_date": "2023-01-01",
    "cycle_length": "28",
    "menstrual_symptoms": ["Cramps"],
    "menstrual_symptoms_other": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_three(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/3"
  payload = {
    "sub": sub,
    "currently_using_contraception": "Yes",
    "current_contraception_details": "Pill",
    "ever_used_contraception": "Yes",
    "past_contraception_details": "IUD",
    "currently_using_hrt": "No",
    "current_hrt_details": None,
    "menstrual_status_at_hrt_start": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_four(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/4"
  payload = {
    "sub": sub,
    "currently_pregnant": "No",
    "pregnancies_total": 2,
    "pregnancies_live_births": 1,
    "pregnancies_stillbirths": 0,
    "pregnancies_ectopics": 0,
    "pregnancies_miscarriages": 1,
    "pregnancies_terminations": 0,
    "currently_breastfeeding": "Yes",
    "tried_to_conceive_12_months": "Yes",
    "fertility_testing": "No",
    "fertility_testing_details": None,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_five(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/5"
  payload = {
    "sub": sub,
    "diagnosed_conditions": ["PCOS"],
    "other_conditions_details": None,
    "pcos_screener_irregular_periods": True,
    "pcos_screener_excessive_hair_growth": False,
    "pcos_screener_overweight_16_40": True,
    "pcos_screener_nipple_discharge": False,
    "endopain_period_pain": True,
    "endopain_pain_between_periods": False,
    "endopain_worsening_pain": False,
    "endopain_prolonged_period_pain": False,
    "endopain_stabbing_pain": False,
    "endopain_radiating_back_pain": False,
    "endopain_hip_or_leg_pain": False,
    "endopain_limits_daily_activities": False,
    "endopain_disabling_pain": False,
    "endopain_sexual_severe_pain": False,
    "endopain_sexual_position_specific_pain": False,
    "endopain_sexual_interrupts_sex": False,
    "endopain_bowel_and_bladder_pain_bowel_movements": False,
    "endopain_bowel_and_bladder_diarrhoea_constipation": False,
    "endopain_bowel_and_bladder_bowel_cramps": False,
    "endopain_bowel_and_bladder_urination_pain": False,
    "endopain_bowel_and_bladder_bladder_discomfort": False,
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_before_update", "test_get_survey_response_reproductive_health_completion_before_update"])
def test_update_survey_response_reproductive_health_six(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health/6"
  payload = {
    "sub": sub,
    "cycles_irregular_past_12_months": "Yes",
    "symptoms": ["Hot flashes"],
    "menopause_status": "Perimenopause",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=[
  "test_update_survey_response_reproductive_health_one",
  "test_update_survey_response_reproductive_health_two",
  "test_update_survey_response_reproductive_health_three",
  "test_update_survey_response_reproductive_health_four",
  "test_update_survey_response_reproductive_health_five",
  "test_update_survey_response_reproductive_health_six",
])
def test_get_survey_response_reproductive_health_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/reproductive_health"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()
  assert "reproductive_organs" in response.json()
  assert response.json()["reproductive_organs"] == "Ovaries"
  assert "ever_menstruated" in response.json()
  assert response.json()["ever_menstruated"] == "Yes"
  assert "currently_using_contraception" in response.json()
  assert response.json()["currently_using_contraception"] == "Yes"
  assert "currently_pregnant" in response.json()
  assert response.json()["currently_pregnant"] == "No"
  assert "diagnosed_conditions" in response.json()
  assert response.json()["diagnosed_conditions"] == ["PCOS"]
  assert "cycles_irregular_past_12_months" in response.json()
  assert response.json()["cycles_irregular_past_12_months"] == "Yes"


@pytest.mark.dependency(depends=["test_get_survey_response_reproductive_health_after_update"])
def test_get_survey_response_reproductive_health_completion_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "completion_percentage_reproductive_health_survey" in response.json()
  assert response.json()["completion_percentage_reproductive_health_survey"] == 1.0


@pytest.mark.dependency()
def test_get_survey_response_lifestyle_before_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()
  assert "general_health" in response.json()
  assert response.json()["general_health"] is None


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_before_update"])
def test_get_survey_response_lifestyle_completion_before_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "completion_percentage_lifestyle_survey" in response.json()
  assert response.json()["completion_percentage_lifestyle_survey"] == 0.0


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_before_update", "test_get_survey_response_lifestyle_completion_before_update"])
def test_update_survey_response_lifestyle_one(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle/1"
  payload = {
    "sub": sub,
    "general_health": "Good",
    "health_vs_last_year": "Better",
    "height": 180.0,
    "weight": 400.0,
    "height_weight_unit_type": "Metric",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_before_update", "test_get_survey_response_lifestyle_completion_before_update"])
def test_update_survey_response_lifestyle_two(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle/2"
  payload = {
    "sub": sub,
    "daily_routine_activity": "Moderate",
    "strength_training": "Yes",
    "cardio_exercise": "No",
    "brisk_walking": "Yes",
    "hours_sitting_per_day": "26",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_before_update", "test_get_survey_response_lifestyle_completion_before_update"])
def test_update_survey_response_lifestyle_three(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle/3"
  payload = {
    "sub": sub,
    "special_diet": "Vegetarian",
    "special_diet_other": None,
    "regular_diet_quality": "Good",
    "supplements": "Yes",
    "supplements_details": "Vitamin D and Omega-3",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_before_update", "test_get_survey_response_lifestyle_completion_before_update"])
def test_update_survey_response_lifestyle_four(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle/4"
  payload = {
    "sub": sub,
    "sleep_hours": "7",
    "stress_level": "Low",
    "alcohol_frequency": "Occasionally",
    "nicotine_use": "Yes",
    "nicotine_details": None,
    "mindfulness_practice": "No",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "response_id" in response.json()


@pytest.mark.dependency(depends=[
  "test_update_survey_response_lifestyle_one",
  "test_update_survey_response_lifestyle_two",
  "test_update_survey_response_lifestyle_three",
  "test_update_survey_response_lifestyle_four",
])
def test_get_survey_response_lifestyle_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/survey_response/lifestyle"
  response = session.get(url)
  assert response.status_code == 200
  assert "response_id" in response.json()
  assert "general_health" in response.json()
  assert response.json()["general_health"] == "Good"
  assert "daily_routine_activity" in response.json()
  assert response.json()["daily_routine_activity"] == "Moderate"
  assert "special_diet" in response.json()
  assert response.json()["special_diet"] == "Vegetarian"
  assert "sleep_hours" in response.json()
  assert response.json()["sleep_hours"] == "7"


@pytest.mark.dependency(depends=["test_get_survey_response_lifestyle_after_update"])
def test_get_survey_response_lifestyle_completion_after_update(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "completion_percentage_lifestyle_survey" in response.json()
  assert response.json()["completion_percentage_lifestyle_survey"] == 1.0


@pytest.mark.dependency(depends=[
  "test_referrer_reward_point_before_update",
  "test_get_survey_response_personal_health_completion_after_update",
  "test_get_survey_response_reproductive_health_completion_after_update",
  "test_get_survey_response_lifestyle_completion_after_update",
])
def test_referrer_reward_point_after_update(base_url: str, referrer_sub: str, referrer_session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{referrer_sub}/home"
  response = referrer_session.get(url)
  assert response.status_code == 200
  assert "reward_points" in response.json()
  assert response.json()["reward_points"] == shared_data["referrer_reward_points"] + 1
