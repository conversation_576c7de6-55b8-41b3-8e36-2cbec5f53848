import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_user_question_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
    url = f"{base_url}/user/{sub}/question_support_request"
    payload = {
      "sub": sub,
      "question": "Do vaccines cause autism?"
    }
    response = session.post(url, json=payload)
    assert response.status_code == 200
    assert "request_id" in response.json()
    assert "response_output" in response.json()
    shared_data["request_id"] = response.json()["request_id"]


@pytest.mark.dependency(depends=["test_create_user_question_support_request"])
def test_list_question_support_requests(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/question_support_request"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 1


@pytest.mark.dependency(depends=["test_create_user_question_support_request"])
def test_get_question_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/question_support_request/{request_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "request_id" in response.json()
  assert response.json()["request_id"] == request_id
