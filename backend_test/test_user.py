import pytest
import re
from requests import Session
from typing import Any
from datetime import date, timedelta


@pytest.mark.dependency()
def test_create_user(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user"
  payload = {
    "sub": user_sub,
    "email": "<EMAIL>",
    "birth_month": 1,
    "birth_year": 2000,
    "sex_assigned_at_birth": "Male"
  }
  response = user_session.post(url, json=payload)
  assert response.status_code == 200
  assert "user_id" in response.json()


@pytest.mark.dependency(depends=["test_create_user"])
def test_create_user_again(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user"
  payload = {
    "sub": user_sub,
    "email": "<EMAIL>",
    "birth_month": 1,
    "birth_year": 2000,
    "sex_assigned_at_birth": "Male"
  }
  response = user_session.post(url, json=payload)
  assert response.status_code == 406


@pytest.mark.dependency(depends=["test_create_user"])
def test_update_user_instagram_handle(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user/{user_sub}/instagram/@foo"
  response = user_session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_create_user"])
def test_get_user(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user/{user_sub}"
  response = user_session.get(url)
  assert response.status_code == 200
  assert "user_id" in response.json()
  assert "username" in response.json()
  assert re.match(r'\d+-\d+-\d+-\d+-\d+', response.json()["username"])
  assert "referral_code" in response.json()
  assert re.match(r'\w+-\w+-\w+', response.json()["referral_code"])


@pytest.mark.dependency(depends=["test_create_user"])
def test_get_another_user(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user/foo"
  response = user_session.get(url)
  assert response.status_code == 401


@pytest.mark.dependency(depends=[
  "test_create_user_again",
  "test_update_user_instagram_handle",
  "test_get_user",
  "test_get_another_user"
])
def test_delete_user(base_url: str, user_sub: str, user_session: Session) -> None:
  url = f"{base_url}/user/{user_sub}"
  response = user_session.delete(url)
  assert response.status_code == 200


@pytest.mark.dependency(depends=["test_delete_user"])
def test_recreate_user(base_url: str, user_sub: str, user_session: Session, user_session_data: dict[str, Any]) -> None:
  url = f"{base_url}/user"
  birthday = date.today() - timedelta(days=365 * 34)  # 34 years old
  payload = {
    "sub": user_sub,
    "email": "<EMAIL>",
    "birth_month": birthday.month,
    "birth_year": birthday.year,
    "sex_assigned_at_birth": "Female"
  }
  response = user_session.post(url, json=payload)
  assert response.status_code == 200
  assert "user_id" in response.json()
  user_session_data["user"] = response.json()


@pytest.mark.dependency(depends=["test_recreate_user"])
def test_recreated_user_screening_alerts(base_url: str, user_sub: str, user_session: Session, user_session_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{user_sub}/screening_alert"
  response = user_session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 1
  assert response.json()[0]["notes"] == "The NHS recommends that people with a cervix aged 25-49 have a cervical screening every 3 years."
  assert response.json()[0]["suggested_months_between_appointments"] == 36


@pytest.mark.dependency(depends=[
  "backend_test/test_screening_alert.py::test_list_screening_alerts_after_update",
  "backend_test/test_share.py::test_get_approved_incoming_share",
], scope='session')
def test_user_home(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/home"
  response = session.get(url)
  assert response.status_code == 200
  assert "count_new_user_screening_alerts" in response.json()
  assert response.json()["count_new_user_screening_alerts"] == 2
  assert "count_outgoing_user_shares" in response.json()
  assert response.json()["count_outgoing_user_shares"] == 0


@pytest.mark.dependency()
def test_user_referral(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/referral"
  response = session.get(url)
  assert response.status_code == 200
  assert "referral_code" in response.json()
  assert response.json()["referral_code"] == "seventeen-diffusive-transpose"
  assert "count_user_referrals" in response.json()
  assert response.json()["count_user_referrals"] == 0


@pytest.mark.dependency()
def test_user_impact(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/impact"
  response = session.get(url)
  assert response.status_code == 200
  assert "count_research_projects" in response.json()
  assert "count_research_queries" in response.json()
