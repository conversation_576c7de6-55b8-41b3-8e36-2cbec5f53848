import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_appointment_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/appointment_support_request"
  payload = {
    "sub": sub,
    "appointment_type": "GP visit about tiredness",
    "appointment_details": "I'm feeling tired all the time and I don't know why",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "request_id" in response.json()
  assert "response_output" in response.json()
  shared_data["request_id"] = response.json()["request_id"]


@pytest.mark.dependency(depends=["test_create_appointment_support_request"])
def test_list_appointment_support_requests(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/appointment_support_request"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == 1


@pytest.mark.dependency(depends=["test_create_appointment_support_request"])
def test_get_appointment_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/appointment_support_request/{request_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "request_id" in response.json()
  assert response.json()["request_id"] == request_id


@pytest.mark.dependency(depends=["test_create_appointment_support_request"])
def test_rate_appointment_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/appointment_support_request/{request_id}/rating/3"
  response = session.put(url)
  assert response.status_code == 204


@pytest.mark.dependency(depends=["test_create_appointment_support_request"])
def test_rate_appointment_support_request_out_of_bounds(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/appointment_support_request/{request_id}/rating/9"
  response = session.put(url)
  assert response.status_code == 406


@pytest.mark.dependency(depends=["test_create_appointment_support_request"])
def test_rate_appointment_support_request_improperly(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/appointment_support_request/{request_id}/rating/banana"
  response = session.put(url)
  assert response.status_code == 422


@pytest.mark.dependency(depends=[
  "test_list_appointment_support_requests",
  "test_get_appointment_support_request",
  "test_rate_appointment_support_request",
  "test_rate_appointment_support_request_out_of_bounds",
  "test_rate_appointment_support_request_improperly"
])
def test_delete_appointment_support_request(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  assert "request_id" in shared_data
  request_id = shared_data["request_id"]
  url = f"{base_url}/user/{sub}/appointment_support_request/{request_id}"
  response = session.delete(url)
  assert response.status_code == 200
