import pytest
from requests import Session
from typing import Any


@pytest.fixture(scope="module")
def shared_data() -> dict[str, Any]:
  return {}


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_initial_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/screening"
  response = session.get(url)
  assert response.status_code == 200
  shared_data["initial_screening_count"] = len(response.json())


@pytest.mark.dependency(depends=["test_initial_screening"])
def test_create_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  url = f"{base_url}/user/{sub}/screening"
  payload = {
    "sub": sub,
    "type": "Blood Test",
    "subtype": "Iron",
    "next_date": "2025-02-17",
    "last_date": "null",
    "status": "Requires update",
    "user_managed_schedule": False,
    "notes": "this is a note"
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "screening_id" in response.json()
  assert "label" in response.json()
  assert response.json()["label"] == "Blood Test (Iron)"
  assert response.json()["last_date"] is None
  assert response.json()["notes"] == "this is a note"
  shared_data["screening_id"] = response.json()["screening_id"]


@pytest.mark.dependency(depends=["backend_test/test_user.py::test_recreate_user"], scope='session')
def test_create_screening_invalid_date(base_url: str, sub: str, session: Session) -> None:
  url = f"{base_url}/user/{sub}/screening"
  payload = {
    "sub": sub,
    "type": "Blood Test",
    "subtype": "Iron",
    "next_date": "2025-02-17",
    "last_date": "foo",
    "status": "Not yet attended",
    "user_managed_schedule": False
  }
  response = session.post(url, json=payload)
  assert response.status_code == 422


@pytest.mark.dependency(depends=["test_create_screening"])
def test_list_screenings(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  initial_screening_count = shared_data["initial_screening_count"]
  url = f"{base_url}/user/{sub}/screening"
  response = session.get(url)
  assert response.status_code == 200
  assert len(response.json()) == initial_screening_count + 1


@pytest.mark.dependency(depends=["test_create_screening"])
def test_get_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  screening_id = shared_data["screening_id"]
  url = f"{base_url}/user/{sub}/screening/{screening_id}"
  response = session.get(url)
  assert response.status_code == 200
  assert "screening_id" in response.json()


@pytest.mark.dependency(depends=["test_create_screening"])
def test_update_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  screening_id = shared_data["screening_id"]
  url = f"{base_url}/user/{sub}/screening/{screening_id}"
  payload = {
    "sub": sub,
    "screening_id": screening_id,
    "next_date": "2025-02-27",
    "status": "Not yet attended",
    "notes": "",
    "user_managed_schedule": False
  }
  response = session.patch(url, json=payload)
  assert response.status_code == 200
  assert response.json()["notes"] is None


@pytest.mark.dependency(depends=["test_create_screening"])
def test_attend_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  screening_id = shared_data["screening_id"]
  url = f"{base_url}/user/{sub}/screening/{screening_id}/attended"
  payload = {
    "sub": sub,
    "screening_id": screening_id,
    "attended_date": "2025-02-27",
    "notes": "",
  }
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert response.json()["notes"] is None
  assert response.json()["status"] == "Attended"


@pytest.mark.dependency(depends=[
  "test_list_screenings",
  "test_get_screening",
  "test_update_screening",
  "test_attend_screening",
])
def test_delete_screening(base_url: str, sub: str, session: Session, shared_data: dict[str, Any]) -> None:
  screening_id = shared_data["screening_id"]
  url = f"{base_url}/user/{sub}/screening/{screening_id}"
  response = session.delete(url)
  assert response.status_code == 200
