module "azure" {
  source = "../modules/azure"

  environment        = "production"
  team_portal_domain = "team.inherit.healthcare"
  backend_domain     = "api.inherit.healthcare"
  core_outputs       = data.terraform_remote_state.core.outputs
  token_issuer       = "https://securetoken.google.com/in-h-e-rit-pl978o"
  token_audience     = "in-h-e-rit-pl978o"

  frontend_google_client_id = "318012792699-se5ag0ftm2gu0adipqnnrr79n74hk22t.apps.googleusercontent.com"
}

output "azure" {
  sensitive = true
  value     = module.azure
}
