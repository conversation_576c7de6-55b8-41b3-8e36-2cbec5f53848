{"version": 4, "terraform_version": "1.11.1", "serial": 48, "lineage": "b0f6b2bf-1ba5-5dc5-e73e-ba9eb5f5738a", "outputs": {}, "resources": [{"mode": "data", "type": "azuread_client_config", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/azuread\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "id": "a54262cf-8305-482f-aca1-5fc925e6dabd-04b07795-8ddb-461a-bbee-02f9e1bf7b46-dff6b10f-c991-4f73-8e34-507288c39663", "object_id": "dff6b10f-c991-4f73-8e34-507288c39663", "tenant_id": "a54262cf-8305-482f-aca1-5fc925e6dabd", "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "azuread_application", "name": "github", "provider": "provider[\"registry.terraform.io/hashicorp/azuread\"]", "instances": [{"schema_version": 2, "attributes": {"api": [{"known_client_applications": [], "mapped_claims_enabled": false, "oauth2_permission_scope": [], "requested_access_token_version": 1}], "app_role": [], "app_role_ids": {}, "client_id": "09a6ba5c-7ef7-442f-b9dd-0e14cc863d39", "description": "", "device_only_auth_enabled": false, "disabled_by_microsoft": "", "display_name": "github-actions", "fallback_public_client_enabled": false, "feature_tags": [{"custom_single_sign_on": false, "enterprise": false, "gallery": false, "hide": false}], "group_membership_claims": [], "id": "/applications/25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6", "identifier_uris": [], "logo_image": "", "logo_url": "", "marketing_url": "", "notes": "", "oauth2_permission_scope_ids": {}, "oauth2_post_response_required": false, "object_id": "25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6", "optional_claims": [{"access_token": [], "id_token": [], "saml2_token": []}], "owners": ["dff6b10f-c991-4f73-8e34-507288c39663"], "password": [], "prevent_duplicate_names": false, "privacy_statement_url": "", "public_client": [{"redirect_uris": []}], "publisher_domain": "phoebeplee.onmicrosoft.com", "required_resource_access": [], "service_management_reference": "", "sign_in_audience": "AzureADMyOrg", "single_page_application": [{"redirect_uris": []}], "support_url": "", "tags": [], "template_id": "", "terms_of_service_url": "", "timeouts": null, "web": [{"homepage_url": "", "implicit_grant": [{"access_token_issuance_enabled": false, "id_token_issuance_enabled": false}], "logout_url": "", "redirect_uris": []}]}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************************************", "dependencies": ["data.azuread_client_config.current"]}]}, {"mode": "managed", "type": "azuread_application_federated_identity_credential", "name": "github", "provider": "provider[\"registry.terraform.io/hashicorp/azuread\"]", "instances": [{"index_key": "nonproduction", "schema_version": 0, "attributes": {"application_id": "/applications/25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6", "audiences": ["api://AzureADTokenExchange"], "credential_id": "5e0fb232-3ef0-4b3d-91fe-260d8a6851ba", "description": "", "display_name": "nonproduction", "id": "25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6/federatedIdentityCredential/5e0fb232-3ef0-4b3d-91fe-260d8a6851ba", "issuer": "https://token.actions.githubusercontent.com", "subject": "repo:inherit-healthcare/inherit:environment:nonproduction", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["azuread_application.github", "data.azuread_client_config.current"]}, {"index_key": "production", "schema_version": 0, "attributes": {"application_id": "/applications/25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6", "audiences": ["api://AzureADTokenExchange"], "credential_id": "b871007a-2734-4d5c-8b36-c8e98db770fd", "description": "", "display_name": "production", "id": "25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6/federatedIdentityCredential/b871007a-2734-4d5c-8b36-c8e98db770fd", "issuer": "https://token.actions.githubusercontent.com", "subject": "repo:inherit-healthcare/inherit:environment:production", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["azuread_application.github", "data.azuread_client_config.current"]}, {"index_key": "pull_request", "schema_version": 0, "attributes": {"application_id": "/applications/25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6", "audiences": ["api://AzureADTokenExchange"], "credential_id": "a050a487-bea5-4199-bb25-5be461a1d38a", "description": "", "display_name": "pull_request", "id": "25cc23d5-1df1-4b65-9b0a-1f2e95d5edf6/federatedIdentityCredential/a050a487-bea5-4199-bb25-5be461a1d38a", "issuer": "https://token.actions.githubusercontent.com", "subject": "repo:inherit-healthcare/inherit:pull_request", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["azuread_application.github", "data.azuread_client_config.current"]}]}, {"mode": "managed", "type": "azuread_service_principal", "name": "github", "provider": "provider[\"registry.terraform.io/hashicorp/azuread\"]", "instances": [{"schema_version": 1, "attributes": {"account_enabled": true, "alternative_names": [], "app_role_assignment_required": false, "app_role_ids": {}, "app_roles": [], "application_tenant_id": "a54262cf-8305-482f-aca1-5fc925e6dabd", "client_id": "09a6ba5c-7ef7-442f-b9dd-0e14cc863d39", "description": "", "display_name": "github-actions", "feature_tags": [{"custom_single_sign_on": false, "enterprise": false, "gallery": false, "hide": false}], "features": [{"custom_single_sign_on_app": false, "enterprise_application": false, "gallery_application": false, "visible_to_users": true}], "homepage_url": "", "id": "/servicePrincipals/07fc8a97-1808-46ff-ac27-7f948cb08b5e", "login_url": "", "logout_url": "", "notes": "", "notification_email_addresses": [], "oauth2_permission_scope_ids": {}, "oauth2_permission_scopes": [], "object_id": "07fc8a97-1808-46ff-ac27-7f948cb08b5e", "owners": ["dff6b10f-c991-4f73-8e34-507288c39663"], "preferred_single_sign_on_mode": "", "redirect_uris": [], "saml_metadata_url": "", "saml_single_sign_on": [{"relay_state": ""}], "service_principal_names": [], "sign_in_audience": "AzureADMyOrg", "tags": [], "timeouts": null, "type": "Application", "use_existing": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************************************", "dependencies": ["azuread_application.github", "data.azuread_client_config.current"]}]}, {"mode": "managed", "type": "azurerm_resource_group", "name": "terraform", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/terraform", "location": "uksouth", "managed_by": "", "name": "terraform", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo1NDAwMDAwMDAwMDAwLCJkZWxldGUiOjU0MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}, {"mode": "managed", "type": "azurerm_role_assignment", "name": "example", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"condition": "", "condition_version": "", "delegated_managed_identity_resource_id": "", "description": "", "id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/providers/Microsoft.Authorization/roleAssignments/cc6d5c43-d13d-1f6e-92c3-28b6f9d16bf3", "name": "cc6d5c43-d13d-1f6e-92c3-28b6f9d16bf3", "principal_id": "07fc8a97-1808-46ff-ac27-7f948cb08b5e", "principal_type": "ServicePrincipal", "role_definition_id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c", "role_definition_name": "Contributor", "scope": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054", "skip_service_principal_aad_check": null, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["azuread_application.github", "azuread_service_principal.github", "data.azuread_client_config.current"]}]}, {"mode": "managed", "type": "azurerm_storage_account", "name": "terraform", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "GRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": false, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "https_traffic_only_enabled": true, "id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/terraform/providers/Microsoft.Storage/storageAccounts/terraformot7xt8538juj", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": false, "local_user_enabled": true, "location": "uksouth", "min_tls_version": "TLS1_2", "name": "terraformot7xt8538juj", "network_rules": [], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://terraformot7xt8538juj.blob.core.windows.net/;AccountName=terraformot7xt8538juj;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://terraformot7xt8538juj.blob.core.windows.net/", "primary_blob_host": "terraformot7xt8538juj.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=terraformot7xt8538juj;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://terraformot7xt8538juj.dfs.core.windows.net/", "primary_dfs_host": "terraformot7xt8538juj.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://terraformot7xt8538juj.file.core.windows.net/", "primary_file_host": "terraformot7xt8538juj.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "uksouth", "primary_queue_endpoint": "https://terraformot7xt8538juj.queue.core.windows.net/", "primary_queue_host": "terraformot7xt8538juj.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://terraformot7xt8538juj.table.core.windows.net/", "primary_table_host": "terraformot7xt8538juj.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://terraformot7xt8538juj.z33.web.core.windows.net/", "primary_web_host": "terraformot7xt8538juj.z33.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": true, "include_apis": true, "retention_policy_days": 7, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "terraform", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": "", "secondary_blob_host": "", "secondary_blob_internet_endpoint": "", "secondary_blob_internet_host": "", "secondary_blob_microsoft_endpoint": "", "secondary_blob_microsoft_host": "", "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=terraformot7xt8538juj;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": "", "secondary_dfs_host": "", "secondary_dfs_internet_endpoint": "", "secondary_dfs_internet_host": "", "secondary_dfs_microsoft_endpoint": "", "secondary_dfs_microsoft_host": "", "secondary_file_endpoint": "", "secondary_file_host": "", "secondary_file_internet_endpoint": "", "secondary_file_internet_host": "", "secondary_file_microsoft_endpoint": "", "secondary_file_microsoft_host": "", "secondary_location": "ukwest", "secondary_queue_endpoint": "", "secondary_queue_host": "", "secondary_queue_microsoft_endpoint": "", "secondary_queue_microsoft_host": "", "secondary_table_endpoint": "", "secondary_table_host": "", "secondary_table_microsoft_endpoint": "", "secondary_table_microsoft_host": "", "secondary_web_endpoint": "", "secondary_web_host": "", "secondary_web_internet_endpoint": "", "secondary_web_internet_host": "", "secondary_web_microsoft_endpoint": "", "secondary_web_microsoft_host": "", "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {}, "timeouts": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "primary_connection_string"}], [{"type": "get_attr", "value": "secondary_access_key"}], [{"type": "get_attr", "value": "primary_access_key"}], [{"type": "get_attr", "value": "primary_blob_connection_string"}], [{"type": "get_attr", "value": "secondary_connection_string"}], [{"type": "get_attr", "value": "secondary_blob_connection_string"}], [{"type": "get_attr", "value": "secondary_access_key"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.terraform"]}]}, {"mode": "managed", "type": "azurerm_storage_container", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"container_access_type": "private", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/terraform/providers/Microsoft.Storage/storageAccounts/terraformot7xt8538juj/blobServices/default/containers/state", "metadata": {}, "name": "state", "resource_manager_id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/terraform/providers/Microsoft.Storage/storageAccounts/terraformot7xt8538juj/blobServices/default/containers/state", "storage_account_id": "/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/terraform/providers/Microsoft.Storage/storageAccounts/terraformot7xt8538juj", "storage_account_name": "", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.terraform", "azurerm_storage_account.terraform"]}]}, {"mode": "managed", "type": "github_actions_secret", "name": "azure_client_id", "provider": "provider[\"registry.terraform.io/integrations/github\"]", "instances": [{"schema_version": 0, "attributes": {"created_at": "2025-01-30 16:08:21 +0000 UTC", "encrypted_value": "", "id": "inherit:AZURE_CLIENT_ID", "plaintext_value": "09a6ba5c-7ef7-442f-b9dd-0e14cc863d39", "repository": "inherit", "secret_name": "AZURE_CLIENT_ID", "updated_at": "2025-01-30 16:08:21 +0000 UTC"}, "sensitive_attributes": [[{"type": "get_attr", "value": "plaintext_value"}], [{"type": "get_attr", "value": "encrypted_value"}]], "private": "bnVsbA==", "dependencies": ["azuread_application.github", "data.azuread_client_config.current"]}]}, {"mode": "managed", "type": "github_actions_secret", "name": "azure_subscription_id", "provider": "provider[\"registry.terraform.io/integrations/github\"]", "instances": [{"schema_version": 0, "attributes": {"created_at": "2025-01-30 16:08:19 +0000 UTC", "encrypted_value": "", "id": "inherit:AZURE_SUBSCRIPTION_ID", "plaintext_value": "adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054", "repository": "inherit", "secret_name": "AZURE_SUBSCRIPTION_ID", "updated_at": "2025-01-30 16:08:19 +0000 UTC"}, "sensitive_attributes": [[{"type": "get_attr", "value": "encrypted_value"}], [{"type": "get_attr", "value": "plaintext_value"}]], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "github_actions_secret", "name": "azure_tenant_id", "provider": "provider[\"registry.terraform.io/integrations/github\"]", "instances": [{"schema_version": 0, "attributes": {"created_at": "2025-01-30 16:08:22 +0000 UTC", "encrypted_value": "", "id": "inherit:AZURE_TENANT_ID", "plaintext_value": "a54262cf-8305-482f-aca1-5fc925e6dabd", "repository": "inherit", "secret_name": "AZURE_TENANT_ID", "updated_at": "2025-01-30 16:08:22 +0000 UTC"}, "sensitive_attributes": [[{"type": "get_attr", "value": "encrypted_value"}], [{"type": "get_attr", "value": "plaintext_value"}]], "private": "bnVsbA==", "dependencies": ["data.azuread_client_config.current"]}]}], "check_results": null}