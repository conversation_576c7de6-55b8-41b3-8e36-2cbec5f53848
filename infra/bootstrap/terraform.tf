terraform {
  backend "local" {}

  required_providers {
    azuread = {
      source  = "hashicorp/azuread"
      version = "3.1.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.22.0"
    }
    github = {
      source  = "integrations/github"
      version = "6.5.0"
    }
  }
}

provider "azuread" {
  tenant_id = local.tenant_id
}

provider "azurerm" {
  features {}

  resource_providers_to_register = [
    "Microsoft.App",
    "Microsoft.Communication",
  ]

  tenant_id       = local.tenant_id
  subscription_id = local.subscription_id
}

provider "github" {
  owner = local.organization
}
