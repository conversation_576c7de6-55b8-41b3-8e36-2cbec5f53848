locals {
  tenant_id       = "a54262cf-8305-482f-aca1-5fc925e6dabd"
  subscription_id = "adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054"
  organization    = "inherit-healthcare"
  repository      = "inherit"
}

data "azuread_client_config" "current" {}

resource "azuread_application" "github" {
  display_name = "github-actions"
  owners       = [data.azuread_client_config.current.object_id]
}

resource "azuread_service_principal" "github" {
  client_id                    = azuread_application.github.client_id
  app_role_assignment_required = false
  owners                       = [data.azuread_client_config.current.object_id]
}

resource "github_actions_secret" "azure_tenant_id" {
  repository      = local.repository
  secret_name     = "AZURE_TENANT_ID"
  plaintext_value = data.azuread_client_config.current.tenant_id
}

resource "github_actions_secret" "azure_subscription_id" {
  repository      = local.repository
  secret_name     = "AZURE_SUBSCRIPTION_ID"
  plaintext_value = local.subscription_id
}

resource "github_actions_secret" "azure_client_id" {
  repository      = local.repository
  secret_name     = "AZURE_CLIENT_ID"
  plaintext_value = azuread_application.github.client_id
}

resource "azurerm_role_assignment" "example" {
  scope                = "/subscriptions/${local.subscription_id}"
  role_definition_name = "Contributor"
  principal_id         = azuread_service_principal.github.object_id
}

resource "azuread_application_federated_identity_credential" "github" {
  for_each = {
    nonproduction = "repo:${local.organization}/${local.repository}:environment:nonproduction",
    production    = "repo:${local.organization}/${local.repository}:environment:production",
    pull_request  = "repo:${local.organization}/${local.repository}:pull_request" #TODO use a limited account (only needs the cognitive services key)
  }

  application_id = azuread_application.github.id
  display_name   = each.key
  audiences      = ["api://AzureADTokenExchange"]
  issuer         = "https://token.actions.githubusercontent.com"
  subject        = each.value
}

resource "azurerm_resource_group" "terraform" {
  name     = "terraform"
  location = "UK South"
}

resource "azurerm_storage_account" "terraform" {
  name                     = "terraformot7xt8538juj"
  resource_group_name      = azurerm_resource_group.terraform.name
  location                 = azurerm_resource_group.terraform.location
  account_tier             = "Standard"
  account_replication_type = "GRS"
}

resource "azurerm_storage_container" "terraform_state" {
  name                  = "state"
  storage_account_id    = azurerm_storage_account.terraform.id
  container_access_type = "private"
}
