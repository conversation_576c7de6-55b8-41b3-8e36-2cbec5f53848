resource "azurerm_resource_group" "backend" {
  name     = "development-backend"
  location = "UK South"
}

resource "azurerm_storage_account" "main" {
  name                     = "inheritdevelopment"
  resource_group_name      = azurerm_resource_group.backend.name
  location                 = azurerm_resource_group.backend.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
}

resource "azurerm_storage_container" "userdocs" {
  name                  = "userdocs"
  storage_account_id    = azurerm_storage_account.main.id
  container_access_type = "private"
}

resource "azurerm_storage_queue" "userdocs" {
  name                 = "userdocs"
  storage_account_name = azurerm_storage_account.main.name
}

resource "azurerm_storage_queue" "instagramcheck" {
  name                 = "instagramcheck"
  storage_account_name = azurerm_storage_account.main.name
}

resource "azurerm_storage_queue" "usersharechange" {
  name                 = "usersharechange"
  storage_account_name = azurerm_storage_account.main.name
}
