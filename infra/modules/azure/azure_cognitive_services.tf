resource "azurerm_cognitive_account" "openai" {
  name                = "${var.environment}-openai"
  location            = azurerm_resource_group.backend.location
  resource_group_name = azurerm_resource_group.backend.name

  kind     = "OpenAI"
  sku_name = "S0"

  custom_subdomain_name         = "inherit-${var.environment}-openai"
  public_network_access_enabled = true
  local_auth_enabled            = true
}

resource "azurerm_cognitive_deployment" "openai_gpt_4o_mini" {
  name                 = "${var.environment}-openai-gpt-4o-mini"
  cognitive_account_id = azurerm_cognitive_account.openai.id

  model {
    format  = "OpenAI"
    name    = "gpt-4o-mini"
    version = "2024-07-18"
  }

  sku {
    name     = "GlobalStandard"
    capacity = 100
  }
}

resource "azurerm_cognitive_account" "document_intelligence" {
  name                = "${var.environment}-document-intelligence"
  location            = azurerm_resource_group.backend.location
  resource_group_name = azurerm_resource_group.backend.name

  kind     = "FormRecognizer"
  sku_name = "S0"

  custom_subdomain_name         = "inherit-${var.environment}-document-intelligence"
  public_network_access_enabled = true
  local_auth_enabled            = true
}
