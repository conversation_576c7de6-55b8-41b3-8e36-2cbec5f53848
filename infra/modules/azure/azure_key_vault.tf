# Key vault references from core workspace
locals {
  keyvaults = {
    backend  = var.core_outputs.keyvaults[var.environment].backend
    worker   = var.core_outputs.keyvaults[var.environment].worker
    frontend = var.core_outputs.keyvaults[var.environment].frontend
  }
}

data "azurerm_client_config" "current" {}

resource "azurerm_key_vault_access_policy" "container_app_backend" {
  key_vault_id = local.keyvaults.backend
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_user_assigned_identity.container_app_backend.principal_id

  secret_permissions = ["Get"]
}

resource "azurerm_key_vault_access_policy" "container_app_worker" {
  key_vault_id = local.keyvaults.worker
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_user_assigned_identity.container_app_worker.principal_id

  secret_permissions = ["Get"]
}

resource "azurerm_key_vault_access_policy" "container_app_frontend" {
  key_vault_id = local.keyvaults.frontend
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_user_assigned_identity.container_app_frontend.principal_id

  secret_permissions = ["Get"]
}

# Secrets

resource "random_password" "backend_db_server_password" {
  length  = 32
  special = false
}
resource "azurerm_key_vault_secret" "backend_db_server_password" {
  for_each = toset(["backend", "worker", "frontend"])

  name         = "backend-db-server-password"
  value        = random_password.backend_db_server_password.result
  key_vault_id = local.keyvaults[each.key]
}

resource "random_password" "backend_token_secret" {
  length  = 32
  special = false
}
resource "azurerm_key_vault_secret" "backend_token_secret" {
  name         = "backend-token-secret"
  value        = random_password.backend_token_secret.result
  key_vault_id = local.keyvaults.backend
}

resource "random_password" "docs_password" {
  length  = 12
  special = true
}
resource "azurerm_key_vault_secret" "docs_password" {
  name         = "docs-password"
  value        = random_password.docs_password.result
  key_vault_id = local.keyvaults.backend
}

resource "random_password" "frontend_superuser_password" {
  length  = 32
  special = true
}
resource "azurerm_key_vault_secret" "frontend_superuser_password" {
  name         = "frontend-superuser-password"
  value        = random_password.frontend_superuser_password.result
  key_vault_id = local.keyvaults.frontend
}

resource "random_password" "frontend_secret" {
  length  = 32
  special = false
}
resource "azurerm_key_vault_secret" "frontend_secret" {
  name         = "frontend-secret"
  value        = random_password.frontend_secret.result
  key_vault_id = local.keyvaults.frontend
}

resource "random_password" "frontend_db_server_password" {
  length  = 32
  special = false
}
resource "azurerm_key_vault_secret" "frontend_db_server_password" {
  name         = "frontend-db-server-password"
  value        = random_password.frontend_db_server_password.result
  key_vault_id = local.keyvaults.frontend
}


resource "azurerm_key_vault_secret" "openai_primary_access_key" {
  for_each = toset(["backend", "worker", "frontend"])

  name         = "openai-primary-access-key"
  value        = azurerm_cognitive_account.openai.primary_access_key
  key_vault_id = local.keyvaults[each.key]
}
resource "azurerm_key_vault_secret" "openai_secondary_access_key" {
  for_each = toset(["backend", "worker", "frontend"])

  name         = "openai-secondary-access-key"
  value        = azurerm_cognitive_account.openai.secondary_access_key
  key_vault_id = local.keyvaults[each.key]
}

resource "azurerm_key_vault_secret" "storage_account_main_primary_access_key" {
  for_each = toset(["backend", "worker"])

  name         = "storage-account-main-primary-access-key"
  value        = azurerm_storage_account.main.primary_access_key
  key_vault_id = local.keyvaults[each.key]
}
resource "azurerm_key_vault_secret" "storage_account_main_secondary_access_key" {
  for_each = toset(["backend", "worker"])

  name         = "storage-account-main-secondary-access-key"
  value        = azurerm_storage_account.main.secondary_access_key
  key_vault_id = local.keyvaults[each.key]
}
resource "azurerm_key_vault_secret" "storage_account_main_primary_connection_string" {
  for_each = toset(["backend", "worker"])

  name         = "storage-account-main-primary-connection-string"
  value        = azurerm_storage_account.main.primary_connection_string
  key_vault_id = local.keyvaults[each.key]
}
resource "azurerm_key_vault_secret" "storage_account_main_secondary_connection_string" {
  for_each = toset(["backend", "worker"])

  name         = "storage-account-main-secondary-connection-string"
  value        = azurerm_storage_account.main.secondary_connection_string
  key_vault_id = local.keyvaults[each.key]
}

resource "random_password" "recaptcha_private_key" {
  length = 40
}
resource "azurerm_key_vault_secret" "recaptcha_private_key" {
  name         = "recaptcha-private-key"
  value        = random_password.recaptcha_private_key.result
  key_vault_id = local.keyvaults.frontend
}

resource "random_password" "recaptcha_public_key" {
  length = 40
}
resource "azurerm_key_vault_secret" "recaptcha_public_key" {
  name         = "recaptcha-public-key"
  value        = random_password.recaptcha_public_key.result
  key_vault_id = local.keyvaults.frontend
}

resource "azurerm_key_vault_secret" "document_intelligence_primary_access_key" {
  name         = "document-intelligence-primary-access-key"
  value        = azurerm_cognitive_account.document_intelligence.primary_access_key
  key_vault_id = local.keyvaults.worker
}
resource "azurerm_key_vault_secret" "document_intelligence_secondary_access_key" {
  name         = "document-intelligence-secondary-access-key"
  value        = azurerm_cognitive_account.document_intelligence.secondary_access_key
  key_vault_id = local.keyvaults.worker
}


resource "azurerm_key_vault_secret" "communication_service_primary_connection_string" {
  name         = "communication-service-primary-connection-string"
  value        = var.core_outputs.communication_service.primary_connection_string
  key_vault_id = local.keyvaults.backend
}
resource "azurerm_key_vault_secret" "communication_service_secondary_connection_string" {
  name         = "communication-service-secondary-connection-string"
  value        = var.core_outputs.communication_service.secondary_connection_string
  key_vault_id = local.keyvaults.backend
}

# Secrets from outside Azure are set manually in the key vaults
data "azurerm_key_vault_secret" "frontend_google_client_secret" {
  name         = "google-client-secret"
  key_vault_id = local.keyvaults.frontend
}
