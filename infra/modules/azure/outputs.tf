output "backend_postgres_hostname" {
  value = azurerm_postgresql_flexible_server.backend.fqdn
}

output "backend_postgres_database" {
  value = azurerm_postgresql_flexible_server_database.backend.name
}

output "backend_postgres_username" {
  value = azurerm_postgresql_flexible_server.backend.administrator_login
}

output "backend_postgres_password" {
  value = azurerm_postgresql_flexible_server.backend.administrator_password
}

output "backend_resource_group" {
  value = azurerm_resource_group.backend.name
}

output "backend_postgres_server" {
  value = azurerm_postgresql_flexible_server.backend.name
}

output "docs_password" {
  value = random_password.docs_password.result
}

output "openai_primary_access_key" {
  value = azurerm_cognitive_account.openai.primary_access_key
}

output "openai_endpoint" {
  value = azurerm_cognitive_account.openai.endpoint
}

output "openai_model" {
  value = azurerm_cognitive_deployment.openai_gpt_4o_mini.name
}

output "storage_account_primary_access_key" {
  value = azurerm_storage_account.main.primary_access_key
}

output "storage_account_primary_connection_string" {
  value = azurerm_storage_account.main.primary_connection_string
}

output "storage_account_name" {
  value = azurerm_storage_account.main.name
}

output "storage_container_userdocs_name" {
  value = azurerm_storage_container.userdocs.name
}

output "storage_queue_userdocs_name" {
  value = azurerm_storage_queue.userdocs.name
}

output "storage_queue_instagramcheck_name" {
  value = azurerm_storage_queue.instagramcheck.name
}

output "document_intelligence_primary_access_key" {
  value = azurerm_cognitive_account.document_intelligence.primary_access_key
}

output "document_intelligence_endpoint" {
  value = azurerm_cognitive_account.document_intelligence.endpoint
}

output "frontend_postgres_hostname" {
  value = azurerm_postgresql_flexible_server.frontend.fqdn
}

output "frontend_postgres_database" {
  value = azurerm_postgresql_flexible_server_database.frontend.name
}

output "frontend_postgres_username" {
  value = azurerm_postgresql_flexible_server.frontend.administrator_login
}

output "frontend_postgres_password" {
  value = azurerm_postgresql_flexible_server.frontend.administrator_password
}

output "frontend_resource_group" {
  value = azurerm_resource_group.frontend.name
}

output "frontend_postgres_server" {
  value = azurerm_postgresql_flexible_server.frontend.name
}

output "frontend_superuser_password" {
  value = random_password.frontend_superuser_password.result
}

output "communication_connection_string" {
  value = var.core_outputs.communication_service.primary_connection_string
}
