locals {
  postgres_username = "psqladmin"
}

resource "azurerm_postgresql_flexible_server" "backend" {
  name                          = "${var.environment}-backend-db-server"
  resource_group_name           = azurerm_resource_group.backend.name
  location                      = azurerm_resource_group.backend.location
  version                       = "16"
  public_network_access_enabled = true
  administrator_login           = local.postgres_username
  administrator_password        = random_password.backend_db_server_password.result

  storage_mb   = 32768
  storage_tier = "P4"
  sku_name     = "B_Standard_B1ms"

  #TODO high availability becomes available in D or E series

  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      zone,
    ]
  }
}

resource "azurerm_postgresql_flexible_server_database" "backend" {
  name      = "${var.environment}-backend-db"
  server_id = azurerm_postgresql_flexible_server.backend.id
  collation = "en_US.utf8"
  charset   = "utf8"

  lifecycle {
    prevent_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_firewall_rule" "backend" {
  for_each = toset(concat(
    azurerm_container_app.backend.outbound_ip_addresses,
    azurerm_container_app.worker.outbound_ip_addresses,
    azurerm_container_app.frontend.outbound_ip_addresses,
  ))

  name             = "allow-${replace(each.key, ".", "-")}"
  server_id        = azurerm_postgresql_flexible_server.backend.id
  start_ip_address = each.key
  end_ip_address   = each.key
}

resource "azurerm_postgresql_flexible_server" "frontend" {
  name                          = "${var.environment}-frontend-db-server"
  resource_group_name           = azurerm_resource_group.frontend.name
  location                      = azurerm_resource_group.frontend.location
  version                       = "16"
  public_network_access_enabled = true
  administrator_login           = local.postgres_username
  administrator_password        = random_password.frontend_db_server_password.result

  storage_mb   = 32768
  storage_tier = "P4"
  sku_name     = "B_Standard_B1ms"

  #TODO high availability becomes available in D or E series

  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      zone,
    ]
  }
}

resource "azurerm_postgresql_flexible_server_database" "frontend" {
  name      = "${var.environment}-frontend-db"
  server_id = azurerm_postgresql_flexible_server.frontend.id
  collation = "en_US.utf8"
  charset   = "utf8"

  lifecycle {
    prevent_destroy = true
  }
}

resource "azurerm_postgresql_flexible_server_firewall_rule" "frontend" {
  for_each = toset(azurerm_container_app.frontend.outbound_ip_addresses)

  name             = "allow-${replace(each.key, ".", "-")}"
  server_id        = azurerm_postgresql_flexible_server.frontend.id
  start_ip_address = each.key
  end_ip_address   = each.key
}

resource "azurerm_postgresql_flexible_server_configuration" "backend_pg_qs_query_capture_mode" {
  name      = "pg_qs.query_capture_mode"
  server_id = azurerm_postgresql_flexible_server.backend.id
  value     = "top"
}

resource "azurerm_postgresql_flexible_server_configuration" "backend_pgms_wait_sampling_query_capture_mode" {
  name      = "pgms_wait_sampling.query_capture_mode"
  server_id = azurerm_postgresql_flexible_server.backend.id
  value     = "all"
}

resource "azurerm_postgresql_flexible_server_configuration" "backend_track_io_timing" {
  name      = "track_io_timing"
  server_id = azurerm_postgresql_flexible_server.backend.id
  value     = "on"
}

resource "azurerm_monitor_diagnostic_setting" "backend_db_postgresql_flexible_server_metrics" {
  name                       = "${azurerm_postgresql_flexible_server.backend.name}-all-metrics"
  target_resource_id         = azurerm_postgresql_flexible_server.backend.id
  log_analytics_workspace_id = azurerm_log_analytics_workspace.backend.id

  enabled_log {
    category = "PostgreSQLFlexSessions"
  }

  enabled_log {
    category = "PostgreSQLFlexQueryStoreRuntime"
  }

  enabled_log {
    category = "PostgreSQLFlexQueryStoreWaitStats"
  }

  metric {
    category = "AllMetrics"
  }
}
