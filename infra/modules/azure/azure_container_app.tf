resource "azurerm_log_analytics_workspace" "backend" {
  name                = "${var.environment}-backend-api"
  location            = azurerm_resource_group.backend.location
  resource_group_name = azurerm_resource_group.backend.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_container_app_environment" "backend" {
  name                       = "${var.environment}-backend-api"
  location                   = azurerm_resource_group.backend.location
  resource_group_name        = azurerm_resource_group.backend.name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.backend.id
}

resource "azurerm_user_assigned_identity" "container_app_backend" {
  name                = "${var.environment}-backend-api"
  location            = azurerm_resource_group.backend.location
  resource_group_name = azurerm_resource_group.backend.name
}

resource "azurerm_container_app" "backend" {
  name                         = "${var.environment}-backend-api"
  container_app_environment_id = azurerm_container_app_environment.backend.id
  resource_group_name          = azurerm_resource_group.backend.name
  revision_mode                = "Single"

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app_backend.id]
  }

  registry {
    server   = var.core_outputs.acr.login_server
    identity = azurerm_user_assigned_identity.container_app_backend.id
  }

  secret {
    name                = "backend-db-server-password"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.backend_db_server_password["backend"].id
  }

  secret {
    name                = "backend-token-secret"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.backend_token_secret.id
  }

  secret {
    name                = "docs-password"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.docs_password.id
  }

  secret {
    name                = "openai-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.openai_primary_access_key["backend"].id
  }

  secret {
    name                = "storage-account-main-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.storage_account_main_primary_access_key["backend"].id
  }

  secret {
    name                = "communication-service-primary-connection-string"
    identity            = azurerm_user_assigned_identity.container_app_backend.id
    key_vault_secret_id = azurerm_key_vault_secret.communication_service_primary_connection_string.id
  }

  template {
    min_replicas = 1

    container {
      name   = "backend-api"
      image  = "nginx:latest"
      cpu    = 0.25
      memory = "0.5Gi"
      env {
        name        = "SECRET_KEY"
        secret_name = "backend-token-secret"
      }
      env {
        name  = "POSTGRES_HOST"
        value = azurerm_postgresql_flexible_server.backend.fqdn
      }
      env {
        name  = "POSTGRES_USERNAME"
        value = local.postgres_username
      }
      env {
        name        = "POSTGRES_PASSWORD"
        secret_name = "backend-db-server-password"
      }
      env {
        name  = "POSTGRES_DATABASE"
        value = azurerm_postgresql_flexible_server_database.backend.name
      }
      env {
        name  = "POSTGRES_SSLMODE"
        value = "require"
      }
      env {
        name  = "DOCS_USERNAME"
        value = "docs"
      }
      env {
        name        = "DOCS_PASSWORD"
        secret_name = "docs-password"
      }
      env {
        name        = "AZURE_OPENAI_API_KEY"
        secret_name = "openai-primary-access-key"
      }
      env {
        name  = "AZURE_OPENAI_ENDPOINT"
        value = azurerm_cognitive_account.openai.endpoint
      }
      env {
        name  = "AZURE_OPENAI_MODEL"
        value = azurerm_cognitive_deployment.openai_gpt_4o_mini.name
      }
      env {
        name        = "AZURE_STORAGE_ACCOUNT_ACCESS_KEY"
        secret_name = "storage-account-main-primary-access-key"
      }
      env {
        name  = "AZURE_STORAGE_ACCOUNT_NAME"
        value = azurerm_storage_account.main.name
      }
      env {
        name  = "AZURE_STORAGE_CONTAINER_USERDOCS_NAME"
        value = azurerm_storage_container.userdocs.name
      }
      env {
        name  = "AZURE_STORAGE_QUEUE_USERDOCS_NAME"
        value = azurerm_storage_queue.userdocs.name
      }
      env {
        name  = "AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME"
        value = azurerm_storage_queue.instagramcheck.name
      }
      env {
        name  = "API_BASE_URL"
        value = "https://${var.backend_domain}"
      }
      env {
        name        = "AZURE_COMMUNICATION_CONNECTION_STRING"
        secret_name = "communication-service-primary-connection-string"
      }
      env {
        name  = "TOKEN_ISSUER"
        value = var.token_issuer
      }
      env {
        name  = "TOKEN_AUDIENCE"
        value = var.token_audience
      }
    }
  }

  ingress {
    target_port      = 8000
    external_enabled = true

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  lifecycle {
    ignore_changes = [
      template[0].container[0].image,
    ]
  }
}

resource "azurerm_role_assignment" "backend_acr_pull" {
  scope                = var.core_outputs.acr.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.container_app_backend.principal_id
}

resource "azurerm_container_app_custom_domain" "backend" {
  name                     = var.backend_domain
  container_app_id         = azurerm_container_app.backend.id
  certificate_binding_type = "SniEnabled"

  lifecycle {
    ignore_changes = [
      container_app_environment_certificate_id,
      certificate_binding_type
    ]
  }
}

resource "azurerm_user_assigned_identity" "container_app_worker" {
  name                = "${var.environment}-worker"
  location            = azurerm_resource_group.backend.location
  resource_group_name = azurerm_resource_group.backend.name
}

resource "azurerm_container_app" "worker" {
  name                         = "${var.environment}-worker"
  container_app_environment_id = azurerm_container_app_environment.backend.id
  resource_group_name          = azurerm_resource_group.backend.name
  revision_mode                = "Single"

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app_worker.id]
  }

  registry {
    server   = var.core_outputs.acr.login_server
    identity = azurerm_user_assigned_identity.container_app_worker.id
  }

  secret {
    name                = "backend-db-server-password"
    identity            = azurerm_user_assigned_identity.container_app_worker.id
    key_vault_secret_id = azurerm_key_vault_secret.backend_db_server_password["worker"].id
  }

  secret {
    name                = "storage-account-main-primary-connection-string"
    identity            = azurerm_user_assigned_identity.container_app_worker.id
    key_vault_secret_id = azurerm_key_vault_secret.storage_account_main_primary_connection_string["worker"].id
  }

  secret {
    name                = "storage-account-main-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_worker.id
    key_vault_secret_id = azurerm_key_vault_secret.storage_account_main_primary_access_key["worker"].id
  }

  secret {
    name                = "document-intelligence-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_worker.id
    key_vault_secret_id = azurerm_key_vault_secret.document_intelligence_primary_access_key.id
  }

  secret {
    name                = "openai-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_worker.id
    key_vault_secret_id = azurerm_key_vault_secret.openai_primary_access_key["worker"].id
  }

  template {
    container {
      name   = "worker"
      image  = "nginx:latest"
      cpu    = 0.25
      memory = "0.5Gi"
      env {
        name  = "POSTGRES_HOST"
        value = azurerm_postgresql_flexible_server.backend.fqdn
      }
      env {
        name  = "POSTGRES_USERNAME"
        value = local.postgres_username
      }
      env {
        name        = "POSTGRES_PASSWORD"
        secret_name = "backend-db-server-password"
      }
      env {
        name  = "POSTGRES_DATABASE"
        value = azurerm_postgresql_flexible_server_database.backend.name
      }
      env {
        name  = "POSTGRES_SSLMODE"
        value = "require"
      }
      env {
        name        = "AZURE_STORAGE_CONNECTION_STRING"
        secret_name = "storage-account-main-primary-connection-string"
      }
      env {
        name  = "AZURE_STORAGE_ACCOUNT_NAME"
        value = azurerm_storage_account.main.name
      }
      env {
        name        = "AZURE_STORAGE_ACCOUNT_ACCESS_KEY"
        secret_name = "storage-account-main-primary-access-key"
      }
      env {
        name  = "AZURE_STORAGE_CONTAINER_USERDOCS_NAME"
        value = azurerm_storage_container.userdocs.name
      }
      env {
        name  = "AZURE_STORAGE_QUEUE_USERDOCS_NAME"
        value = azurerm_storage_queue.userdocs.name
      }
      env {
        name  = "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT"
        value = azurerm_cognitive_account.document_intelligence.endpoint
      }
      env {
        name        = "AZURE_DOCUMENT_INTELLIGENCE_API_KEY"
        secret_name = "document-intelligence-primary-access-key"
      }
      env {
        name  = "AZURE_OPENAI_ENDPOINT"
        value = azurerm_cognitive_account.openai.endpoint
      }
      env {
        name        = "AZURE_OPENAI_API_KEY"
        secret_name = "openai-primary-access-key"
      }
      env {
        name  = "AZURE_OPENAI_MODEL"
        value = azurerm_cognitive_deployment.openai_gpt_4o_mini.name
      }
      env {
        name        = "AzureWebJobsStorage"
        secret_name = "storage-account-main-primary-connection-string"
      }
      /*
      env {
        name  = "REDIS_HOST"
        value = "${azurerm_container_app.redis.name}"
      }
      env {
        name  = "REDIS_PORT"
        value = "6379"
      }
      */
    }
  }

  lifecycle {
    ignore_changes = [
      template[0].container[0].image,
    ]
  }
}

resource "azurerm_role_assignment" "worker_acr_pull" {
  scope                = var.core_outputs.acr.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.container_app_worker.principal_id
}
resource "azurerm_log_analytics_workspace" "frontend" {
  name                = "${var.environment}-frontend"
  location            = azurerm_resource_group.frontend.location
  resource_group_name = azurerm_resource_group.frontend.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_container_app_environment" "frontend" {
  name                       = "${var.environment}-frontend"
  location                   = azurerm_resource_group.frontend.location
  resource_group_name        = azurerm_resource_group.frontend.name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.frontend.id
}

resource "azurerm_user_assigned_identity" "container_app_frontend" {
  name                = "${var.environment}-frontend"
  location            = azurerm_resource_group.frontend.location
  resource_group_name = azurerm_resource_group.frontend.name
}

resource "azurerm_container_app" "frontend" {
  name                         = "${var.environment}-frontend"
  container_app_environment_id = azurerm_container_app_environment.frontend.id
  resource_group_name          = azurerm_resource_group.frontend.name
  revision_mode                = "Single"

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app_frontend.id]
  }

  registry {
    server   = var.core_outputs.acr.login_server
    identity = azurerm_user_assigned_identity.container_app_frontend.id
  }

  secret {
    name                = "frontend-db-server-password"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.frontend_db_server_password.id
  }

  secret {
    name                = "backend-db-server-password"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.backend_db_server_password["frontend"].id
  }

  secret {
    name                = "frontend-secret"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.frontend_secret.id
  }

  secret {
    name                = "frontend-superuser-password"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.frontend_superuser_password.id
  }

  secret {
    name                = "recaptcha-private-key"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.recaptcha_private_key.id
  }

  secret {
    name                = "recaptcha-public-key"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.recaptcha_public_key.id
  }

  secret {
    name                = "openai-primary-access-key"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = azurerm_key_vault_secret.openai_primary_access_key["frontend"].id
  }

  secret {
    name                = "frontend-google-client-secret"
    identity            = azurerm_user_assigned_identity.container_app_frontend.id
    key_vault_secret_id = data.azurerm_key_vault_secret.frontend_google_client_secret.id
  }

  template {
    container {
      name   = "frontend"
      image  = "nginx:latest"
      cpu    = 0.25
      memory = "0.5Gi"
      env {
        name  = "POSTGRES_HOST"
        value = azurerm_postgresql_flexible_server.frontend.fqdn
      }
      env {
        name  = "POSTGRES_USERNAME"
        value = local.postgres_username
      }
      env {
        name        = "POSTGRES_PASSWORD"
        secret_name = "frontend-db-server-password"
      }
      env {
        name  = "POSTGRES_DATABASE"
        value = azurerm_postgresql_flexible_server_database.frontend.name
      }
      env {
        name  = "POSTGRES_SSLMODE"
        value = "require"
      }
      env {
        name  = "BACKEND_HOST"
        value = azurerm_postgresql_flexible_server.backend.fqdn
      }
      env {
        name  = "BACKEND_USERNAME"
        value = local.postgres_username
      }
      env {
        name        = "BACKEND_PASSWORD"
        secret_name = "backend-db-server-password"
      }
      env {
        name  = "BACKEND_DATABASE"
        value = azurerm_postgresql_flexible_server_database.backend.name
      }
      env {
        name  = "BACKEND_SSLMODE"
        value = "require"
      }
      env {
        name  = "GOOGLE_CLIENT_ID"
        value = var.frontend_google_client_id
      }
      env {
        name        = "GOOGLE_CLIENT_SECRET"
        secret_name = "frontend-google-client-secret"
      }
      env {
        name        = "SECRET_KEY"
        secret_name = "frontend-secret"
      }
      env {
        name        = "INHERIT_SUPERUSER_PASSWORD"
        secret_name = "frontend-superuser-password"
      }
      env {
        name  = "TEAM_PORTAL_DOMAIN"
        value = var.team_portal_domain
      }
      env {
        name  = "DJANGO_ACCOUNT_DEFAULT_HTTP_PROTOCOL"
        value = "https"
      }
      env {
        name        = "RECAPTCHA_PRIVATE_KEY"
        secret_name = "recaptcha-private-key"
      }
      env {
        name        = "RECAPTCHA_PUBLIC_KEY"
        secret_name = "recaptcha-public-key"
      }
      env {
        name        = "AZURE_OPENAI_API_KEY"
        secret_name = "openai-primary-access-key"
      }
      env {
        name  = "AZURE_OPENAI_ENDPOINT"
        value = azurerm_cognitive_account.openai.endpoint
      }
      env {
        name  = "AZURE_OPENAI_MODEL"
        value = azurerm_cognitive_deployment.openai_gpt_4o_mini.name
      }
      /*
      env {
        name  = "REDIS_HOST"
        value = "${azurerm_container_app.redis.name}"
      }
      env {
        name  = "REDIS_PORT"
        value = "6379"
      }
      */
    }
  }

  ingress {
    target_port      = 80
    external_enabled = true

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  lifecycle {
    ignore_changes = [
      template[0].container[0].image,
    ]
  }
}

resource "azurerm_role_assignment" "frontend_acr_pull" {
  scope                = var.core_outputs.acr.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.container_app_frontend.principal_id
}

resource "azurerm_container_app_custom_domain" "frontend" {
  name                     = var.team_portal_domain
  container_app_id         = azurerm_container_app.frontend.id
  certificate_binding_type = "SniEnabled"

  lifecycle {
    ignore_changes = [
      container_app_environment_certificate_id,
      certificate_binding_type
    ]
  }
}

/*
resource "azurerm_container_app" "redis" {
  name                = "${var.environment}-redis"
  resource_group_name          = azurerm_resource_group.frontend.name
  container_app_environment_id = azurerm_container_app_environment.frontend.id
  revision_mode     = "Single"

  template {
    container {
      name   = "redis"
      image  = "redis:8.0.0"
      cpu    = "0.25"
      memory = "0.5Gi"
    }
  }
}
*/