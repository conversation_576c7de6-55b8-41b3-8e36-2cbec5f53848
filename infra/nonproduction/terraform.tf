terraform {
  backend "azurerm" {
    resource_group_name  = "terraform"             # created by bootstrap
    storage_account_name = "terraformot7xt8538juj" # created by bootstrap
    container_name       = "state"                 # created by bootstrap
    key                  = "nonproduction.tfstate"
  }
}

data "terraform_remote_state" "core" {
  backend = "azurerm"

  config = {
    resource_group_name  = "terraform"             # created by bootstrap
    storage_account_name = "terraformot7xt8538juj" # created by bootstrap
    container_name       = "state"                 # created by bootstrap
    key                  = "core.tfstate"
  }
}
