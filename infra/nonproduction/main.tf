module "azure" {
  source = "../modules/azure"

  environment        = "nonproduction"
  team_portal_domain = "nonproduction.team.inherit.healthcare"
  backend_domain     = "nonproduction.api.inherit.healthcare"
  core_outputs       = data.terraform_remote_state.core.outputs
  token_issuer       = "https://securetoken.google.com/in-h-e-rit-pl978o"
  token_audience     = "in-h-e-rit-pl978o"

  frontend_google_client_id = "318012792699-l64l6qaobsuf77ngvlod65h5n67nu5si.apps.googleusercontent.com"
}

output "azure" {
  sensitive = true
  value     = module.azure
}
