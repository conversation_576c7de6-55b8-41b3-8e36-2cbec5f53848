resource "azurerm_communication_service" "main" {
  name                = "inherit"
  resource_group_name = azurerm_resource_group.main.name
  data_location       = "UK"
}

resource "azurerm_communication_service_email_domain_association" "main" {
  communication_service_id = azurerm_communication_service.main.id
  email_service_domain_id  = azurerm_email_communication_service_domain.main.id
}

resource "azurerm_email_communication_service" "main" {
  name                = "inherit"
  resource_group_name = azurerm_resource_group.main.name
  data_location       = "UK"
}

resource "azurerm_email_communication_service_domain" "main" {
  name                             = "app.inherit.healthcare"
  email_service_id                 = azurerm_email_communication_service.main.id
  domain_management                = "CustomerManaged"
  user_engagement_tracking_enabled = true
}

locals {
  zone_id = "272f3f99d5e74bf033849418f438f29a"
}

resource "cloudflare_dns_record" "dkim" {
  zone_id = local.zone_id
  ttl     = azurerm_email_communication_service_domain.main.verification_records[0].dkim[0].ttl
  name    = "${azurerm_email_communication_service_domain.main.verification_records[0].dkim[0].name}.app.inherit.healthcare"
  type    = azurerm_email_communication_service_domain.main.verification_records[0].dkim[0].type
  content = azurerm_email_communication_service_domain.main.verification_records[0].dkim[0].value
}

resource "cloudflare_dns_record" "dkim2" {
  zone_id = local.zone_id
  ttl     = azurerm_email_communication_service_domain.main.verification_records[0].dkim2[0].ttl
  name    = "${azurerm_email_communication_service_domain.main.verification_records[0].dkim2[0].name}.app.inherit.healthcare"
  type    = azurerm_email_communication_service_domain.main.verification_records[0].dkim2[0].type
  content = azurerm_email_communication_service_domain.main.verification_records[0].dkim2[0].value
}

resource "cloudflare_dns_record" "domain" {
  zone_id = local.zone_id
  ttl     = azurerm_email_communication_service_domain.main.verification_records[0].domain[0].ttl
  name    = azurerm_email_communication_service_domain.main.verification_records[0].domain[0].name
  type    = azurerm_email_communication_service_domain.main.verification_records[0].domain[0].type
  content = azurerm_email_communication_service_domain.main.verification_records[0].domain[0].value
}

resource "cloudflare_dns_record" "spf" {
  zone_id = local.zone_id
  ttl     = azurerm_email_communication_service_domain.main.verification_records[0].spf[0].ttl
  name    = azurerm_email_communication_service_domain.main.verification_records[0].spf[0].name
  type    = azurerm_email_communication_service_domain.main.verification_records[0].spf[0].type
  content = azurerm_email_communication_service_domain.main.verification_records[0].spf[0].value
}

output "communication_service" {
  sensitive = true
  value     = azurerm_communication_service.main
}
