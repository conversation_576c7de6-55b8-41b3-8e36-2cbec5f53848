terraform {
  backend "azurerm" {
    resource_group_name  = "terraform"             # created by bootstrap
    storage_account_name = "terraformot7xt8538juj" # created by bootstrap
    container_name       = "state"                 # created by bootstrap
    key                  = "core.tfstate"
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.22.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "5.3.0"
    }
  }
}

provider "azurerm" {
  features {}
}
