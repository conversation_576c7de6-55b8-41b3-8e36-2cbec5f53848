data "azurerm_client_config" "current" {}

locals {
  environments = { # we need short versions because Azure key vault names are limited to 24 characters AND have to be globally unique
    production    = "p"
    nonproduction = "n"
  }

  keyvault_access_group = {                                # these are the IDs of Entra ID groups that provide human users and Github Actions access to the secrets
    production    = "3fa42688-32cc-4121-96df-c37f48c2cf57" # keyvault-production
    nonproduction = "3bd69873-fbdd-4ad1-b2b3-1a4abb70944a" # keyvault-nonproduction
  }
}

# Per-environment key vaults
# Each service has its own key vault. Secrets are duplicated across key vaults as needed.
# For example the password for the backend database is in all three key vaults as all three services
# need access to that resource.
resource "azurerm_key_vault" "backend" {
  for_each = local.environments

  name                = "inherit${each.value}backend"
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
}

resource "azurerm_key_vault" "worker" {
  for_each = local.environments

  name                = "inherit${each.value}worker"
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
}

resource "azurerm_key_vault" "frontend" {
  for_each = local.environments

  name                = "inherit${each.value}frontend"
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
}

# Per-environment access policies for users and Github Actions
resource "azurerm_key_vault_access_policy" "backend" {
  for_each = local.environments

  key_vault_id = azurerm_key_vault.backend[each.key].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = local.keyvault_access_group[each.key]

  secret_permissions = ["Get", "Set", "List", "Delete", "Purge", "Recover", "Backup", "Restore"]
}

resource "azurerm_key_vault_access_policy" "worker" {
  for_each = local.environments

  key_vault_id = azurerm_key_vault.worker[each.key].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = local.keyvault_access_group[each.key]

  secret_permissions = ["Get", "Set", "List", "Delete", "Purge", "Recover", "Backup", "Restore"]
}

resource "azurerm_key_vault_access_policy" "frontend" {
  for_each = local.environments

  key_vault_id = azurerm_key_vault.frontend[each.key].id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = local.keyvault_access_group[each.key]

  secret_permissions = ["Get", "Set", "List", "Delete", "Purge", "Recover", "Backup", "Restore"]
}

output "keyvaults" {
  value = {
    for k, v in local.environments : k => {
      backend  = azurerm_key_vault.backend[k].id
      worker   = azurerm_key_vault.worker[k].id
      frontend = azurerm_key_vault.frontend[k].id
    }
  }
}
