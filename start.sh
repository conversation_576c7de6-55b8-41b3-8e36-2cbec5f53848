#!/bin/bash
set -e # stop on error
opts="--project-directory=. --project-name=inherit --file=repository/compose.yaml --file=backend/compose.yaml --file=frontend/compose.yaml --file=frontend/compose-db.yaml"
docker compose $opts up --detach
trap "docker compose $opts down" EXIT
source repository/apply.sh
url="postgresql://postgres:password@localhost:5433/postgres?sslmode=disable"
echo -n "waiting for frontend database to be ready ..."
iteration=0
timeout=30
ready=0
until [[ $ready -eq 1 ]]; do
  if psql $url -c 'SELECT 1;' &> /dev/null; then
    ready=1
  else
    iteration=$(( iteration + 1 ))
    if [[ $iteration -gt $timeout ]]; then
      echo " timed out after $timeout seconds"
      exit 1
    fi
    sleep 1
    echo -n "."
  fi
done
echo " ok"
docker exec inherit-frontend-1 python frontend/manage.py createinheritteamusers
docker compose $opts up
