from manual.instagramcheck.config import INSTAGRAM_PASSWORD, INSTAGRAM_USERNAME
from playwright.sync_api import sync_playwright, expect, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Page, TimeoutError as PlaywrightTimeoutError
from datetime import datetime
import logging


class InstagramBrowser:
  browser: Browser
  browser_context: BrowserContext
  page: Page

  def __init__(self, browser: <PERSON><PERSON><PERSON>, browser_context: BrowserContext, page: Page) -> None:
    self.browser = browser
    self.browser_context = browser_context
    self.page = page

  @classmethod
  def init(cls):  # type: ignore[no-untyped-def]
    logging.info("starting browser")
    p = sync_playwright().start()
    browser = p.chromium.launch(headless=False)
    browser_context = browser.new_context()
    page = browser_context.new_page()
    try:
      logging.info("navigate to login page")
      page.goto("https://www.instagram.com/accounts/login/?next=%2Finherit.healthcare%2F&source=omni_redirect", timeout=10000)
      logging.info("allow cookies")
      page.get_by_role("button", name="Allow all cookies").click(timeout=10000)
      try:
        if page.get_by_role("button", name="Sign up").is_visible(timeout=3000):
          logging.info("click log in button")
          page.get_by_role("button", name="Log in", exact=True).click(timeout=3000)
      except PlaywrightTimeoutError:
        logging.warning("'Login' button not found, continuing")
      logging.info("fill username")
      page.get_by_role("textbox", name="Phone number, username, or").fill(INSTAGRAM_USERNAME)
      logging.info("fill password")
      page.get_by_role("textbox", name="Password").fill(INSTAGRAM_PASSWORD)
      logging.info("click login button")
      page.get_by_role("button", name="Log in", exact=True).click(timeout=10000)
      try:
        logging.info("click not now button")
        page.get_by_role("button", name="Not now").click(timeout=10000)
      except PlaywrightTimeoutError:
        logging.warning("'Not now' button not found, continuing")
      logging.info("confirm profile page is visible")
      expect(page.get_by_role("link", name="followers")).to_be_visible(timeout=10000)
      logging.info("open followers")
      page.get_by_role("link", name="followers").click(timeout=10000)
      return cls(browser, browser_context, page)
    except Exception as err:
      logging.info(f"error while logging in to Instagram: {type(err)} {err}")
      page.screenshot(path=f"login_error {datetime.now().isoformat()}.png")
      raise RuntimeError("unable to login to Instagram") from err

  def check_instagram_follower(self, instagram_handle: str) -> bool:
    """Check if the user is a follower of our Instagram"""
    result = False
    page = self.page
    logging.info("fill instagram handle")
    page.get_by_role("textbox", name="Search input").fill(instagram_handle)
    try:
      logging.info("look for handle in followers list")
      expect(page.get_by_role("link", name=instagram_handle, exact=True)).to_be_visible(timeout=3000)
      result = True
    except AssertionError:
      pass  # this means the handle is not a follower
    except Exception as err:
      page.screenshot(path=f"error {datetime.now().isoformat()}.png")
      raise err
    return result
