from manual.instagramcheck.config import POSTGRES_USERNAME, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DATABASE, POSTGRES_SSLMODE
from manual.instagramcheck.repository import models, queries
from sqlalchemy import Engine, create_engine, text
import logging


class PostgresDatabase:
  engine: Engine

  def __init__(self) -> None:
    url = f"postgresql://{POSTGRES_USERNAME}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DATABASE}?sslmode={POSTGRES_SSLMODE}"
    self.engine = create_engine(url=url, connect_args={"connect_timeout": 3})


database = PostgresDatabase()


def award_reward_point(user_id: str) -> None:
  with database.engine.begin() as conn:
    store = queries.Querier(conn=conn)
    user = store.get_user_by_id(user_id=user_id)
    if user is None:
      raise RuntimeError(f"unable to find user {user_id}")
    if user.reward_point_awarded_for_instagram:
      logging.info(f"instagram reward point already awarded to user {user_id}")
      return
    store.add_user_reward_point_for_instagram(sub=user.sub)
    store.create_user_reward_point_log(queries.CreateUserRewardPointLogParams(
      sub=user.sub,
      reason=models.UserRewardPointLogReason.INSTAGRAM,
      points=1
    ))
    logging.info(f"instagram reward point awarded to user {user_id}")


def health_check() -> None:
  with database.engine.begin() as conn:
    conn.execute(text("SELECT 1"))
