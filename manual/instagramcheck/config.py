from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
import os

credential = DefaultAzureCredential()
client = SecretClient(
  vault_url="https://inheritpworker.vault.azure.net/",
  credential=credential,
)


def getenv_or_panic(env: str) -> str:
  value = os.getenv(env)
  if value is None:
    raise RuntimeError(f"{env} is required")
  return value


def get_secret_from_keyvault(secret_name: str) -> str | None:
  secret = client.get_secret(secret_name)
  return secret.value


# From Azure Key Vault
AZURE_STORAGE_ACCOUNT_ACCESS_KEY = get_secret_from_keyvault("storage-account-main-primary-access-key")
POSTGRES_PASSWORD = get_secret_from_keyvault("backend-db-server-password")

# From env
INSTAGRAM_PASSWORD = getenv_or_panic("INSTAGRAM_PASSWORD")

# Set directly
POSTGRES_USERNAME = "psqladmin"
POSTGRES_HOST = "production-backend-db-server.postgres.database.azure.com"
POSTGRES_PORT = "5432"
POSTGRES_DATABASE = "production-backend-db"
POSTGRES_SSLMODE = "require"
AZURE_STORAGE_ACCOUNT_NAME = "inheritproduction"
AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME = "instagramcheck"
INSTAGRAM_USERNAME = "inherit.healthcare"
