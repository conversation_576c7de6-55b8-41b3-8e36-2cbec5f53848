from manual.instagramcheck.config import AZURE_STORAGE_ACCOUNT_ACCESS_KEY, AZURE_STORAGE_ACCOUNT_NAME, AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME
from azure.storage.queue import Queue<PERSON>lient, TextBase64DecodePolicy
import pydantic


queue_client = QueueClient(
  account_url=f"https://{AZURE_STORAGE_ACCOUNT_NAME}.queue.core.windows.net",
  queue_name=AZURE_STORAGE_QUEUE_INSTAGRAMCHECK_NAME,
  credential=AZURE_STORAGE_ACCOUNT_ACCESS_KEY,
  message_decode_policy=TextBase64DecodePolicy()
)


class InstagramCheckMessage(pydantic.BaseModel):
  user_id: str
  instagram_handle: str


def parse_message(message: str) -> InstagramCheckMessage:
  return InstagramCheckMessage.model_validate_json(message)
