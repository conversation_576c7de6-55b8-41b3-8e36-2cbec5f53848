# Code generated by sqlc. DO NOT EDIT.
# versions:
#   sqlc v1.29.0
import datetime
import enum
import pydantic
from typing import Any, List, Optional


class HeightWeightUnitType(enum.StrEnum):
    METRIC = "Metric"
    IMPERIAL = "Imperial"


class QuestionType(enum.StrEnum):
    TEXT = "text"
    ARRAY = "array"
    MULTIARRAY = "multiarray"
    NONE = "none"


class UserCountryOfResidence(enum.StrEnum):
    ENGLAND = "England"
    SCOTLAND = "Scotland"
    WALES = "Wales"
    NORTHERNIRELAND = "Northern Ireland"
    OTHER = "Other"


class UserDocumentMimeType(enum.StrEnum):
    APPLICATION_PDF = "application/pdf"


class UserDocumentType(enum.StrEnum):
    TESTRESULTS = "Test Results"
    CLINICIANLETTER = "Clinician Letter"
    OTHER = "Other"


class UserEthnicity(enum.StrEnum):
    WHITE_ENGLISHWELSHSCOTTISHNORTHERNIRISHORBRITISH = "White - English, Welsh, Scottish, Northern Irish, or British"
    WHITE_IRISH = "White - Irish"
    WHITE_GYPSYORIRISHTRAVELLER = "White - Gypsy or Irish Traveller"
    WHITE_ROMA = "White - Roma"
    ANYOTHERWHITEBACKGROUND = "Any other White background"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDBLACKCARIBBEAN = "Mixed or Multiple Ethnic Groups - White and Black Caribbean"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDBLACKAFRICAN = "Mixed or Multiple Ethnic Groups - White and Black African"
    MIXEDORMULTIPLEETHNICGROUPS_WHITEANDASIAN = "Mixed or Multiple Ethnic Groups - White and Asian"
    ANYOTHERMIXEDORMULTIPLEETHNICBACKGROUND = "Any other Mixed or Multiple ethnic background"
    ASIANORASIANBRITISH_INDIAN = "Asian or Asian British - Indian"
    ASIANORASIANBRITISH_PAKISTANI = "Asian or Asian British - Pakistani"
    ASIANORASIANBRITISH_BANGLADESHI = "Asian or Asian British - Bangladeshi"
    ASIANORASIANBRITISH_CHINESE = "Asian or Asian British - Chinese"
    ANYOTHERASIANBACKGROUND = "Any other Asian background"
    BLACKBLACKBRITISHCARIBBEANORAFRICAN_CARIBBEAN = "Black, Black British, Caribbean, or African - Caribbean"
    BLACKBLACKBRITISHCARIBBEANORAFRICAN_AFRICAN = "Black, Black British, Caribbean, or African - African"
    ANYOTHERBLACKBLACKBRITISHORCARIBBEANBACKGROUND = "Any other Black, Black British, or Caribbean background"
    OTHERETHNICGROUP_ARAB = "Other Ethnic Group - Arab"
    ANYOTHERETHNICGROUP = "Any other ethnic group"


class UserHealthEventType(enum.StrEnum):
    DIAGNOSIS = "Diagnosis"
    INJURY = "Injury"
    PRESCRIPTION = "Prescription"
    LIFEEVENT = "Life Event"
    PROCEDURE = "Procedure"
    APPOINTMENT = "Appointment"
    HEALTHUPDATE = "Health Update"
    VACCINATION = "Vaccination"
    SCREENING = "Screening"
    HEALTHCHECK = "Health Check"
    MENTALHEALTHEVENT = "Mental Health Event"
    ALLERGY = "Allergy"
    OTHER = "Other"


class UserImpactProject(enum.StrEnum):
    FEMALE = "Female"
    LIVEBIRTHS = "LiveBirths"
    MISCARRIAGES = "Miscarriages"
    PCOS = "PCOS"
    ENDOMETRIOSIS = "Endometriosis"
    TRYINGTOCONCEIVE = "TryingToConceive"
    POSSIBLEUNDIAGNOSEDPCOS = "PossibleUndiagnosedPCOS"
    POSSIBLEUNDIAGNOSEDENDOMETRIOSIS = "PossibleUndiagnosedEndometriosis"


class UserQuestionModerationStatus(enum.StrEnum):
    PENDING = "Pending"
    APPROVED = "Approved"
    ANSWERED = "Answered"
    REJECTED = "Rejected"


class UserRelationship(enum.StrEnum):
    MOTHER = "Mother"
    FATHER = "Father"
    GRANDMOTHER = "Grandmother"
    GRANDFATHER = "Grandfather"
    DAUGHTER = "Daughter"
    SON = "Son"
    SISTER = "Sister"
    BROTHER = "Brother"
    NIECE = "Niece"
    NEPHEW = "Nephew"
    AUNT = "Aunt"
    UNCLE = "Uncle"
    GRANDDAUGHTER = "Granddaughter"
    GRANDSON = "Grandson"
    COUSIN = "Cousin"
    PARENT = "Parent"
    CHILD = "Child"
    SIBLING = "Sibling"
    GRANDPARENT = "Grandparent"
    GRANDCHILD = "Grandchild"
    PARENTSSIBLING = "Parent's Sibling"
    SIBLINGSCHILD = "Sibling's Child"
    HALF_BROTHER = "Half-Brother"
    HALF_SISTER = "Half-Sister"
    HALF_SIBLING = "Half-Sibling"


class UserRewardPointLogReason(enum.StrEnum):
    INSTAGRAM = "Instagram"
    PERSONALHEALTHSURVEY = "Personal Health Survey"
    REPRODUCTIVEHEALTHSURVEY = "Reproductive Health Survey"
    LIFESTYLESURVEY = "Lifestyle Survey"
    SURVEY = "Survey"
    SHARING = "Sharing"
    REFERRAL = "Referral"
    BEINGREFERRED = "Being Referred"
    REDEMPTION = "Redemption"


class UserRiskFactors(enum.StrEnum):
    DIABETESDIAGNOSIS = "Diabetes Diagnosis"
    HISTORYOFSMOKING = "History of Smoking"
    FAMILYHISTORYOFTYPE2DIABETES = "Family History of Type 2 Diabetes"
    HISTORYOFGESTATIONALDIABETES = "History of Gestational Diabetes"
    HYPERTENSION = "Hypertension"
    PCOS = "PCOS"
    PREDIABETES = "Prediabetes"
    HISTORYOFHEARTDISEASE = "History of Heart Disease"
    HISTORYOFSTROKE = "History of Stroke"


class UserScreeningAlertStatus(enum.StrEnum):
    NEW = "New"
    MERGED = "Merged"
    IGNORED = "Ignored"


class UserScreeningStatus(enum.StrEnum):
    NOTYETATTENDED = "Not yet attended"
    ATTENDED = "Attended"
    REQUIRESUPDATE = "Requires update"


class UserScreeningType(enum.StrEnum):
    CERVICALSMEAR = "Cervical Smear"
    BOWELSCREENING = "Bowel Screening"
    MAMMOGRAM = "Mammogram"
    SCAN = "Scan"
    BLOODTEST = "Blood Test"
    BLOODPRESSURE = "Blood Pressure"
    CONSULTATION = "Consultation"
    ABDOMINALAORTICANEURYSMSCREENING = "Abdominal Aortic Aneurysm Screening"
    DIABETICEYESCREENING = "Diabetic Eye Screening"
    ROUTINEHEALTHCHECK = "Routine Health Check"
    LUNGCANCERSCREENING = "Lung Cancer Screening"
    OTHER = "Other"


class UserSexAssignedAtBirth(enum.StrEnum):
    FEMALE = "Female"
    MALE = "Male"
    INTERSEX = "Intersex"


class DailyUserCount(pydantic.BaseModel):
    date_id: str
    date: datetime.date
    d_new_user_count: int
    d_total_user_count: int
    d_growth_rate: float
    wk_new_user_count: int
    wk_growth_rate: float
    wk_total_user_count: int
    mth_new_user_count: int
    mth_total_user_count: int
    mth_growth_rate: float
    created_date: datetime.datetime


class Expert(pydantic.BaseModel):
    expert_id: str
    sub: str
    name: str
    qualifications: str
    email: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class ExpertAnswer(pydantic.BaseModel):
    answer_id: str
    expert_id: str
    question_text: str
    answer_text: str
    parent_answer_id: Optional[str]
    position_in_tree: int
    answer_tag: Optional[str]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class ExpertAnswerTag(pydantic.BaseModel):
    tag_id: str
    expert_id: str
    tag_name: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class MagicLink(pydantic.BaseModel):
    link_id: str
    token: str
    metadata: Any
    expires_at: datetime.datetime


class Question(pydantic.BaseModel):
    question_id: str
    key: str
    question: str
    type: QuestionType
    options: Optional[List[str]]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class Survey(pydantic.BaseModel):
    survey_id: str
    title: str
    description: str
    criteria: str
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class SurveyQuestion(pydantic.BaseModel):
    survey_id: str
    question_id: str
    created_date: datetime.datetime


class User(pydantic.BaseModel):
    user_id: str
    sub: str
    username: str
    email: str
    birth_month: int
    birth_year: int
    sex_assigned_at_birth: UserSexAssignedAtBirth
    reward_points: int
    reward_point_awarded_for_instagram: bool
    reward_point_awarded_for_personal_health_survey: bool
    reward_point_awarded_for_reproductive_health_survey: bool
    reward_point_awarded_for_lifestyle_survey: bool
    reward_point_awarded_for_sharing: bool
    reward_point_awarded_to_referrer: bool
    referral_code: str
    referrer: Optional[str]
    instagram_handle: Optional[str]
    impact_points: int
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserAppointmentSupportRequest(pydantic.BaseModel):
    request_id: str
    user_id: str
    response_output: str
    appointment_type: str
    appointment_details: Optional[str]
    rating: Optional[int]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserDocument(pydantic.BaseModel):
    document_id: str
    user_id: str
    label: Optional[str]
    type: Optional[UserDocumentType]
    mime_type: Optional[UserDocumentMimeType]
    json_output: Optional[Any]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserHealthEvent(pydantic.BaseModel):
    event_id: str
    user_id: str
    type: UserHealthEventType
    details: str
    notes: Optional[Any]
    start_date: datetime.date
    end_date: Optional[datetime.date]
    ongoing: bool
    genetic: bool
    is_reviewed: bool
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserHealthUpdate(pydantic.BaseModel):
    update_id: str
    user_id: str
    text: str
    json_output: Optional[Any]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserImpactLog(pydantic.BaseModel):
    impact_id: str
    user_id: str
    project: UserImpactProject
    points: int
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserQuestion(pydantic.BaseModel):
    question_id: str
    user_id: str
    question_text: str
    moderation_status: UserQuestionModerationStatus
    votes: int
    answer_id: Optional[str]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserQuestionSupportRequest(pydantic.BaseModel):
    request_id: str
    user_id: str
    question: str
    response_output: str
    rating: Optional[int]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserRewardPointLog(pydantic.BaseModel):
    log_id: str
    user_id: str
    points: int
    reason: UserRewardPointLogReason
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserScreening(pydantic.BaseModel):
    screening_id: str
    user_id: str
    type: UserScreeningType
    subtype: Optional[str]
    next_date: Optional[datetime.date]
    last_date: Optional[datetime.date]
    attended_date: Optional[datetime.date]
    months_between_appointments: Optional[int]
    notes: Optional[str]
    status: UserScreeningStatus
    user_managed_schedule: bool
    alert_id: Optional[str]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserScreeningAlert(pydantic.BaseModel):
    alert_id: str
    user_id: str
    type: UserScreeningType
    subtype: Optional[str]
    next_date: Optional[datetime.date]
    suggested_months_between_appointments: Optional[int]
    notes: Optional[str]
    status: UserScreeningAlertStatus
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserShare(pydantic.BaseModel):
    share_id: str
    sharer_id: str
    sharee_id: str
    sharee_relationship_to_sharer: UserRelationship
    label_for_sharer: str
    label_for_sharee: str
    approved: bool
    approved_date: Optional[datetime.datetime]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserSurveyResponse(pydantic.BaseModel):
    response_id: str
    user_id: str
    survey_id: str
    question_id: str
    selected_options: Optional[List[str]]
    answer: Optional[str]
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]


class UserSurveyResponseLifestyle(pydantic.BaseModel):
    response_id: str
    user_id: str
    general_health: Optional[str]
    health_vs_last_year: Optional[str]
    height: Optional[float]
    weight: Optional[float]
    height_weight_unit_type: Optional[HeightWeightUnitType]
    daily_routine_activity: Optional[str]
    strength_training: Optional[str]
    cardio_exercise: Optional[str]
    brisk_walking: Optional[str]
    hours_sitting_per_day: Optional[str]
    special_diet: Optional[str]
    special_diet_other: Optional[str]
    regular_diet_quality: Optional[str]
    supplements: Optional[str]
    supplements_details: Optional[str]
    sleep_hours: Optional[str]
    stress_level: Optional[str]
    alcohol_frequency: Optional[str]
    nicotine_use: Optional[str]
    nicotine_details: Optional[str]
    mindfulness_practice: Optional[str]
    created_date: datetime.datetime


class UserSurveyResponsePersonalHealth(pydantic.BaseModel):
    response_id: str
    user_id: str
    gp_postcode: Optional[str]
    ethnicity: Optional[str]
    gender: Optional[str]
    country: Optional[str]
    current_health_condition: bool
    historic_health_condition: bool
    injuries: bool
    allergies: bool
    medications: bool
    routine_vaccines: Optional[List[str]]
    vaccines: Optional[List[str]]
    childhood_vaccinations_status: Optional[str]
    additional_vaccine_notes: Optional[str]
    family_health_notes: Optional[str]
    disability: Optional[bool]
    disability_needs_details: Optional[str]
    created_date: datetime.datetime


class UserSurveyResponseReproductiveHealth(pydantic.BaseModel):
    response_id: str
    user_id: str
    reproductive_organs: Optional[str]
    reproductive_organ_details: Optional[str]
    reproductive_surgeries: Optional[List[str]]
    surgery_other_details: Optional[str]
    ever_menstruated: Optional[str]
    menarche_age: Optional[str]
    menstruated_last_12_months: Optional[str]
    last_period_date: Optional[str]
    cycle_length: Optional[str]
    menstrual_symptoms: Optional[List[str]]
    menstrual_symptoms_other: Optional[str]
    currently_using_contraception: Optional[str]
    current_contraception_details: Optional[str]
    ever_used_contraception: Optional[str]
    past_contraception_details: Optional[str]
    currently_using_hrt: Optional[str]
    current_hrt_details: Optional[str]
    menstrual_status_at_hrt_start: Optional[str]
    currently_pregnant: Optional[str]
    pregnancies_total: Optional[int]
    pregnancies_live_births: Optional[int]
    pregnancies_stillbirths: Optional[int]
    pregnancies_ectopics: Optional[int]
    pregnancies_miscarriages: Optional[int]
    pregnancies_terminations: Optional[int]
    currently_breastfeeding: Optional[str]
    tried_to_conceive_12_months: Optional[str]
    fertility_testing: Optional[str]
    fertility_testing_details: Optional[str]
    diagnosed_conditions: Optional[List[str]]
    other_conditions_details: Optional[str]
    pcos_screener_irregular_periods: Optional[bool]
    pcos_screener_excessive_hair_growth: Optional[bool]
    pcos_screener_overweight_16_40: Optional[bool]
    pcos_screener_nipple_discharge: Optional[bool]
    endopain_period_pain: Optional[bool]
    endopain_pain_between_periods: Optional[bool]
    endopain_worsening_pain: Optional[bool]
    endopain_prolonged_period_pain: Optional[bool]
    endopain_stabbing_pain: Optional[bool]
    endopain_radiating_back_pain: Optional[bool]
    endopain_hip_or_leg_pain: Optional[bool]
    endopain_limits_daily_activities: Optional[bool]
    endopain_disabling_pain: Optional[bool]
    endopain_sexual_severe_pain: Optional[bool]
    endopain_sexual_position_specific_pain: Optional[bool]
    endopain_sexual_interrupts_sex: Optional[bool]
    endopain_bowel_and_bladder_pain_bowel_movements: Optional[bool]
    endopain_bowel_and_bladder_diarrhoea_constipation: Optional[bool]
    endopain_bowel_and_bladder_bowel_cramps: Optional[bool]
    endopain_bowel_and_bladder_urination_pain: Optional[bool]
    endopain_bowel_and_bladder_bladder_discomfort: Optional[bool]
    cycles_irregular_past_12_months: Optional[str]
    symptoms: Optional[List[str]]
    menopause_status: Optional[str]
    created_date: datetime.datetime


class Username(pydantic.BaseModel):
    username: str
    referral_code: str
    assigned: bool
