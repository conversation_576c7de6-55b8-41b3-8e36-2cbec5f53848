# Code generated by sqlc. DO NOT EDIT.
# versions:
#   sqlc v1.29.0
# source: queries.sql
import datetime
import decimal
import pydantic
from typing import Any, Iterator, List, Optional

import sqlalchemy

from manual.instagramcheck.repository import models


ADD_USER_REWARD_POINT = """-- name: add_user_reward_point \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1
WHERE
  "sub" = :p1
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


ADD_USER_REWARD_POINT_FOR_INSTAGRAM = """-- name: add_user_reward_point_for_instagram \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_instagram" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_for_instagram" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


ADD_USER_REWARD_POINT_FOR_LIFESTYLE_SURVEY = """-- name: add_user_reward_point_for_lifestyle_survey \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_lifestyle_survey" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_for_lifestyle_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


ADD_USER_REWARD_POINT_FOR_PERSONAL_HEALTH_SURVEY = """-- name: add_user_reward_point_for_personal_health_survey \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_personal_health_survey" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_for_personal_health_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


ADD_USER_REWARD_POINT_FOR_REPRODUCTIVE_HEALTH_SURVEY = """-- name: add_user_reward_point_for_reproductive_health_survey \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_reproductive_health_survey" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_for_reproductive_health_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


ADD_USER_REWARD_POINT_FOR_SHARING = """-- name: add_user_reward_point_for_sharing \\:one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_sharing" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_for_sharing" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


APPROVE_USER_SHARE = """-- name: approve_user_share \\:one
UPDATE
  "user_share"
SET
  "approved" = TRUE,
  "approved_date" = now()
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "share_id" = :p2
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


ASSIGN_USERNAME = """-- name: assign_username \\:one
UPDATE
  "username"
SET
  "assigned" = TRUE
WHERE
  "username" = (SELECT "username" FROM "username" WHERE "assigned" = FALSE LIMIT 1)
RETURNING username, referral_code, assigned
"""


COUNT_ACTIVE_USERS_BY_DATE = """-- name: count_active_users_by_date \\:one
SELECT
  COUNT(*)
FROM
  "user"
WHERE
  "created_date" < :p1\\:\\:timestamp
  AND "is_deleted" = FALSE
"""


COUNT_NEW_USER_SCREENING_ALERTS = """-- name: count_new_user_screening_alerts \\:one
SELECT
  COUNT(*)
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "status" = 'New'
  AND "is_deleted" = FALSE
"""


COUNT_OUTGOING_USER_SHARES = """-- name: count_outgoing_user_shares \\:one
SELECT
  COUNT(*)
FROM
  "user_share"
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


COUNT_USER_REFERRALS = """-- name: count_user_referrals \\:one
SELECT
  COUNT(*)
FROM
  "user"
WHERE
  "referrer" = (SELECT "referral_code" FROM "user" AS "referrer" WHERE "referrer"."sub" = :p1 AND "is_deleted" = FALSE)
  AND "reward_point_awarded_for_personal_health_survey" = TRUE
  AND "reward_point_awarded_for_reproductive_health_survey" = TRUE
  AND "reward_point_awarded_for_lifestyle_survey" = TRUE
  AND "is_deleted" = FALSE
"""


COUNT_USERS_FOR_DATE = """-- name: count_users_for_date \\:one
SELECT COUNT(user_id) AS user_count
FROM "user"
WHERE "created_date" >= :p1\\:\\:timestamp
  AND "created_date" < :p2\\:\\:timestamp
  AND "is_deleted" = FALSE
"""


CREATE_APPOINTMENT_SUPPORT_REQUEST = """-- name: create_appointment_support_request \\:one
INSERT INTO
  "user_appointment_support_request" (
    "user_id",
    "response_output",
    "appointment_type",
    "appointment_details",
    "rating"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5
  ) RETURNING request_id, user_id, response_output, appointment_type, appointment_details, rating, created_date, is_deleted, deleted_date
"""


class CreateAppointmentSupportRequestParams(pydantic.BaseModel):
    sub: str
    response_output: str
    appointment_type: str
    appointment_details: Optional[str]
    rating: Optional[int]


CREATE_DAILY_USER_COUNT = """-- name: create_daily_user_count \\:one
SELECT 
    :p1\\:\\:date AS date,
    :p2\\:\\:integer AS d_new_user_count,
    :p3\\:\\:integer AS d_total_user_count,
    :p4\\:\\:numeric AS d_growth,
    :p5\\:\\:integer AS wk_new_user_count,
    :p6\\:\\:integer AS wk_cur_total_user_count,
    :p7\\:\\:numeric AS wk_growth,
    :p8\\:\\:integer AS mth_new_user_count,
    :p9\\:\\:integer AS mth_cur_total_user_count,
    :p10\\:\\:numeric AS mth_growth
"""


class CreateDailyUserCountParams(pydantic.BaseModel):
    date: datetime.date
    d_new_user_count: int
    d_total_user_count: int
    d_growth: decimal.Decimal
    wk_new_user_count: int
    wk_total_user_count: int
    wk_growth: decimal.Decimal
    mth_new_user_count: int
    mth_total_user_count: int
    mth_growth: decimal.Decimal


class CreateDailyUserCountRow(pydantic.BaseModel):
    date: datetime.date
    d_new_user_count: int
    d_total_user_count: int
    d_growth: decimal.Decimal
    wk_new_user_count: int
    wk_cur_total_user_count: int
    wk_growth: decimal.Decimal
    mth_new_user_count: int
    mth_cur_total_user_count: int
    mth_growth: decimal.Decimal


CREATE_MAGIC_LINK = """-- name: create_magic_link \\:one

INSERT INTO
  "magic_link" (
    "token",
    "metadata"
  )
VALUES
  (
    :p1,
    :p2
  ) RETURNING link_id, token, metadata, expires_at
"""


CREATE_QUESTION_SUPPORT_REQUEST = """-- name: create_question_support_request \\:one
INSERT INTO
  "user_question_support_request" (
    "user_id",
    "question",
    "response_output",
    "rating"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4
  ) RETURNING request_id, user_id, question, response_output, rating, created_date, is_deleted, deleted_date
"""


class CreateQuestionSupportRequestParams(pydantic.BaseModel):
    sub: str
    question: str
    response_output: str
    rating: Optional[int]


CREATE_USER = """-- name: create_user \\:one
INSERT INTO "user" (
  "sub",
  "username",
  "email",
  "birth_month",
  "birth_year",
  "sex_assigned_at_birth",
  "referral_code",
  "referrer"
)
VALUES (
  :p1,
  :p2,
  :p3,
  :p4,
  :p5,
  :p6,
  :p7,
  :p8
) RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


class CreateUserParams(pydantic.BaseModel):
    sub: str
    username: str
    email: str
    birth_month: int
    birth_year: int
    sex_assigned_at_birth: models.UserSexAssignedAtBirth
    referral_code: str
    referrer: Optional[str]


CREATE_USER_DOCUMENT = """-- name: create_user_document \\:one

INSERT INTO "user_document" (
  "user_id"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
) RETURNING document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
"""


CREATE_USER_HEALTH_EVENT = """-- name: create_user_health_event \\:one

INSERT INTO "user_health_event" (
  "user_id",
  "type",
  "details",
  "notes",
  "start_date",
  "end_date",
  "ongoing",
  "genetic"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
  :p2,
  :p3,
  :p4,
  :p5,
  :p6,
  :p7,
  :p8
) RETURNING event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
"""


class CreateUserHealthEventParams(pydantic.BaseModel):
    sub: str
    type: models.UserHealthEventType
    details: str
    notes: Optional[Any]
    start_date: datetime.date
    end_date: Optional[datetime.date]
    ongoing: bool
    genetic: bool


CREATE_USER_REWARD_POINT_LOG = """-- name: create_user_reward_point_log \\:one

INSERT INTO
  "user_reward_point_log" (
    "user_id",
    "points",
    "reason"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3
  ) RETURNING log_id, user_id, points, reason, created_date, is_deleted, deleted_date
"""


class CreateUserRewardPointLogParams(pydantic.BaseModel):
    sub: str
    points: int
    reason: models.UserRewardPointLogReason


CREATE_USER_SCREENING = """-- name: create_user_screening \\:one
INSERT INTO "user_screening" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "last_date",
  "attended_date",
  "months_between_appointments",
  "notes",
  "status",
  "user_managed_schedule",
  "alert_id"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
  :p2,
  :p3,
  :p4,
  :p5,
  :p6,
  :p7,
  :p8,
  :p9,
  :p10,
  :p11
) RETURNING screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
"""


class CreateUserScreeningParams(pydantic.BaseModel):
    sub: str
    type: models.UserScreeningType
    subtype: Optional[str]
    next_date: Optional[datetime.date]
    last_date: Optional[datetime.date]
    attended_date: Optional[datetime.date]
    months_between_appointments: Optional[int]
    notes: Optional[str]
    status: models.UserScreeningStatus
    user_managed_schedule: bool
    alert_id: Optional[str]


CREATE_USER_SCREENING_ALERT = """-- name: create_user_screening_alert \\:one

INSERT INTO "user_screening_alert" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "suggested_months_between_appointments",
  "notes"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
  :p2,
  :p3,
  :p4,
  :p5,
  :p6
) RETURNING alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
"""


class CreateUserScreeningAlertParams(pydantic.BaseModel):
    sub: str
    type: models.UserScreeningType
    subtype: Optional[str]
    next_date: Optional[datetime.date]
    suggested_months_between_appointments: Optional[int]
    notes: Optional[str]


CREATE_USER_SHARE = """-- name: create_user_share \\:one

INSERT INTO "user_share" (
  "sharer_id",
  "sharee_id",
  "sharee_relationship_to_sharer",
  "label_for_sharer",
  "label_for_sharee"
)
VALUES (
  (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = :p1 AND "is_deleted" = FALSE),
  (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."username" = :p2 AND "sharee"."email" = :p3 AND "is_deleted" = FALSE),
  :p4,
  :p5,
  :p6
)
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


class CreateUserShareParams(pydantic.BaseModel):
    sub: str
    username: str
    email: str
    sharee_relationship_to_sharer: models.UserRelationship
    label_for_sharer: str
    label_for_sharee: str


CREATE_USER_SURVEY_RESPONSE = """-- name: create_user_survey_response \\:one
INSERT INTO "user_survey_response" (
  "user_id",
  "survey_id",
  "question_id",
  "selected_options",
  "answer"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
  :p2,
  :p3,
  :p4,
  :p5
) RETURNING response_id, user_id, survey_id, question_id, selected_options, answer, created_date, is_deleted, deleted_date
"""


class CreateUserSurveyResponseParams(pydantic.BaseModel):
    sub: str
    survey_id: str
    question_id: str
    selected_options: Optional[List[str]]
    answer: Optional[str]


DELETE_ALL_USER_SHARES = """-- name: delete_all_user_shares \\:many
UPDATE
  "user_share"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = :p1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = :p1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


DELETE_APPOINTMENT_SUPPORT_REQUEST = """-- name: delete_appointment_support_request \\:one
UPDATE
  "user_appointment_support_request"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "request_id" = :p2
  AND "is_deleted" = FALSE
RETURNING request_id, user_id, response_output, appointment_type, appointment_details, rating, created_date, is_deleted, deleted_date
"""


DELETE_USER = """-- name: delete_user \\:one
UPDATE
  "user"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "sub" = :p1
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


DELETE_USER_DOCUMENT = """-- name: delete_user_document \\:one
UPDATE
  "user_document"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "document_id" = :p2
  AND "is_deleted" = FALSE
RETURNING document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
"""


DELETE_USER_HEALTH_EVENT = """-- name: delete_user_health_event \\:one
UPDATE
  "user_health_event"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "event_id" = :p2
  AND "is_deleted" = FALSE
RETURNING event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
"""


DELETE_USER_SCREENING = """-- name: delete_user_screening \\:one
UPDATE
  "user_screening"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "screening_id" = :p2
  AND "is_deleted" = FALSE
RETURNING screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
"""


DELETE_USER_SHARE = """-- name: delete_user_share \\:one
UPDATE
  "user_share"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = :p1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = :p1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "share_id" = :p2
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


GET_ALL_SURVEYS = """-- name: get_all_surveys \\:many

SELECT
  s.survey_id, s.title, s.description, s.criteria, s.created_date, s.is_deleted, s.deleted_date
FROM
  "survey" s
WHERE
  s."is_deleted" = FALSE
ORDER BY s."created_date" DESC
"""


GET_APPOINTMENT_SUPPORT_REQUEST = """-- name: get_appointment_support_request \\:one
SELECT
  request_id, user_id, response_output, appointment_type, appointment_details, rating, created_date, is_deleted, deleted_date
FROM
  "user_appointment_support_request"
WHERE
  "request_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


GET_DAILY_USER_COUNT = """-- name: get_daily_user_count \\:many
SELECT
  date_id, date, d_new_user_count, d_total_user_count, d_growth_rate, wk_new_user_count, wk_growth_rate, wk_total_user_count, mth_new_user_count, mth_total_user_count, mth_growth_rate, created_date
FROM
  "daily_user_count"
WHERE
  "date" <= :p1
"""


GET_LAST_PROCESSED_DATE = """-- name: get_last_processed_date \\:one
SELECT MAX(date) AS last_date
FROM "daily_user_count"
"""


GET_MAGIC_LINK = """-- name: get_magic_link \\:one
SELECT
  link_id, token, metadata, expires_at
FROM
  "magic_link"
WHERE
  "token" = :p1
"""


GET_QUESTION_BY_ID = """-- name: get_question_by_id \\:one
SELECT
  q.question_id, q.key, q.question, q.type, q.options, q.created_date, q.is_deleted, q.deleted_date
FROM
  "question" q
WHERE
  q."question_id" = :p1
  AND q."is_deleted" = FALSE
"""


GET_QUESTION_SUPPORT_REQUEST = """-- name: get_question_support_request \\:one
SELECT
  request_id, user_id, question, response_output, rating, created_date, is_deleted, deleted_date
FROM
  "user_question_support_request"
WHERE
  "request_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


GET_SURVEY_BY_ID = """-- name: get_survey_by_id \\:one
SELECT
  s.survey_id, s.title, s.description, s.criteria, s.created_date, s.is_deleted, s.deleted_date
FROM
  "survey" s
WHERE
  s."survey_id" = :p1
  AND s."is_deleted" = FALSE
"""


GET_SURVEY_QUESTIONS = """-- name: get_survey_questions \\:many
SELECT
  q.question_id, q.key, q.question, q.type, q.options, q.created_date, q.is_deleted, q.deleted_date
FROM
  "question" q
  JOIN "survey_question" sq ON q."question_id" = sq."question_id"
WHERE
  sq."survey_id" = :p1
  AND q."is_deleted" = FALSE
ORDER BY q."created_date"
"""


GET_UNANSWERED_QUESTIONS_FOR_SURVEY = """-- name: get_unanswered_questions_for_survey \\:many
SELECT
  q.question_id, q.key, q.question, q.type, q.options, q.created_date, q.is_deleted, q.deleted_date
FROM
  "question" q
  JOIN "survey_question" sq ON q."question_id" = sq."question_id"
  LEFT JOIN "user_survey_response" usr ON q."question_id" = usr."question_id"
    AND usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p2 AND "is_deleted" = FALSE)
    AND usr."is_deleted" = FALSE
WHERE
  sq."survey_id" = :p1
  AND q."is_deleted" = FALSE
  AND usr."response_id" IS NULL
ORDER BY q."created_date"
"""


GET_USER_BY_ID = """-- name: get_user_by_id \\:one
SELECT
  user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
FROM
  "user"
WHERE
  "user_id" = :p1
  AND "is_deleted" = FALSE
"""


GET_USER_BY_REFERRAL_CODE = """-- name: get_user_by_referral_code \\:one
SELECT
  user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
FROM
  "user"
WHERE
  "referral_code" = :p1
  AND "is_deleted" = FALSE
"""


GET_USER_BY_SUB = """-- name: get_user_by_sub \\:one
SELECT
  user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
FROM
  "user"
WHERE
  "sub" = :p1
  AND "is_deleted" = FALSE
"""


GET_USER_DOB = """-- name: get_user_dob \\:many
SELECT
  "birth_year",
  "birth_month"

FROM 
  "user"
WHERE
  "is_deleted" = FALSE
"""


class GetUserDOBRow(pydantic.BaseModel):
    birth_year: int
    birth_month: int


GET_USER_DOCUMENT = """-- name: get_user_document \\:one
SELECT
  document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
FROM
  "user_document"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "document_id" = :p2
  AND "is_deleted" = FALSE
"""


GET_USER_HEALTH_EVENT = """-- name: get_user_health_event \\:one
SELECT
  event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "event_id" = :p2
  AND "is_deleted" = FALSE
"""


GET_USER_RESPONSES_FOR_SURVEY = """-- name: get_user_responses_for_survey \\:many
SELECT
  usr.response_id, usr.user_id, usr.survey_id, usr.question_id, usr.selected_options, usr.answer, usr.created_date, usr.is_deleted, usr.deleted_date
FROM
  "user_survey_response" usr
WHERE
  usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND usr."survey_id" = :p2
  AND usr."is_deleted" = FALSE
ORDER BY usr."created_date"
"""


GET_USER_SCREENING = """-- name: get_user_screening \\:one
SELECT
  screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
FROM
  "user_screening"
WHERE
  "screening_id" = :p1
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p2 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


GET_USER_SCREENING_ALERT = """-- name: get_user_screening_alert \\:one
SELECT
  alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "alert_id" = :p2
  AND "is_deleted" = FALSE
"""


GET_USER_SHARE = """-- name: get_user_share \\:one
SELECT
  share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
FROM
  "user_share"
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = :p1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = :p1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "share_id" = :p2
  AND "is_deleted" = FALSE
"""


GET_USER_SURVEY_PROGRESS = """-- name: get_user_survey_progress \\:one
SELECT
  COUNT(DISTINCT usr."question_id") as answered_count,
  COUNT(DISTINCT sq."question_id") as total_count
FROM
  "survey_question" sq
  LEFT JOIN "user_survey_response" usr ON sq."question_id" = usr."question_id"
    AND usr."is_deleted" = FALSE
    AND usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p2 AND "is_deleted" = FALSE)
WHERE
  sq."survey_id" = :p1
"""


class GetUserSurveyProgressRow(pydantic.BaseModel):
    answered_count: int
    total_count: int


GET_USER_SURVEY_RESPONSE_LIFESTYLE = """-- name: get_user_survey_response_lifestyle \\:one

SELECT
  response_id, user_id, general_health, health_vs_last_year, height, weight, height_weight_unit_type, daily_routine_activity, strength_training, cardio_exercise, brisk_walking, hours_sitting_per_day, special_diet, special_diet_other, regular_diet_quality, supplements, supplements_details, sleep_hours, stress_level, alcohol_frequency, nicotine_use, nicotine_details, mindfulness_practice, created_date
FROM
  "user_survey_response_lifestyle"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
"""


GET_USER_SURVEY_RESPONSE_PERSONAL_HEALTH = """-- name: get_user_survey_response_personal_health \\:one


SELECT
  response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
FROM
  "user_survey_response_personal_health"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
"""


GET_USER_SURVEY_RESPONSE_REPRODUCTIVE_HEALTH = """-- name: get_user_survey_response_reproductive_health \\:one

SELECT
  response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
FROM
  "user_survey_response_reproductive_health"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
"""


IMPACT_ENDOMETRIOSIS = """-- name: impact_endometriosis \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Endometriosis' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_health_event" WHERE "type" = 'Diagnosis' AND lower("details") IN ('endometriosis')) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Endometriosis' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_FEMALE = """-- name: impact_female \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Female' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user" WHERE "sex_assigned_at_birth" = 'Female') AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Female' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_LIVE_BIRTHS = """-- name: impact_live_births \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'LiveBirths' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "pregnancies_live_births" > 0) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'LiveBirths' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_MISCARRIAGES = """-- name: impact_miscarriages \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Miscarriages' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "pregnancies_miscarriages" > 0) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Miscarriages' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_PCOS = """-- name: impact_pcos \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PCOS' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_health_event" WHERE "type" = 'Diagnosis' AND lower("details") IN ('pcos', 'polycystic ovary syndrome', 'poly-cystic ovary syndrome')) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PCOS' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_POSSIBLE_UNDIAGNOSED_ENDOMETRIOSIS = """-- name: impact_possible_undiagnosed_endometriosis \\:exec


INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PossibleUndiagnosedEndometriosis' as "project",
  1 as "points"
FROM (
  SELECT "user_id"
  FROM "user_survey_response_reproductive_health"
  WHERE (
    coalesce("endopain_period_pain", false)\\:\\:int +
    coalesce("endopain_pain_between_periods", false)\\:\\:int +
    coalesce("endopain_worsening_pain", false)\\:\\:int +
    coalesce("endopain_prolonged_period_pain", false)\\:\\:int +
    coalesce("endopain_stabbing_pain", false)\\:\\:int +
    coalesce("endopain_radiating_back_pain", false)\\:\\:int +
    coalesce("endopain_hip_or_leg_pain", false)\\:\\:int +
    coalesce("endopain_limits_daily_activities", false)\\:\\:int +
    coalesce("endopain_disabling_pain", false)\\:\\:int +
    coalesce("endopain_sexual_severe_pain", false)\\:\\:int +
    coalesce("endopain_sexual_position_specific_pain", false)\\:\\:int +
    coalesce("endopain_sexual_interrupts_sex", false)\\:\\:int +
    coalesce("endopain_bowel_and_bladder_pain_bowel_movements", false)\\:\\:int +
    coalesce("endopain_bowel_and_bladder_diarrhoea_constipation", false)\\:\\:int +
    coalesce("endopain_bowel_and_bladder_bowel_cramps", false)\\:\\:int +
    coalesce("endopain_bowel_and_bladder_urination_pain", false)\\:\\:int +
    coalesce("endopain_bowel_and_bladder_bladder_discomfort", false)\\:\\:int
  ) > 12 -- At least 75% of the Endometriosis screener questions answered positively
) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PossibleUndiagnosedEndometriosis' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_POSSIBLE_UNDIAGNOSED_PCOS = """-- name: impact_possible_undiagnosed_pcos \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PossibleUndiagnosedPCOS' as "project",
  1 as "points"
FROM (
  SELECT "user_id"
  FROM "user_survey_response_reproductive_health"
  WHERE (
    coalesce("pcos_screener_irregular_periods", false)\\:\\:int +
    coalesce("pcos_screener_excessive_hair_growth", false)\\:\\:int +
    coalesce("pcos_screener_overweight_16_40", false)\\:\\:int +
    coalesce("pcos_screener_nipple_discharge", false)\\:\\:int
  ) >= 3 -- At least 75% of the PCOS screener questions answered positively
) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PossibleUndiagnosedPCOS' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_TRYING_TO_CONCEIVE = """-- name: impact_trying_to_conceive \\:exec

INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'TryingToConceive' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "tried_to_conceive_12_months" = 'Yes') AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'TryingToConceive' 
WHERE "user_impact_log"."project" IS NULL
"""


IMPACT_UPDATE_POINTS = """-- name: impact_update_points \\:exec


UPDATE "user"
SET "impact_points" = (SELECT coalesce(sum("points"), 0) FROM "user_impact_log" WHERE "user_impact_log"."user_id" = "user"."user_id")
"""


INSERT_DAILY_USER_COUNT = """-- name: insert_daily_user_count \\:one
INSERT INTO
  "daily_user_count" (
    "date",
    "d_new_user_count",
    "d_total_user_count",
    "d_growth_rate",
    "wk_new_user_count",
    "wk_growth_rate",
    "wk_total_user_count",
    "mth_new_user_count",
    "mth_total_user_count",
    "mth_growth_rate"
  )
VALUES
  (
    :p1,
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7,
    :p8,
    :p9,
    :p10
  ) RETURNING date_id, date, d_new_user_count, d_total_user_count, d_growth_rate, wk_new_user_count, wk_growth_rate, wk_total_user_count, mth_new_user_count, mth_total_user_count, mth_growth_rate, created_date
"""


class InsertDailyUserCountParams(pydantic.BaseModel):
    date: datetime.date
    d_new_user_count: int
    d_total_user_count: int
    d_growth_rate: float
    wk_new_user_count: int
    wk_growth_rate: float
    wk_total_user_count: int
    mth_new_user_count: int
    mth_total_user_count: int
    mth_growth_rate: float


LIST_INCOMING_USER_SHARES = """-- name: list_incoming_user_shares \\:many
SELECT
  share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
FROM
  "user_share"
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "user_share"."is_deleted" = FALSE
"""


LIST_MATCHING_SCREENINGS = """-- name: list_matching_screenings \\:many
SELECT
  screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
FROM
  "user_screening"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "type" = :p2
  AND ("subtype" = :p3 OR ("subtype" IS NULL AND :p3 IS NULL))
  AND "status" = :p4
  AND "is_deleted" = FALSE
"""


class ListMatchingScreeningsParams(pydantic.BaseModel):
    sub: str
    type: models.UserScreeningType
    subtype: Optional[str]
    status: models.UserScreeningStatus


LIST_NEW_USER_SCREENING_ALERTS = """-- name: list_new_user_screening_alerts \\:many
SELECT
  alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "status" = 'New'
  AND "is_deleted" = FALSE
"""


LIST_OUTGOING_USER_SHARES = """-- name: list_outgoing_user_shares \\:many
SELECT
  share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
FROM
  "user_share"
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


LIST_QUESTION_SUPPORT_REQUESTS = """-- name: list_question_support_requests \\:many
SELECT
  request_id, user_id, question, response_output, rating, created_date, is_deleted, deleted_date
FROM
  "user_question_support_request"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
ORDER BY
  "created_date" DESC
"""


LIST_SHARED_HEALTH_EVENTS = """-- name: list_shared_health_events \\:many

SELECT
  user_health_event.event_id, user_health_event.user_id, user_health_event.type, user_health_event.details, user_health_event.notes, user_health_event.start_date, user_health_event.end_date, user_health_event.ongoing, user_health_event.genetic, user_health_event.is_reviewed, user_health_event.created_date, user_health_event.is_deleted, user_health_event.deleted_date,
  user_share.share_id, user_share.sharer_id, user_share.sharee_id, user_share.sharee_relationship_to_sharer, user_share.label_for_sharer, user_share.label_for_sharee, user_share.approved, user_share.approved_date, user_share.created_date, user_share.is_deleted, user_share.deleted_date
FROM
  "user_health_event"
  INNER JOIN "user_share" ON "user_health_event"."user_id" = "user_share"."sharer_id"
WHERE
  "user_share"."sharee_id" = (SELECT "user_id" FROM "user" WHERE "user"."sub" = :p1 AND "user"."is_deleted" = FALSE)
  AND "user_health_event"."genetic" = TRUE
  AND "user_share"."is_deleted" = FALSE
  AND "user_health_event"."is_deleted" = FALSE
"""


class ListSharedHealthEventsRow(pydantic.BaseModel):
    event_id: str
    user_id: str
    type: models.UserHealthEventType
    details: str
    notes: Optional[Any]
    start_date: datetime.date
    end_date: Optional[datetime.date]
    ongoing: bool
    genetic: bool
    is_reviewed: bool
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]
    share_id: str
    sharer_id: str
    sharee_id: str
    sharee_relationship_to_sharer: models.UserRelationship
    label_for_sharer: str
    label_for_sharee: str
    approved: bool
    approved_date: Optional[datetime.datetime]
    created_date_2: datetime.datetime
    is_deleted_2: bool
    deleted_date_2: Optional[datetime.datetime]


LIST_USER_APPOINTMENT_SUPPORT_REQUESTS = """-- name: list_user_appointment_support_requests \\:many
SELECT
  request_id, user_id, response_output, appointment_type, appointment_details, rating, created_date, is_deleted, deleted_date
FROM
  "user_appointment_support_request"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
ORDER BY
  "created_date" DESC
"""


LIST_USER_DOCUMENTS = """-- name: list_user_documents \\:many
SELECT
  document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
FROM
  "user_document"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "label" IS NOT NULL
  AND "is_deleted" = FALSE
"""


LIST_USER_HEALTH_EVENTS = """-- name: list_user_health_events \\:many
SELECT
  event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


LIST_USER_HEALTH_EVENTS_BY_TYPE = """-- name: list_user_health_events_by_type \\:many
SELECT
  event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "type" = :p2
  AND "is_deleted" = FALSE
"""


LIST_USER_SCREENING_ALERTS = """-- name: list_user_screening_alerts \\:many
SELECT
  alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


LIST_USER_SCREENINGS = """-- name: list_user_screenings \\:many

SELECT
  screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
FROM
  "user_screening"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
"""


MAGIC_LINK_APPROVE_USER_SHARE = """-- name: magic_link_approve_user_share \\:one
UPDATE
  "user_share"
SET
  "approved" = TRUE,
  "approved_date" = now()
WHERE
  "share_id" = :p1
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


MARK_USER_REWARD_POINT_FOR_REFERRAL = """-- name: mark_user_reward_point_for_referral \\:one
UPDATE
  "user"
SET
  "reward_point_awarded_to_referrer" = TRUE
WHERE
  "sub" = :p1
  AND "reward_point_awarded_to_referrer" = FALSE
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


UPDATE_APPOINTMENT_SUPPORT_REQUEST_RATING = """-- name: update_appointment_support_request_rating \\:one
UPDATE
  "user_appointment_support_request"
SET
  "rating" = :p3
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "request_id" = :p2
  AND "is_deleted" = FALSE
RETURNING request_id, user_id, response_output, appointment_type, appointment_details, rating, created_date, is_deleted, deleted_date
"""


class UpdateAppointmentSupportRequestRatingParams(pydantic.BaseModel):
    sub: str
    request_id: str
    rating: Optional[int]


UPDATE_QUESTION_SUPPORT_REQUEST_RATING = """-- name: update_question_support_request_rating \\:one
UPDATE
  "user_question_support_request"
SET
  "rating" = :p3
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "request_id" = :p2
  AND "is_deleted" = FALSE
RETURNING request_id, user_id, question, response_output, rating, created_date, is_deleted, deleted_date
"""


class UpdateQuestionSupportRequestRatingParams(pydantic.BaseModel):
    sub: str
    request_id: str
    rating: Optional[int]


UPDATE_USER_DOCUMENT = """-- name: update_user_document \\:one
UPDATE
  "user_document"
SET 
  "label" = :p3,
  "type" = :p4,
  "mime_type" = :p5
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "document_id" = :p2
  AND "label" IS NULL
  AND "is_deleted" = FALSE
RETURNING document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
"""


class UpdateUserDocumentParams(pydantic.BaseModel):
    sub: str
    document_id: str
    label: Optional[str]
    type: Optional[models.UserDocumentType]
    mime_type: Optional[models.UserDocumentMimeType]


UPDATE_USER_HEALTH_EVENT = """-- name: update_user_health_event \\:one
UPDATE
  "user_health_event"
SET
  "type" = :p3,
  "details" = :p4,
  "notes" = :p5,
  "start_date" = :p6,
  "end_date" = :p7,
  "ongoing" = :p8
WHERE 
  "event_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
"""


class UpdateUserHealthEventParams(pydantic.BaseModel):
    sub: str
    event_id: str
    type: models.UserHealthEventType
    details: str
    notes: Optional[Any]
    start_date: datetime.date
    end_date: Optional[datetime.date]
    ongoing: bool


UPDATE_USER_INSTAGRAM_HANDLE = """-- name: update_user_instagram_handle \\:one
UPDATE
  "user"
SET
  "instagram_handle" = :p2
WHERE
  "sub" = :p1
  AND "is_deleted" = FALSE
RETURNING user_id, sub, username, email, birth_month, birth_year, sex_assigned_at_birth, reward_points, reward_point_awarded_for_instagram, reward_point_awarded_for_personal_health_survey, reward_point_awarded_for_reproductive_health_survey, reward_point_awarded_for_lifestyle_survey, reward_point_awarded_for_sharing, reward_point_awarded_to_referrer, referral_code, referrer, instagram_handle, impact_points, created_date, is_deleted, deleted_date
"""


UPDATE_USER_SCREENING = """-- name: update_user_screening \\:one
UPDATE
  "user_screening"
SET
  "next_date" = :p3,
  "last_date" = :p4,
  "attended_date" = :p5,
  "months_between_appointments" = :p6,
  "notes" = :p7,
  "status" = :p8,
  "user_managed_schedule" = :p9
WHERE
  "screening_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
"""


class UpdateUserScreeningParams(pydantic.BaseModel):
    sub: str
    screening_id: str
    next_date: Optional[datetime.date]
    last_date: Optional[datetime.date]
    attended_date: Optional[datetime.date]
    months_between_appointments: Optional[int]
    notes: Optional[str]
    status: models.UserScreeningStatus
    user_managed_schedule: bool


UPDATE_USER_SCREENING_ALERT_STATUS = """-- name: update_user_screening_alert_status \\:one
UPDATE
  "user_screening_alert"
SET
  "status" = :p3
WHERE 
  "alert_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
"""


class UpdateUserScreeningAlertStatusParams(pydantic.BaseModel):
    sub: str
    alert_id: str
    status: models.UserScreeningAlertStatus


UPDATE_USER_SCREENING_ATTENDED = """-- name: update_user_screening_attended \\:one
UPDATE
  "user_screening"
SET
  "attended_date" = :p3,
  "notes" = :p4,
  "status" = :p5
WHERE
  "screening_id" = :p2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING screening_id, user_id, type, subtype, next_date, last_date, attended_date, months_between_appointments, notes, status, user_managed_schedule, alert_id, created_date, is_deleted, deleted_date
"""


class UpdateUserScreeningAttendedParams(pydantic.BaseModel):
    sub: str
    screening_id: str
    attended_date: Optional[datetime.date]
    notes: Optional[str]
    status: models.UserScreeningStatus


UPDATE_USER_SHARE_SHAREE_LABEL = """-- name: update_user_share_sharee_label \\:one
UPDATE
  "user_share"
SET
  "label_for_sharee" = :p3
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "share_id" = :p2
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


class UpdateUserShareShareeLabelParams(pydantic.BaseModel):
    sub: str
    share_id: str
    label_for_sharee: str


UPDATE_USER_SHARE_SHARER_LABEL = """-- name: update_user_share_sharer_label \\:one
UPDATE
  "user_share"
SET
  "label_for_sharer" = :p3
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE)
  AND "share_id" = :p2
  AND "is_deleted" = FALSE
RETURNING share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
"""


class UpdateUserShareSharerLabelParams(pydantic.BaseModel):
    sub: str
    share_id: str
    label_for_sharer: str


UPDATE_USER_SURVEY_LIFESTYLE_FOUR = """-- name: update_user_survey_lifestyle_four \\:one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "sleep_hours",
    "stress_level",
    "alcohol_frequency",
    "nicotine_use",
    "nicotine_details",
    "mindfulness_practice"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "sleep_hours" = EXCLUDED."sleep_hours",
  "stress_level" = EXCLUDED."stress_level",
  "alcohol_frequency" = EXCLUDED."alcohol_frequency",
  "nicotine_use" = EXCLUDED."nicotine_use",
  "nicotine_details" = EXCLUDED."nicotine_details",
  "mindfulness_practice" = EXCLUDED."mindfulness_practice"
RETURNING response_id, user_id, general_health, health_vs_last_year, height, weight, height_weight_unit_type, daily_routine_activity, strength_training, cardio_exercise, brisk_walking, hours_sitting_per_day, special_diet, special_diet_other, regular_diet_quality, supplements, supplements_details, sleep_hours, stress_level, alcohol_frequency, nicotine_use, nicotine_details, mindfulness_practice, created_date
"""


class UpdateUserSurveyLifestyleFourParams(pydantic.BaseModel):
    sub: str
    sleep_hours: Optional[str]
    stress_level: Optional[str]
    alcohol_frequency: Optional[str]
    nicotine_use: Optional[str]
    nicotine_details: Optional[str]
    mindfulness_practice: Optional[str]


UPDATE_USER_SURVEY_LIFESTYLE_ONE = """-- name: update_user_survey_lifestyle_one \\:one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "general_health",
    "health_vs_last_year",
    "height",
    "weight",
    "height_weight_unit_type"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "general_health" = EXCLUDED."general_health",
  "health_vs_last_year" = EXCLUDED."health_vs_last_year",
  "height" = EXCLUDED."height",
  "weight" = EXCLUDED."weight",
  "height_weight_unit_type" = EXCLUDED."height_weight_unit_type"
RETURNING response_id, user_id, general_health, health_vs_last_year, height, weight, height_weight_unit_type, daily_routine_activity, strength_training, cardio_exercise, brisk_walking, hours_sitting_per_day, special_diet, special_diet_other, regular_diet_quality, supplements, supplements_details, sleep_hours, stress_level, alcohol_frequency, nicotine_use, nicotine_details, mindfulness_practice, created_date
"""


class UpdateUserSurveyLifestyleOneParams(pydantic.BaseModel):
    sub: str
    general_health: Optional[str]
    health_vs_last_year: Optional[str]
    height: Optional[float]
    weight: Optional[float]
    height_weight_unit_type: Optional[models.HeightWeightUnitType]


UPDATE_USER_SURVEY_LIFESTYLE_THREE = """-- name: update_user_survey_lifestyle_three \\:one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "special_diet",
    "special_diet_other",
    "regular_diet_quality",
    "supplements",
    "supplements_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "special_diet" = EXCLUDED."special_diet",
  "special_diet_other" = EXCLUDED."special_diet_other",
  "regular_diet_quality" = EXCLUDED."regular_diet_quality",
  "supplements" = EXCLUDED."supplements",
  "supplements_details" = EXCLUDED."supplements_details"
RETURNING response_id, user_id, general_health, health_vs_last_year, height, weight, height_weight_unit_type, daily_routine_activity, strength_training, cardio_exercise, brisk_walking, hours_sitting_per_day, special_diet, special_diet_other, regular_diet_quality, supplements, supplements_details, sleep_hours, stress_level, alcohol_frequency, nicotine_use, nicotine_details, mindfulness_practice, created_date
"""


class UpdateUserSurveyLifestyleThreeParams(pydantic.BaseModel):
    sub: str
    special_diet: Optional[str]
    special_diet_other: Optional[str]
    regular_diet_quality: Optional[str]
    supplements: Optional[str]
    supplements_details: Optional[str]


UPDATE_USER_SURVEY_LIFESTYLE_TWO = """-- name: update_user_survey_lifestyle_two \\:one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "daily_routine_activity",
    "strength_training",
    "cardio_exercise",
    "brisk_walking",
    "hours_sitting_per_day"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "daily_routine_activity" = EXCLUDED."daily_routine_activity",
  "strength_training" = EXCLUDED."strength_training",
  "cardio_exercise" = EXCLUDED."cardio_exercise",
  "brisk_walking" = EXCLUDED."brisk_walking",
  "hours_sitting_per_day" = EXCLUDED."hours_sitting_per_day"
RETURNING response_id, user_id, general_health, health_vs_last_year, height, weight, height_weight_unit_type, daily_routine_activity, strength_training, cardio_exercise, brisk_walking, hours_sitting_per_day, special_diet, special_diet_other, regular_diet_quality, supplements, supplements_details, sleep_hours, stress_level, alcohol_frequency, nicotine_use, nicotine_details, mindfulness_practice, created_date
"""


class UpdateUserSurveyLifestyleTwoParams(pydantic.BaseModel):
    sub: str
    daily_routine_activity: Optional[str]
    strength_training: Optional[str]
    cardio_exercise: Optional[str]
    brisk_walking: Optional[str]
    hours_sitting_per_day: Optional[str]


UPDATE_USER_SURVEY_PERSONAL_HEALTH_FIVE = """-- name: update_user_survey_personal_health_five \\:one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "disability",
    "disability_needs_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "disability" = EXCLUDED."disability",
  "disability_needs_details" = EXCLUDED."disability_needs_details"
RETURNING response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
"""


class UpdateUserSurveyPersonalHealthFiveParams(pydantic.BaseModel):
    sub: str
    disability: Optional[bool]
    disability_needs_details: Optional[str]


UPDATE_USER_SURVEY_PERSONAL_HEALTH_FOUR = """-- name: update_user_survey_personal_health_four \\:one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "family_health_notes"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "family_health_notes" = EXCLUDED."family_health_notes"
RETURNING response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
"""


UPDATE_USER_SURVEY_PERSONAL_HEALTH_ONE = """-- name: update_user_survey_personal_health_one \\:one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "gp_postcode",
    "ethnicity",
    "gender",
    "country"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "gp_postcode" = EXCLUDED."gp_postcode",
  "ethnicity" = EXCLUDED."ethnicity",
  "country" = EXCLUDED."country",
  "gender" = EXCLUDED."gender"
RETURNING response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
"""


class UpdateUserSurveyPersonalHealthOneParams(pydantic.BaseModel):
    sub: str
    gp_postcode: Optional[str]
    ethnicity: Optional[str]
    gender: Optional[str]
    country: Optional[str]


UPDATE_USER_SURVEY_PERSONAL_HEALTH_THREE = """-- name: update_user_survey_personal_health_three \\:one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "routine_vaccines",
    "vaccines",
    "childhood_vaccinations_status",
    "additional_vaccine_notes"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "routine_vaccines" = EXCLUDED."routine_vaccines",
  "vaccines" = EXCLUDED."vaccines",
  "childhood_vaccinations_status" = EXCLUDED."childhood_vaccinations_status",
  "additional_vaccine_notes" = EXCLUDED."additional_vaccine_notes"
RETURNING response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
"""


class UpdateUserSurveyPersonalHealthThreeParams(pydantic.BaseModel):
    sub: str
    routine_vaccines: Optional[List[str]]
    vaccines: Optional[List[str]]
    childhood_vaccinations_status: Optional[str]
    additional_vaccine_notes: Optional[str]


UPDATE_USER_SURVEY_PERSONAL_HEALTH_TWO = """-- name: update_user_survey_personal_health_two \\:one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "current_health_condition",
    "historic_health_condition",
    "injuries",
    "allergies",
    "medications"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "current_health_condition" = EXCLUDED."current_health_condition",
  "historic_health_condition" = EXCLUDED."historic_health_condition",
  "injuries" = EXCLUDED."injuries",
  "allergies" = EXCLUDED."allergies",
  "medications" = EXCLUDED."medications"
RETURNING response_id, user_id, gp_postcode, ethnicity, gender, country, current_health_condition, historic_health_condition, injuries, allergies, medications, routine_vaccines, vaccines, childhood_vaccinations_status, additional_vaccine_notes, family_health_notes, disability, disability_needs_details, created_date
"""


class UpdateUserSurveyPersonalHealthTwoParams(pydantic.BaseModel):
    sub: str
    current_health_condition: bool
    historic_health_condition: bool
    injuries: bool
    allergies: bool
    medications: bool


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_FIVE = """-- name: update_user_survey_reproductive_health_five \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "diagnosed_conditions",
    "other_conditions_details",
    "pcos_screener_irregular_periods",
    "pcos_screener_excessive_hair_growth",
    "pcos_screener_overweight_16_40",
    "pcos_screener_nipple_discharge",
    "endopain_period_pain",
    "endopain_pain_between_periods",
    "endopain_worsening_pain",
    "endopain_prolonged_period_pain",
    "endopain_stabbing_pain",
    "endopain_radiating_back_pain",
    "endopain_hip_or_leg_pain",
    "endopain_limits_daily_activities",
    "endopain_disabling_pain",
    "endopain_sexual_severe_pain",
    "endopain_sexual_position_specific_pain",
    "endopain_sexual_interrupts_sex",
    "endopain_bowel_and_bladder_pain_bowel_movements",
    "endopain_bowel_and_bladder_diarrhoea_constipation",
    "endopain_bowel_and_bladder_bowel_cramps",
    "endopain_bowel_and_bladder_urination_pain",
    "endopain_bowel_and_bladder_bladder_discomfort"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7,
    :p8,
    :p9,
    :p10,
    :p11,
    :p12,
    :p13,
    :p14,
    :p15,
    :p16,
    :p17,
    :p18,
    :p19,
    :p20,
    :p21,
    :p22,
    :p23,
    :p24
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "diagnosed_conditions" = EXCLUDED."diagnosed_conditions",
  "other_conditions_details" = EXCLUDED."other_conditions_details",
  "pcos_screener_irregular_periods" = EXCLUDED."pcos_screener_irregular_periods",
  "pcos_screener_excessive_hair_growth" = EXCLUDED."pcos_screener_excessive_hair_growth",
  "pcos_screener_overweight_16_40" = EXCLUDED."pcos_screener_overweight_16_40",
  "pcos_screener_nipple_discharge" = EXCLUDED."pcos_screener_nipple_discharge",
  "endopain_period_pain" = EXCLUDED."endopain_period_pain",
  "endopain_pain_between_periods" = EXCLUDED."endopain_pain_between_periods",
  "endopain_worsening_pain" = EXCLUDED."endopain_worsening_pain",
  "endopain_prolonged_period_pain" = EXCLUDED."endopain_prolonged_period_pain",
  "endopain_stabbing_pain" = EXCLUDED."endopain_stabbing_pain",
  "endopain_radiating_back_pain" = EXCLUDED."endopain_radiating_back_pain",
  "endopain_hip_or_leg_pain" = EXCLUDED."endopain_hip_or_leg_pain",
  "endopain_limits_daily_activities" = EXCLUDED."endopain_limits_daily_activities",
  "endopain_disabling_pain" = EXCLUDED."endopain_disabling_pain",
  "endopain_sexual_severe_pain" = EXCLUDED."endopain_sexual_severe_pain",
  "endopain_sexual_position_specific_pain" = EXCLUDED."endopain_sexual_position_specific_pain",
  "endopain_sexual_interrupts_sex" = EXCLUDED."endopain_sexual_interrupts_sex",
  "endopain_bowel_and_bladder_pain_bowel_movements" = EXCLUDED."endopain_bowel_and_bladder_pain_bowel_movements",
  "endopain_bowel_and_bladder_diarrhoea_constipation" = EXCLUDED."endopain_bowel_and_bladder_diarrhoea_constipation",
  "endopain_bowel_and_bladder_bowel_cramps" = EXCLUDED."endopain_bowel_and_bladder_bowel_cramps",
  "endopain_bowel_and_bladder_urination_pain" = EXCLUDED."endopain_bowel_and_bladder_urination_pain",
  "endopain_bowel_and_bladder_bladder_discomfort" = EXCLUDED."endopain_bowel_and_bladder_bladder_discomfort"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthFiveParams(pydantic.BaseModel):
    sub: str
    diagnosed_conditions: Optional[List[str]]
    other_conditions_details: Optional[str]
    pcos_screener_irregular_periods: Optional[bool]
    pcos_screener_excessive_hair_growth: Optional[bool]
    pcos_screener_overweight_16_40: Optional[bool]
    pcos_screener_nipple_discharge: Optional[bool]
    endopain_period_pain: Optional[bool]
    endopain_pain_between_periods: Optional[bool]
    endopain_worsening_pain: Optional[bool]
    endopain_prolonged_period_pain: Optional[bool]
    endopain_stabbing_pain: Optional[bool]
    endopain_radiating_back_pain: Optional[bool]
    endopain_hip_or_leg_pain: Optional[bool]
    endopain_limits_daily_activities: Optional[bool]
    endopain_disabling_pain: Optional[bool]
    endopain_sexual_severe_pain: Optional[bool]
    endopain_sexual_position_specific_pain: Optional[bool]
    endopain_sexual_interrupts_sex: Optional[bool]
    endopain_bowel_and_bladder_pain_bowel_movements: Optional[bool]
    endopain_bowel_and_bladder_diarrhoea_constipation: Optional[bool]
    endopain_bowel_and_bladder_bowel_cramps: Optional[bool]
    endopain_bowel_and_bladder_urination_pain: Optional[bool]
    endopain_bowel_and_bladder_bladder_discomfort: Optional[bool]


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_FOUR = """-- name: update_user_survey_reproductive_health_four \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "currently_pregnant",
    "pregnancies_total",
    "pregnancies_live_births",
    "pregnancies_stillbirths",
    "pregnancies_ectopics",
    "pregnancies_miscarriages",
    "pregnancies_terminations",
    "currently_breastfeeding",
    "tried_to_conceive_12_months",
    "fertility_testing",
    "fertility_testing_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7,
    :p8,
    :p9,
    :p10,
    :p11,
    :p12
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "currently_pregnant" = EXCLUDED."currently_pregnant",
  "pregnancies_total" = EXCLUDED."pregnancies_total",
  "pregnancies_live_births" = EXCLUDED."pregnancies_live_births",
  "pregnancies_stillbirths" = EXCLUDED."pregnancies_stillbirths",
  "pregnancies_ectopics" = EXCLUDED."pregnancies_ectopics",
  "pregnancies_miscarriages" = EXCLUDED."pregnancies_miscarriages",
  "pregnancies_terminations" = EXCLUDED."pregnancies_terminations",
  "currently_breastfeeding" = EXCLUDED."currently_breastfeeding",
  "tried_to_conceive_12_months" = EXCLUDED."tried_to_conceive_12_months",
  "fertility_testing" = EXCLUDED."fertility_testing",
  "fertility_testing_details" = EXCLUDED."fertility_testing_details"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthFourParams(pydantic.BaseModel):
    sub: str
    currently_pregnant: Optional[str]
    pregnancies_total: Optional[int]
    pregnancies_live_births: Optional[int]
    pregnancies_stillbirths: Optional[int]
    pregnancies_ectopics: Optional[int]
    pregnancies_miscarriages: Optional[int]
    pregnancies_terminations: Optional[int]
    currently_breastfeeding: Optional[str]
    tried_to_conceive_12_months: Optional[str]
    fertility_testing: Optional[str]
    fertility_testing_details: Optional[str]


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_ONE = """-- name: update_user_survey_reproductive_health_one \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "reproductive_organs",
    "reproductive_organ_details",
    "reproductive_surgeries",
    "surgery_other_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "reproductive_organs" = EXCLUDED."reproductive_organs",
  "reproductive_organ_details" = EXCLUDED."reproductive_organ_details",
  "reproductive_surgeries" = EXCLUDED."reproductive_surgeries",
  "surgery_other_details" = EXCLUDED."surgery_other_details"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthOneParams(pydantic.BaseModel):
    sub: str
    reproductive_organs: Optional[str]
    reproductive_organ_details: Optional[str]
    reproductive_surgeries: Optional[List[str]]
    surgery_other_details: Optional[str]


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_SIX = """-- name: update_user_survey_reproductive_health_six \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "cycles_irregular_past_12_months",
    "symptoms",
    "menopause_status"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "cycles_irregular_past_12_months" = EXCLUDED."cycles_irregular_past_12_months",
  "symptoms" = EXCLUDED."symptoms",
  "menopause_status" = EXCLUDED."menopause_status"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthSixParams(pydantic.BaseModel):
    sub: str
    cycles_irregular_past_12_months: Optional[str]
    symptoms: Optional[List[str]]
    menopause_status: Optional[str]


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_THREE = """-- name: update_user_survey_reproductive_health_three \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "currently_using_contraception",
    "current_contraception_details",
    "ever_used_contraception",
    "past_contraception_details",
    "currently_using_hrt",
    "current_hrt_details",
    "menstrual_status_at_hrt_start"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7,
    :p8
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "currently_using_contraception" = EXCLUDED."currently_using_contraception",
  "current_contraception_details" = EXCLUDED."current_contraception_details",
  "ever_used_contraception" = EXCLUDED."ever_used_contraception",
  "past_contraception_details" = EXCLUDED."past_contraception_details",
  "currently_using_hrt" = EXCLUDED."currently_using_hrt",
  "current_hrt_details" = EXCLUDED."current_hrt_details",
  "menstrual_status_at_hrt_start" = EXCLUDED."menstrual_status_at_hrt_start"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthThreeParams(pydantic.BaseModel):
    sub: str
    currently_using_contraception: Optional[str]
    current_contraception_details: Optional[str]
    ever_used_contraception: Optional[str]
    past_contraception_details: Optional[str]
    currently_using_hrt: Optional[str]
    current_hrt_details: Optional[str]
    menstrual_status_at_hrt_start: Optional[str]


UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_TWO = """-- name: update_user_survey_reproductive_health_two \\:one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "ever_menstruated",
    "menarche_age",
    "menstruated_last_12_months",
    "last_period_date",
    "cycle_length",
    "menstrual_symptoms",
    "menstrual_symptoms_other"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = :p1 AND "is_deleted" = FALSE),
    :p2,
    :p3,
    :p4,
    :p5,
    :p6,
    :p7,
    :p8
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "ever_menstruated" = EXCLUDED."ever_menstruated",
  "menarche_age" = EXCLUDED."menarche_age",
  "menstruated_last_12_months" = EXCLUDED."menstruated_last_12_months",
  "last_period_date" = EXCLUDED."last_period_date",
  "cycle_length" = EXCLUDED."cycle_length",
  "menstrual_symptoms" = EXCLUDED."menstrual_symptoms",
  "menstrual_symptoms_other" = EXCLUDED."menstrual_symptoms_other"
RETURNING response_id, user_id, reproductive_organs, reproductive_organ_details, reproductive_surgeries, surgery_other_details, ever_menstruated, menarche_age, menstruated_last_12_months, last_period_date, cycle_length, menstrual_symptoms, menstrual_symptoms_other, currently_using_contraception, current_contraception_details, ever_used_contraception, past_contraception_details, currently_using_hrt, current_hrt_details, menstrual_status_at_hrt_start, currently_pregnant, pregnancies_total, pregnancies_live_births, pregnancies_stillbirths, pregnancies_ectopics, pregnancies_miscarriages, pregnancies_terminations, currently_breastfeeding, tried_to_conceive_12_months, fertility_testing, fertility_testing_details, diagnosed_conditions, other_conditions_details, pcos_screener_irregular_periods, pcos_screener_excessive_hair_growth, pcos_screener_overweight_16_40, pcos_screener_nipple_discharge, endopain_period_pain, endopain_pain_between_periods, endopain_worsening_pain, endopain_prolonged_period_pain, endopain_stabbing_pain, endopain_radiating_back_pain, endopain_hip_or_leg_pain, endopain_limits_daily_activities, endopain_disabling_pain, endopain_sexual_severe_pain, endopain_sexual_position_specific_pain, endopain_sexual_interrupts_sex, endopain_bowel_and_bladder_pain_bowel_movements, endopain_bowel_and_bladder_diarrhoea_constipation, endopain_bowel_and_bladder_bowel_cramps, endopain_bowel_and_bladder_urination_pain, endopain_bowel_and_bladder_bladder_discomfort, cycles_irregular_past_12_months, symptoms, menopause_status, created_date
"""


class UpdateUserSurveyReproductiveHealthTwoParams(pydantic.BaseModel):
    sub: str
    ever_menstruated: Optional[str]
    menarche_age: Optional[str]
    menstruated_last_12_months: Optional[str]
    last_period_date: Optional[str]
    cycle_length: Optional[str]
    menstrual_symptoms: Optional[List[str]]
    menstrual_symptoms_other: Optional[str]


VALID_SURVEY_QUESTION = """-- name: valid_survey_question \\:one
SELECT
  q.question_id, q.key, q.question, q.type, q.options, q.created_date, q.is_deleted, q.deleted_date
FROM
  "question" q
  INNER JOIN "survey_question" sq ON q."question_id" = sq."question_id"
WHERE
  sq."survey_id" = :p1
  AND sq."question_id" = :p2
"""


VALID_USER = """-- name: valid_user \\:one

SELECT
  "sub"
FROM
  "user"
WHERE
  "sub" = :p1
  AND is_deleted = FALSE
"""


WORKER_CREATE_USER_SCREENING_ALERT = """-- name: worker_create_user_screening_alert \\:one
INSERT INTO "user_screening_alert" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "suggested_months_between_appointments",
  "notes"
)
VALUES (
  :p1,
  :p2,
  :p3,
  :p4,
  :p5,
  :p6
) RETURNING alert_id, user_id, type, subtype, next_date, suggested_months_between_appointments, notes, status, created_date, is_deleted, deleted_date
"""


class WorkerCreateUserScreeningAlertParams(pydantic.BaseModel):
    user_id: str
    type: models.UserScreeningType
    subtype: Optional[str]
    next_date: Optional[datetime.date]
    suggested_months_between_appointments: Optional[int]
    notes: Optional[str]


WORKER_GET_USER_DOCUMENT_BY_ID = """-- name: worker_get_user_document_by_id \\:one
SELECT
  document_id, user_id, label, type, mime_type, json_output, created_date, is_deleted, deleted_date
FROM
  "user_document"
WHERE
  "document_id" = :p1
  AND "is_deleted" = FALSE
"""


WORKER_LIST_SHARED_HEALTH_EVENTS = """-- name: worker_list_shared_health_events \\:many
SELECT
  user_health_event.event_id, user_health_event.user_id, user_health_event.type, user_health_event.details, user_health_event.notes, user_health_event.start_date, user_health_event.end_date, user_health_event.ongoing, user_health_event.genetic, user_health_event.is_reviewed, user_health_event.created_date, user_health_event.is_deleted, user_health_event.deleted_date,
  user_share.share_id, user_share.sharer_id, user_share.sharee_id, user_share.sharee_relationship_to_sharer, user_share.label_for_sharer, user_share.label_for_sharee, user_share.approved, user_share.approved_date, user_share.created_date, user_share.is_deleted, user_share.deleted_date
FROM
  "user_health_event"
  INNER JOIN "user_share" ON "user_health_event"."user_id" = "user_share"."sharer_id"
WHERE
  "user_share"."sharee_id" = :p1
  AND "user_health_event"."genetic" = TRUE
  AND "user_share"."is_deleted" = FALSE
  AND "user_health_event"."is_deleted" = FALSE
"""


class WorkerListSharedHealthEventsRow(pydantic.BaseModel):
    event_id: str
    user_id: str
    type: models.UserHealthEventType
    details: str
    notes: Optional[Any]
    start_date: datetime.date
    end_date: Optional[datetime.date]
    ongoing: bool
    genetic: bool
    is_reviewed: bool
    created_date: datetime.datetime
    is_deleted: bool
    deleted_date: Optional[datetime.datetime]
    share_id: str
    sharer_id: str
    sharee_id: str
    sharee_relationship_to_sharer: models.UserRelationship
    label_for_sharer: str
    label_for_sharee: str
    approved: bool
    approved_date: Optional[datetime.datetime]
    created_date_2: datetime.datetime
    is_deleted_2: bool
    deleted_date_2: Optional[datetime.datetime]


WORKER_LIST_SHARES_BY_SHARER_ID = """-- name: worker_list_shares_by_sharer_id \\:many
SELECT
  share_id, sharer_id, sharee_id, sharee_relationship_to_sharer, label_for_sharer, label_for_sharee, approved, approved_date, created_date, is_deleted, deleted_date
FROM
  "user_share"
WHERE
  "sharer_id" = :p1
  AND "approved" = TRUE
  AND "is_deleted" = FALSE
"""


WORKER_LIST_USER_HEALTH_EVENTS = """-- name: worker_list_user_health_events \\:many
SELECT
  event_id, user_id, type, details, notes, start_date, end_date, ongoing, genetic, is_reviewed, created_date, is_deleted, deleted_date
FROM
  "user_health_event"
WHERE
  "user_id" = :p1
  AND "is_deleted" = FALSE
"""


WORKER_UPDATE_USER_DOCUMENT_BY_ID = """-- name: worker_update_user_document_by_id \\:one
UPDATE
  "user_document"
SET
  "json_output" = :p2
WHERE
  "document_id" = :p1
  AND "is_deleted" = FALSE
RETURNING "document_id"
"""


class Querier:
    def __init__(self, conn: sqlalchemy.engine.Connection):
        self._conn = conn

    def add_user_reward_point(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def add_user_reward_point_for_instagram(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT_FOR_INSTAGRAM), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def add_user_reward_point_for_lifestyle_survey(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT_FOR_LIFESTYLE_SURVEY), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def add_user_reward_point_for_personal_health_survey(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT_FOR_PERSONAL_HEALTH_SURVEY), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def add_user_reward_point_for_reproductive_health_survey(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT_FOR_REPRODUCTIVE_HEALTH_SURVEY), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def add_user_reward_point_for_sharing(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(ADD_USER_REWARD_POINT_FOR_SHARING), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def approve_user_share(self, *, sub: str, share_id: str) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(APPROVE_USER_SHARE), {"p1": sub, "p2": share_id}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def assign_username(self) -> Optional[models.Username]:
        row = self._conn.execute(sqlalchemy.text(ASSIGN_USERNAME)).first()
        if row is None:
            return None
        return models.Username(
            username=row[0],
            referral_code=row[1],
            assigned=row[2],
        )

    def count_active_users_by_date(self, *, start_date: datetime.datetime) -> Optional[int]:
        row = self._conn.execute(sqlalchemy.text(COUNT_ACTIVE_USERS_BY_DATE), {"p1": start_date}).first()
        if row is None:
            return None
        return row[0]

    def count_new_user_screening_alerts(self, *, sub: str) -> Optional[int]:
        row = self._conn.execute(sqlalchemy.text(COUNT_NEW_USER_SCREENING_ALERTS), {"p1": sub}).first()
        if row is None:
            return None
        return row[0]

    def count_outgoing_user_shares(self, *, sub: str) -> Optional[int]:
        row = self._conn.execute(sqlalchemy.text(COUNT_OUTGOING_USER_SHARES), {"p1": sub}).first()
        if row is None:
            return None
        return row[0]

    def count_user_referrals(self, *, sub: str) -> Optional[int]:
        row = self._conn.execute(sqlalchemy.text(COUNT_USER_REFERRALS), {"p1": sub}).first()
        if row is None:
            return None
        return row[0]

    def count_users_for_date(self, *, start_date: datetime.datetime, end_date: datetime.datetime) -> Optional[int]:
        row = self._conn.execute(sqlalchemy.text(COUNT_USERS_FOR_DATE), {"p1": start_date, "p2": end_date}).first()
        if row is None:
            return None
        return row[0]

    def create_appointment_support_request(self, arg: CreateAppointmentSupportRequestParams) -> Optional[models.UserAppointmentSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(CREATE_APPOINTMENT_SUPPORT_REQUEST), {
            "p1": arg.sub,
            "p2": arg.response_output,
            "p3": arg.appointment_type,
            "p4": arg.appointment_details,
            "p5": arg.rating,
        }).first()
        if row is None:
            return None
        return models.UserAppointmentSupportRequest(
            request_id=row[0],
            user_id=row[1],
            response_output=row[2],
            appointment_type=row[3],
            appointment_details=row[4],
            rating=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def create_daily_user_count(self, arg: CreateDailyUserCountParams) -> Optional[CreateDailyUserCountRow]:
        row = self._conn.execute(sqlalchemy.text(CREATE_DAILY_USER_COUNT), {
            "p1": arg.date,
            "p2": arg.d_new_user_count,
            "p3": arg.d_total_user_count,
            "p4": arg.d_growth,
            "p5": arg.wk_new_user_count,
            "p6": arg.wk_total_user_count,
            "p7": arg.wk_growth,
            "p8": arg.mth_new_user_count,
            "p9": arg.mth_total_user_count,
            "p10": arg.mth_growth,
        }).first()
        if row is None:
            return None
        return CreateDailyUserCountRow(
            date=row[0],
            d_new_user_count=row[1],
            d_total_user_count=row[2],
            d_growth=row[3],
            wk_new_user_count=row[4],
            wk_cur_total_user_count=row[5],
            wk_growth=row[6],
            mth_new_user_count=row[7],
            mth_cur_total_user_count=row[8],
            mth_growth=row[9],
        )

    def create_magic_link(self, *, token: str, metadata: Any) -> Optional[models.MagicLink]:
        row = self._conn.execute(sqlalchemy.text(CREATE_MAGIC_LINK), {"p1": token, "p2": metadata}).first()
        if row is None:
            return None
        return models.MagicLink(
            link_id=row[0],
            token=row[1],
            metadata=row[2],
            expires_at=row[3],
        )

    def create_question_support_request(self, arg: CreateQuestionSupportRequestParams) -> Optional[models.UserQuestionSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(CREATE_QUESTION_SUPPORT_REQUEST), {
            "p1": arg.sub,
            "p2": arg.question,
            "p3": arg.response_output,
            "p4": arg.rating,
        }).first()
        if row is None:
            return None
        return models.UserQuestionSupportRequest(
            request_id=row[0],
            user_id=row[1],
            question=row[2],
            response_output=row[3],
            rating=row[4],
            created_date=row[5],
            is_deleted=row[6],
            deleted_date=row[7],
        )

    def create_user(self, arg: CreateUserParams) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER), {
            "p1": arg.sub,
            "p2": arg.username,
            "p3": arg.email,
            "p4": arg.birth_month,
            "p5": arg.birth_year,
            "p6": arg.sex_assigned_at_birth,
            "p7": arg.referral_code,
            "p8": arg.referrer,
        }).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def create_user_document(self, *, sub: str) -> Optional[models.UserDocument]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_DOCUMENT), {"p1": sub}).first()
        if row is None:
            return None
        return models.UserDocument(
            document_id=row[0],
            user_id=row[1],
            label=row[2],
            type=row[3],
            mime_type=row[4],
            json_output=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def create_user_health_event(self, arg: CreateUserHealthEventParams) -> Optional[models.UserHealthEvent]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_HEALTH_EVENT), {
            "p1": arg.sub,
            "p2": arg.type,
            "p3": arg.details,
            "p4": arg.notes,
            "p5": arg.start_date,
            "p6": arg.end_date,
            "p7": arg.ongoing,
            "p8": arg.genetic,
        }).first()
        if row is None:
            return None
        return models.UserHealthEvent(
            event_id=row[0],
            user_id=row[1],
            type=row[2],
            details=row[3],
            notes=row[4],
            start_date=row[5],
            end_date=row[6],
            ongoing=row[7],
            genetic=row[8],
            is_reviewed=row[9],
            created_date=row[10],
            is_deleted=row[11],
            deleted_date=row[12],
        )

    def create_user_reward_point_log(self, arg: CreateUserRewardPointLogParams) -> Optional[models.UserRewardPointLog]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_REWARD_POINT_LOG), {"p1": arg.sub, "p2": arg.points, "p3": arg.reason}).first()
        if row is None:
            return None
        return models.UserRewardPointLog(
            log_id=row[0],
            user_id=row[1],
            points=row[2],
            reason=row[3],
            created_date=row[4],
            is_deleted=row[5],
            deleted_date=row[6],
        )

    def create_user_screening(self, arg: CreateUserScreeningParams) -> Optional[models.UserScreening]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_SCREENING), {
            "p1": arg.sub,
            "p2": arg.type,
            "p3": arg.subtype,
            "p4": arg.next_date,
            "p5": arg.last_date,
            "p6": arg.attended_date,
            "p7": arg.months_between_appointments,
            "p8": arg.notes,
            "p9": arg.status,
            "p10": arg.user_managed_schedule,
            "p11": arg.alert_id,
        }).first()
        if row is None:
            return None
        return models.UserScreening(
            screening_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            last_date=row[5],
            attended_date=row[6],
            months_between_appointments=row[7],
            notes=row[8],
            status=row[9],
            user_managed_schedule=row[10],
            alert_id=row[11],
            created_date=row[12],
            is_deleted=row[13],
            deleted_date=row[14],
        )

    def create_user_screening_alert(self, arg: CreateUserScreeningAlertParams) -> Optional[models.UserScreeningAlert]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_SCREENING_ALERT), {
            "p1": arg.sub,
            "p2": arg.type,
            "p3": arg.subtype,
            "p4": arg.next_date,
            "p5": arg.suggested_months_between_appointments,
            "p6": arg.notes,
        }).first()
        if row is None:
            return None
        return models.UserScreeningAlert(
            alert_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            suggested_months_between_appointments=row[5],
            notes=row[6],
            status=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def create_user_share(self, arg: CreateUserShareParams) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_SHARE), {
            "p1": arg.sub,
            "p2": arg.username,
            "p3": arg.email,
            "p4": arg.sharee_relationship_to_sharer,
            "p5": arg.label_for_sharer,
            "p6": arg.label_for_sharee,
        }).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def create_user_survey_response(self, arg: CreateUserSurveyResponseParams) -> Optional[models.UserSurveyResponse]:
        row = self._conn.execute(sqlalchemy.text(CREATE_USER_SURVEY_RESPONSE), {
            "p1": arg.sub,
            "p2": arg.survey_id,
            "p3": arg.question_id,
            "p4": arg.selected_options,
            "p5": arg.answer,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponse(
            response_id=row[0],
            user_id=row[1],
            survey_id=row[2],
            question_id=row[3],
            selected_options=row[4],
            answer=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def delete_all_user_shares(self, *, sub: str) -> Iterator[models.UserShare]:
        result = self._conn.execute(sqlalchemy.text(DELETE_ALL_USER_SHARES), {"p1": sub})
        for row in result:
            yield models.UserShare(
                share_id=row[0],
                sharer_id=row[1],
                sharee_id=row[2],
                sharee_relationship_to_sharer=row[3],
                label_for_sharer=row[4],
                label_for_sharee=row[5],
                approved=row[6],
                approved_date=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def delete_appointment_support_request(self, *, sub: str, request_id: str) -> Optional[models.UserAppointmentSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(DELETE_APPOINTMENT_SUPPORT_REQUEST), {"p1": sub, "p2": request_id}).first()
        if row is None:
            return None
        return models.UserAppointmentSupportRequest(
            request_id=row[0],
            user_id=row[1],
            response_output=row[2],
            appointment_type=row[3],
            appointment_details=row[4],
            rating=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def delete_user(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(DELETE_USER), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def delete_user_document(self, *, sub: str, document_id: str) -> Optional[models.UserDocument]:
        row = self._conn.execute(sqlalchemy.text(DELETE_USER_DOCUMENT), {"p1": sub, "p2": document_id}).first()
        if row is None:
            return None
        return models.UserDocument(
            document_id=row[0],
            user_id=row[1],
            label=row[2],
            type=row[3],
            mime_type=row[4],
            json_output=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def delete_user_health_event(self, *, sub: str, event_id: str) -> Optional[models.UserHealthEvent]:
        row = self._conn.execute(sqlalchemy.text(DELETE_USER_HEALTH_EVENT), {"p1": sub, "p2": event_id}).first()
        if row is None:
            return None
        return models.UserHealthEvent(
            event_id=row[0],
            user_id=row[1],
            type=row[2],
            details=row[3],
            notes=row[4],
            start_date=row[5],
            end_date=row[6],
            ongoing=row[7],
            genetic=row[8],
            is_reviewed=row[9],
            created_date=row[10],
            is_deleted=row[11],
            deleted_date=row[12],
        )

    def delete_user_screening(self, *, sub: str, screening_id: str) -> Optional[models.UserScreening]:
        row = self._conn.execute(sqlalchemy.text(DELETE_USER_SCREENING), {"p1": sub, "p2": screening_id}).first()
        if row is None:
            return None
        return models.UserScreening(
            screening_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            last_date=row[5],
            attended_date=row[6],
            months_between_appointments=row[7],
            notes=row[8],
            status=row[9],
            user_managed_schedule=row[10],
            alert_id=row[11],
            created_date=row[12],
            is_deleted=row[13],
            deleted_date=row[14],
        )

    def delete_user_share(self, *, sub: str, share_id: str) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(DELETE_USER_SHARE), {"p1": sub, "p2": share_id}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def get_all_surveys(self) -> Iterator[models.Survey]:
        result = self._conn.execute(sqlalchemy.text(GET_ALL_SURVEYS))
        for row in result:
            yield models.Survey(
                survey_id=row[0],
                title=row[1],
                description=row[2],
                criteria=row[3],
                created_date=row[4],
                is_deleted=row[5],
                deleted_date=row[6],
            )

    def get_appointment_support_request(self, *, sub: str, request_id: str) -> Optional[models.UserAppointmentSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(GET_APPOINTMENT_SUPPORT_REQUEST), {"p1": sub, "p2": request_id}).first()
        if row is None:
            return None
        return models.UserAppointmentSupportRequest(
            request_id=row[0],
            user_id=row[1],
            response_output=row[2],
            appointment_type=row[3],
            appointment_details=row[4],
            rating=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def get_daily_user_count(self, *, date: datetime.date) -> Iterator[models.DailyUserCount]:
        result = self._conn.execute(sqlalchemy.text(GET_DAILY_USER_COUNT), {"p1": date})
        for row in result:
            yield models.DailyUserCount(
                date_id=row[0],
                date=row[1],
                d_new_user_count=row[2],
                d_total_user_count=row[3],
                d_growth_rate=row[4],
                wk_new_user_count=row[5],
                wk_growth_rate=row[6],
                wk_total_user_count=row[7],
                mth_new_user_count=row[8],
                mth_total_user_count=row[9],
                mth_growth_rate=row[10],
                created_date=row[11],
            )

    def get_last_processed_date(self) -> Optional[Any]:
        row = self._conn.execute(sqlalchemy.text(GET_LAST_PROCESSED_DATE)).first()
        if row is None:
            return None
        return row[0]

    def get_magic_link(self, *, token: str) -> Optional[models.MagicLink]:
        row = self._conn.execute(sqlalchemy.text(GET_MAGIC_LINK), {"p1": token}).first()
        if row is None:
            return None
        return models.MagicLink(
            link_id=row[0],
            token=row[1],
            metadata=row[2],
            expires_at=row[3],
        )

    def get_question_by_id(self, *, question_id: str) -> Optional[models.Question]:
        row = self._conn.execute(sqlalchemy.text(GET_QUESTION_BY_ID), {"p1": question_id}).first()
        if row is None:
            return None
        return models.Question(
            question_id=row[0],
            key=row[1],
            question=row[2],
            type=row[3],
            options=row[4],
            created_date=row[5],
            is_deleted=row[6],
            deleted_date=row[7],
        )

    def get_question_support_request(self, *, sub: str, request_id: str) -> Optional[models.UserQuestionSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(GET_QUESTION_SUPPORT_REQUEST), {"p1": sub, "p2": request_id}).first()
        if row is None:
            return None
        return models.UserQuestionSupportRequest(
            request_id=row[0],
            user_id=row[1],
            question=row[2],
            response_output=row[3],
            rating=row[4],
            created_date=row[5],
            is_deleted=row[6],
            deleted_date=row[7],
        )

    def get_survey_by_id(self, *, survey_id: str) -> Optional[models.Survey]:
        row = self._conn.execute(sqlalchemy.text(GET_SURVEY_BY_ID), {"p1": survey_id}).first()
        if row is None:
            return None
        return models.Survey(
            survey_id=row[0],
            title=row[1],
            description=row[2],
            criteria=row[3],
            created_date=row[4],
            is_deleted=row[5],
            deleted_date=row[6],
        )

    def get_survey_questions(self, *, survey_id: str) -> Iterator[models.Question]:
        result = self._conn.execute(sqlalchemy.text(GET_SURVEY_QUESTIONS), {"p1": survey_id})
        for row in result:
            yield models.Question(
                question_id=row[0],
                key=row[1],
                question=row[2],
                type=row[3],
                options=row[4],
                created_date=row[5],
                is_deleted=row[6],
                deleted_date=row[7],
            )

    def get_unanswered_questions_for_survey(self, *, survey_id: str, sub: str) -> Iterator[models.Question]:
        result = self._conn.execute(sqlalchemy.text(GET_UNANSWERED_QUESTIONS_FOR_SURVEY), {"p1": survey_id, "p2": sub})
        for row in result:
            yield models.Question(
                question_id=row[0],
                key=row[1],
                question=row[2],
                type=row[3],
                options=row[4],
                created_date=row[5],
                is_deleted=row[6],
                deleted_date=row[7],
            )

    def get_user_by_id(self, *, user_id: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_BY_ID), {"p1": user_id}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def get_user_by_referral_code(self, *, referral_code: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_BY_REFERRAL_CODE), {"p1": referral_code}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def get_user_by_sub(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_BY_SUB), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def get_user_dob(self) -> Iterator[GetUserDOBRow]:
        result = self._conn.execute(sqlalchemy.text(GET_USER_DOB))
        for row in result:
            yield GetUserDOBRow(
                birth_year=row[0],
                birth_month=row[1],
            )

    def get_user_document(self, *, sub: str, document_id: str) -> Optional[models.UserDocument]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_DOCUMENT), {"p1": sub, "p2": document_id}).first()
        if row is None:
            return None
        return models.UserDocument(
            document_id=row[0],
            user_id=row[1],
            label=row[2],
            type=row[3],
            mime_type=row[4],
            json_output=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def get_user_health_event(self, *, sub: str, event_id: str) -> Optional[models.UserHealthEvent]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_HEALTH_EVENT), {"p1": sub, "p2": event_id}).first()
        if row is None:
            return None
        return models.UserHealthEvent(
            event_id=row[0],
            user_id=row[1],
            type=row[2],
            details=row[3],
            notes=row[4],
            start_date=row[5],
            end_date=row[6],
            ongoing=row[7],
            genetic=row[8],
            is_reviewed=row[9],
            created_date=row[10],
            is_deleted=row[11],
            deleted_date=row[12],
        )

    def get_user_responses_for_survey(self, *, sub: str, survey_id: str) -> Iterator[models.UserSurveyResponse]:
        result = self._conn.execute(sqlalchemy.text(GET_USER_RESPONSES_FOR_SURVEY), {"p1": sub, "p2": survey_id})
        for row in result:
            yield models.UserSurveyResponse(
                response_id=row[0],
                user_id=row[1],
                survey_id=row[2],
                question_id=row[3],
                selected_options=row[4],
                answer=row[5],
                created_date=row[6],
                is_deleted=row[7],
                deleted_date=row[8],
            )

    def get_user_screening(self, *, screening_id: str, sub: str) -> Optional[models.UserScreening]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SCREENING), {"p1": screening_id, "p2": sub}).first()
        if row is None:
            return None
        return models.UserScreening(
            screening_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            last_date=row[5],
            attended_date=row[6],
            months_between_appointments=row[7],
            notes=row[8],
            status=row[9],
            user_managed_schedule=row[10],
            alert_id=row[11],
            created_date=row[12],
            is_deleted=row[13],
            deleted_date=row[14],
        )

    def get_user_screening_alert(self, *, sub: str, alert_id: str) -> Optional[models.UserScreeningAlert]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SCREENING_ALERT), {"p1": sub, "p2": alert_id}).first()
        if row is None:
            return None
        return models.UserScreeningAlert(
            alert_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            suggested_months_between_appointments=row[5],
            notes=row[6],
            status=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def get_user_share(self, *, sub: str, share_id: str) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SHARE), {"p1": sub, "p2": share_id}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def get_user_survey_progress(self, *, survey_id: str, sub: str) -> Optional[GetUserSurveyProgressRow]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SURVEY_PROGRESS), {"p1": survey_id, "p2": sub}).first()
        if row is None:
            return None
        return GetUserSurveyProgressRow(
            answered_count=row[0],
            total_count=row[1],
        )

    def get_user_survey_response_lifestyle(self, *, sub: str) -> Optional[models.UserSurveyResponseLifestyle]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SURVEY_RESPONSE_LIFESTYLE), {"p1": sub}).first()
        if row is None:
            return None
        return models.UserSurveyResponseLifestyle(
            response_id=row[0],
            user_id=row[1],
            general_health=row[2],
            health_vs_last_year=row[3],
            height=row[4],
            weight=row[5],
            height_weight_unit_type=row[6],
            daily_routine_activity=row[7],
            strength_training=row[8],
            cardio_exercise=row[9],
            brisk_walking=row[10],
            hours_sitting_per_day=row[11],
            special_diet=row[12],
            special_diet_other=row[13],
            regular_diet_quality=row[14],
            supplements=row[15],
            supplements_details=row[16],
            sleep_hours=row[17],
            stress_level=row[18],
            alcohol_frequency=row[19],
            nicotine_use=row[20],
            nicotine_details=row[21],
            mindfulness_practice=row[22],
            created_date=row[23],
        )

    def get_user_survey_response_personal_health(self, *, sub: str) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SURVEY_RESPONSE_PERSONAL_HEALTH), {"p1": sub}).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def get_user_survey_response_reproductive_health(self, *, sub: str) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(GET_USER_SURVEY_RESPONSE_REPRODUCTIVE_HEALTH), {"p1": sub}).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def impact_endometriosis(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_ENDOMETRIOSIS))

    def impact_female(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_FEMALE))

    def impact_live_births(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_LIVE_BIRTHS))

    def impact_miscarriages(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_MISCARRIAGES))

    def impact_pcos(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_PCOS))

    def impact_possible_undiagnosed_endometriosis(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_POSSIBLE_UNDIAGNOSED_ENDOMETRIOSIS))

    def impact_possible_undiagnosed_pcos(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_POSSIBLE_UNDIAGNOSED_PCOS))

    def impact_trying_to_conceive(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_TRYING_TO_CONCEIVE))

    def impact_update_points(self) -> None:
        self._conn.execute(sqlalchemy.text(IMPACT_UPDATE_POINTS))

    def insert_daily_user_count(self, arg: InsertDailyUserCountParams) -> Optional[models.DailyUserCount]:
        row = self._conn.execute(sqlalchemy.text(INSERT_DAILY_USER_COUNT), {
            "p1": arg.date,
            "p2": arg.d_new_user_count,
            "p3": arg.d_total_user_count,
            "p4": arg.d_growth_rate,
            "p5": arg.wk_new_user_count,
            "p6": arg.wk_growth_rate,
            "p7": arg.wk_total_user_count,
            "p8": arg.mth_new_user_count,
            "p9": arg.mth_total_user_count,
            "p10": arg.mth_growth_rate,
        }).first()
        if row is None:
            return None
        return models.DailyUserCount(
            date_id=row[0],
            date=row[1],
            d_new_user_count=row[2],
            d_total_user_count=row[3],
            d_growth_rate=row[4],
            wk_new_user_count=row[5],
            wk_growth_rate=row[6],
            wk_total_user_count=row[7],
            mth_new_user_count=row[8],
            mth_total_user_count=row[9],
            mth_growth_rate=row[10],
            created_date=row[11],
        )

    def list_incoming_user_shares(self, *, sub: str) -> Iterator[models.UserShare]:
        result = self._conn.execute(sqlalchemy.text(LIST_INCOMING_USER_SHARES), {"p1": sub})
        for row in result:
            yield models.UserShare(
                share_id=row[0],
                sharer_id=row[1],
                sharee_id=row[2],
                sharee_relationship_to_sharer=row[3],
                label_for_sharer=row[4],
                label_for_sharee=row[5],
                approved=row[6],
                approved_date=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def list_matching_screenings(self, arg: ListMatchingScreeningsParams) -> Iterator[models.UserScreening]:
        result = self._conn.execute(sqlalchemy.text(LIST_MATCHING_SCREENINGS), {
            "p1": arg.sub,
            "p2": arg.type,
            "p3": arg.subtype,
            "p4": arg.status,
        })
        for row in result:
            yield models.UserScreening(
                screening_id=row[0],
                user_id=row[1],
                type=row[2],
                subtype=row[3],
                next_date=row[4],
                last_date=row[5],
                attended_date=row[6],
                months_between_appointments=row[7],
                notes=row[8],
                status=row[9],
                user_managed_schedule=row[10],
                alert_id=row[11],
                created_date=row[12],
                is_deleted=row[13],
                deleted_date=row[14],
            )

    def list_new_user_screening_alerts(self, *, sub: str) -> Iterator[models.UserScreeningAlert]:
        result = self._conn.execute(sqlalchemy.text(LIST_NEW_USER_SCREENING_ALERTS), {"p1": sub})
        for row in result:
            yield models.UserScreeningAlert(
                alert_id=row[0],
                user_id=row[1],
                type=row[2],
                subtype=row[3],
                next_date=row[4],
                suggested_months_between_appointments=row[5],
                notes=row[6],
                status=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def list_outgoing_user_shares(self, *, sub: str) -> Iterator[models.UserShare]:
        result = self._conn.execute(sqlalchemy.text(LIST_OUTGOING_USER_SHARES), {"p1": sub})
        for row in result:
            yield models.UserShare(
                share_id=row[0],
                sharer_id=row[1],
                sharee_id=row[2],
                sharee_relationship_to_sharer=row[3],
                label_for_sharer=row[4],
                label_for_sharee=row[5],
                approved=row[6],
                approved_date=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def list_question_support_requests(self, *, sub: str) -> Iterator[models.UserQuestionSupportRequest]:
        result = self._conn.execute(sqlalchemy.text(LIST_QUESTION_SUPPORT_REQUESTS), {"p1": sub})
        for row in result:
            yield models.UserQuestionSupportRequest(
                request_id=row[0],
                user_id=row[1],
                question=row[2],
                response_output=row[3],
                rating=row[4],
                created_date=row[5],
                is_deleted=row[6],
                deleted_date=row[7],
            )

    def list_shared_health_events(self, *, sub: str) -> Iterator[ListSharedHealthEventsRow]:
        result = self._conn.execute(sqlalchemy.text(LIST_SHARED_HEALTH_EVENTS), {"p1": sub})
        for row in result:
            yield ListSharedHealthEventsRow(
                event_id=row[0],
                user_id=row[1],
                type=row[2],
                details=row[3],
                notes=row[4],
                start_date=row[5],
                end_date=row[6],
                ongoing=row[7],
                genetic=row[8],
                is_reviewed=row[9],
                created_date=row[10],
                is_deleted=row[11],
                deleted_date=row[12],
                share_id=row[13],
                sharer_id=row[14],
                sharee_id=row[15],
                sharee_relationship_to_sharer=row[16],
                label_for_sharer=row[17],
                label_for_sharee=row[18],
                approved=row[19],
                approved_date=row[20],
                created_date_2=row[21],
                is_deleted_2=row[22],
                deleted_date_2=row[23],
            )

    def list_user_appointment_support_requests(self, *, sub: str) -> Iterator[models.UserAppointmentSupportRequest]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_APPOINTMENT_SUPPORT_REQUESTS), {"p1": sub})
        for row in result:
            yield models.UserAppointmentSupportRequest(
                request_id=row[0],
                user_id=row[1],
                response_output=row[2],
                appointment_type=row[3],
                appointment_details=row[4],
                rating=row[5],
                created_date=row[6],
                is_deleted=row[7],
                deleted_date=row[8],
            )

    def list_user_documents(self, *, sub: str) -> Iterator[models.UserDocument]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_DOCUMENTS), {"p1": sub})
        for row in result:
            yield models.UserDocument(
                document_id=row[0],
                user_id=row[1],
                label=row[2],
                type=row[3],
                mime_type=row[4],
                json_output=row[5],
                created_date=row[6],
                is_deleted=row[7],
                deleted_date=row[8],
            )

    def list_user_health_events(self, *, sub: str) -> Iterator[models.UserHealthEvent]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_HEALTH_EVENTS), {"p1": sub})
        for row in result:
            yield models.UserHealthEvent(
                event_id=row[0],
                user_id=row[1],
                type=row[2],
                details=row[3],
                notes=row[4],
                start_date=row[5],
                end_date=row[6],
                ongoing=row[7],
                genetic=row[8],
                is_reviewed=row[9],
                created_date=row[10],
                is_deleted=row[11],
                deleted_date=row[12],
            )

    def list_user_health_events_by_type(self, *, sub: str, type: models.UserHealthEventType) -> Iterator[models.UserHealthEvent]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_HEALTH_EVENTS_BY_TYPE), {"p1": sub, "p2": type})
        for row in result:
            yield models.UserHealthEvent(
                event_id=row[0],
                user_id=row[1],
                type=row[2],
                details=row[3],
                notes=row[4],
                start_date=row[5],
                end_date=row[6],
                ongoing=row[7],
                genetic=row[8],
                is_reviewed=row[9],
                created_date=row[10],
                is_deleted=row[11],
                deleted_date=row[12],
            )

    def list_user_screening_alerts(self, *, sub: str) -> Iterator[models.UserScreeningAlert]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_SCREENING_ALERTS), {"p1": sub})
        for row in result:
            yield models.UserScreeningAlert(
                alert_id=row[0],
                user_id=row[1],
                type=row[2],
                subtype=row[3],
                next_date=row[4],
                suggested_months_between_appointments=row[5],
                notes=row[6],
                status=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def list_user_screenings(self, *, sub: str) -> Iterator[models.UserScreening]:
        result = self._conn.execute(sqlalchemy.text(LIST_USER_SCREENINGS), {"p1": sub})
        for row in result:
            yield models.UserScreening(
                screening_id=row[0],
                user_id=row[1],
                type=row[2],
                subtype=row[3],
                next_date=row[4],
                last_date=row[5],
                attended_date=row[6],
                months_between_appointments=row[7],
                notes=row[8],
                status=row[9],
                user_managed_schedule=row[10],
                alert_id=row[11],
                created_date=row[12],
                is_deleted=row[13],
                deleted_date=row[14],
            )

    def magic_link_approve_user_share(self, *, share_id: str) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(MAGIC_LINK_APPROVE_USER_SHARE), {"p1": share_id}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def mark_user_reward_point_for_referral(self, *, sub: str) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(MARK_USER_REWARD_POINT_FOR_REFERRAL), {"p1": sub}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def update_appointment_support_request_rating(self, arg: UpdateAppointmentSupportRequestRatingParams) -> Optional[models.UserAppointmentSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_APPOINTMENT_SUPPORT_REQUEST_RATING), {"p1": arg.sub, "p2": arg.request_id, "p3": arg.rating}).first()
        if row is None:
            return None
        return models.UserAppointmentSupportRequest(
            request_id=row[0],
            user_id=row[1],
            response_output=row[2],
            appointment_type=row[3],
            appointment_details=row[4],
            rating=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def update_question_support_request_rating(self, arg: UpdateQuestionSupportRequestRatingParams) -> Optional[models.UserQuestionSupportRequest]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_QUESTION_SUPPORT_REQUEST_RATING), {"p1": arg.sub, "p2": arg.request_id, "p3": arg.rating}).first()
        if row is None:
            return None
        return models.UserQuestionSupportRequest(
            request_id=row[0],
            user_id=row[1],
            question=row[2],
            response_output=row[3],
            rating=row[4],
            created_date=row[5],
            is_deleted=row[6],
            deleted_date=row[7],
        )

    def update_user_document(self, arg: UpdateUserDocumentParams) -> Optional[models.UserDocument]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_DOCUMENT), {
            "p1": arg.sub,
            "p2": arg.document_id,
            "p3": arg.label,
            "p4": arg.type,
            "p5": arg.mime_type,
        }).first()
        if row is None:
            return None
        return models.UserDocument(
            document_id=row[0],
            user_id=row[1],
            label=row[2],
            type=row[3],
            mime_type=row[4],
            json_output=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def update_user_health_event(self, arg: UpdateUserHealthEventParams) -> Optional[models.UserHealthEvent]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_HEALTH_EVENT), {
            "p1": arg.sub,
            "p2": arg.event_id,
            "p3": arg.type,
            "p4": arg.details,
            "p5": arg.notes,
            "p6": arg.start_date,
            "p7": arg.end_date,
            "p8": arg.ongoing,
        }).first()
        if row is None:
            return None
        return models.UserHealthEvent(
            event_id=row[0],
            user_id=row[1],
            type=row[2],
            details=row[3],
            notes=row[4],
            start_date=row[5],
            end_date=row[6],
            ongoing=row[7],
            genetic=row[8],
            is_reviewed=row[9],
            created_date=row[10],
            is_deleted=row[11],
            deleted_date=row[12],
        )

    def update_user_instagram_handle(self, *, sub: str, instagram_handle: Optional[str]) -> Optional[models.User]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_INSTAGRAM_HANDLE), {"p1": sub, "p2": instagram_handle}).first()
        if row is None:
            return None
        return models.User(
            user_id=row[0],
            sub=row[1],
            username=row[2],
            email=row[3],
            birth_month=row[4],
            birth_year=row[5],
            sex_assigned_at_birth=row[6],
            reward_points=row[7],
            reward_point_awarded_for_instagram=row[8],
            reward_point_awarded_for_personal_health_survey=row[9],
            reward_point_awarded_for_reproductive_health_survey=row[10],
            reward_point_awarded_for_lifestyle_survey=row[11],
            reward_point_awarded_for_sharing=row[12],
            reward_point_awarded_to_referrer=row[13],
            referral_code=row[14],
            referrer=row[15],
            instagram_handle=row[16],
            impact_points=row[17],
            created_date=row[18],
            is_deleted=row[19],
            deleted_date=row[20],
        )

    def update_user_screening(self, arg: UpdateUserScreeningParams) -> Optional[models.UserScreening]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SCREENING), {
            "p1": arg.sub,
            "p2": arg.screening_id,
            "p3": arg.next_date,
            "p4": arg.last_date,
            "p5": arg.attended_date,
            "p6": arg.months_between_appointments,
            "p7": arg.notes,
            "p8": arg.status,
            "p9": arg.user_managed_schedule,
        }).first()
        if row is None:
            return None
        return models.UserScreening(
            screening_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            last_date=row[5],
            attended_date=row[6],
            months_between_appointments=row[7],
            notes=row[8],
            status=row[9],
            user_managed_schedule=row[10],
            alert_id=row[11],
            created_date=row[12],
            is_deleted=row[13],
            deleted_date=row[14],
        )

    def update_user_screening_alert_status(self, arg: UpdateUserScreeningAlertStatusParams) -> Optional[models.UserScreeningAlert]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SCREENING_ALERT_STATUS), {"p1": arg.sub, "p2": arg.alert_id, "p3": arg.status}).first()
        if row is None:
            return None
        return models.UserScreeningAlert(
            alert_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            suggested_months_between_appointments=row[5],
            notes=row[6],
            status=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def update_user_screening_attended(self, arg: UpdateUserScreeningAttendedParams) -> Optional[models.UserScreening]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SCREENING_ATTENDED), {
            "p1": arg.sub,
            "p2": arg.screening_id,
            "p3": arg.attended_date,
            "p4": arg.notes,
            "p5": arg.status,
        }).first()
        if row is None:
            return None
        return models.UserScreening(
            screening_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            last_date=row[5],
            attended_date=row[6],
            months_between_appointments=row[7],
            notes=row[8],
            status=row[9],
            user_managed_schedule=row[10],
            alert_id=row[11],
            created_date=row[12],
            is_deleted=row[13],
            deleted_date=row[14],
        )

    def update_user_share_sharee_label(self, arg: UpdateUserShareShareeLabelParams) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SHARE_SHAREE_LABEL), {"p1": arg.sub, "p2": arg.share_id, "p3": arg.label_for_sharee}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def update_user_share_sharer_label(self, arg: UpdateUserShareSharerLabelParams) -> Optional[models.UserShare]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SHARE_SHARER_LABEL), {"p1": arg.sub, "p2": arg.share_id, "p3": arg.label_for_sharer}).first()
        if row is None:
            return None
        return models.UserShare(
            share_id=row[0],
            sharer_id=row[1],
            sharee_id=row[2],
            sharee_relationship_to_sharer=row[3],
            label_for_sharer=row[4],
            label_for_sharee=row[5],
            approved=row[6],
            approved_date=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def update_user_survey_lifestyle_four(self, arg: UpdateUserSurveyLifestyleFourParams) -> Optional[models.UserSurveyResponseLifestyle]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_LIFESTYLE_FOUR), {
            "p1": arg.sub,
            "p2": arg.sleep_hours,
            "p3": arg.stress_level,
            "p4": arg.alcohol_frequency,
            "p5": arg.nicotine_use,
            "p6": arg.nicotine_details,
            "p7": arg.mindfulness_practice,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseLifestyle(
            response_id=row[0],
            user_id=row[1],
            general_health=row[2],
            health_vs_last_year=row[3],
            height=row[4],
            weight=row[5],
            height_weight_unit_type=row[6],
            daily_routine_activity=row[7],
            strength_training=row[8],
            cardio_exercise=row[9],
            brisk_walking=row[10],
            hours_sitting_per_day=row[11],
            special_diet=row[12],
            special_diet_other=row[13],
            regular_diet_quality=row[14],
            supplements=row[15],
            supplements_details=row[16],
            sleep_hours=row[17],
            stress_level=row[18],
            alcohol_frequency=row[19],
            nicotine_use=row[20],
            nicotine_details=row[21],
            mindfulness_practice=row[22],
            created_date=row[23],
        )

    def update_user_survey_lifestyle_one(self, arg: UpdateUserSurveyLifestyleOneParams) -> Optional[models.UserSurveyResponseLifestyle]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_LIFESTYLE_ONE), {
            "p1": arg.sub,
            "p2": arg.general_health,
            "p3": arg.health_vs_last_year,
            "p4": arg.height,
            "p5": arg.weight,
            "p6": arg.height_weight_unit_type,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseLifestyle(
            response_id=row[0],
            user_id=row[1],
            general_health=row[2],
            health_vs_last_year=row[3],
            height=row[4],
            weight=row[5],
            height_weight_unit_type=row[6],
            daily_routine_activity=row[7],
            strength_training=row[8],
            cardio_exercise=row[9],
            brisk_walking=row[10],
            hours_sitting_per_day=row[11],
            special_diet=row[12],
            special_diet_other=row[13],
            regular_diet_quality=row[14],
            supplements=row[15],
            supplements_details=row[16],
            sleep_hours=row[17],
            stress_level=row[18],
            alcohol_frequency=row[19],
            nicotine_use=row[20],
            nicotine_details=row[21],
            mindfulness_practice=row[22],
            created_date=row[23],
        )

    def update_user_survey_lifestyle_three(self, arg: UpdateUserSurveyLifestyleThreeParams) -> Optional[models.UserSurveyResponseLifestyle]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_LIFESTYLE_THREE), {
            "p1": arg.sub,
            "p2": arg.special_diet,
            "p3": arg.special_diet_other,
            "p4": arg.regular_diet_quality,
            "p5": arg.supplements,
            "p6": arg.supplements_details,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseLifestyle(
            response_id=row[0],
            user_id=row[1],
            general_health=row[2],
            health_vs_last_year=row[3],
            height=row[4],
            weight=row[5],
            height_weight_unit_type=row[6],
            daily_routine_activity=row[7],
            strength_training=row[8],
            cardio_exercise=row[9],
            brisk_walking=row[10],
            hours_sitting_per_day=row[11],
            special_diet=row[12],
            special_diet_other=row[13],
            regular_diet_quality=row[14],
            supplements=row[15],
            supplements_details=row[16],
            sleep_hours=row[17],
            stress_level=row[18],
            alcohol_frequency=row[19],
            nicotine_use=row[20],
            nicotine_details=row[21],
            mindfulness_practice=row[22],
            created_date=row[23],
        )

    def update_user_survey_lifestyle_two(self, arg: UpdateUserSurveyLifestyleTwoParams) -> Optional[models.UserSurveyResponseLifestyle]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_LIFESTYLE_TWO), {
            "p1": arg.sub,
            "p2": arg.daily_routine_activity,
            "p3": arg.strength_training,
            "p4": arg.cardio_exercise,
            "p5": arg.brisk_walking,
            "p6": arg.hours_sitting_per_day,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseLifestyle(
            response_id=row[0],
            user_id=row[1],
            general_health=row[2],
            health_vs_last_year=row[3],
            height=row[4],
            weight=row[5],
            height_weight_unit_type=row[6],
            daily_routine_activity=row[7],
            strength_training=row[8],
            cardio_exercise=row[9],
            brisk_walking=row[10],
            hours_sitting_per_day=row[11],
            special_diet=row[12],
            special_diet_other=row[13],
            regular_diet_quality=row[14],
            supplements=row[15],
            supplements_details=row[16],
            sleep_hours=row[17],
            stress_level=row[18],
            alcohol_frequency=row[19],
            nicotine_use=row[20],
            nicotine_details=row[21],
            mindfulness_practice=row[22],
            created_date=row[23],
        )

    def update_user_survey_personal_health_five(self, arg: UpdateUserSurveyPersonalHealthFiveParams) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_PERSONAL_HEALTH_FIVE), {"p1": arg.sub, "p2": arg.disability, "p3": arg.disability_needs_details}).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def update_user_survey_personal_health_four(self, *, sub: str, family_health_notes: Optional[str]) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_PERSONAL_HEALTH_FOUR), {"p1": sub, "p2": family_health_notes}).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def update_user_survey_personal_health_one(self, arg: UpdateUserSurveyPersonalHealthOneParams) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_PERSONAL_HEALTH_ONE), {
            "p1": arg.sub,
            "p2": arg.gp_postcode,
            "p3": arg.ethnicity,
            "p4": arg.gender,
            "p5": arg.country,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def update_user_survey_personal_health_three(self, arg: UpdateUserSurveyPersonalHealthThreeParams) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_PERSONAL_HEALTH_THREE), {
            "p1": arg.sub,
            "p2": arg.routine_vaccines,
            "p3": arg.vaccines,
            "p4": arg.childhood_vaccinations_status,
            "p5": arg.additional_vaccine_notes,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def update_user_survey_personal_health_two(self, arg: UpdateUserSurveyPersonalHealthTwoParams) -> Optional[models.UserSurveyResponsePersonalHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_PERSONAL_HEALTH_TWO), {
            "p1": arg.sub,
            "p2": arg.current_health_condition,
            "p3": arg.historic_health_condition,
            "p4": arg.injuries,
            "p5": arg.allergies,
            "p6": arg.medications,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponsePersonalHealth(
            response_id=row[0],
            user_id=row[1],
            gp_postcode=row[2],
            ethnicity=row[3],
            gender=row[4],
            country=row[5],
            current_health_condition=row[6],
            historic_health_condition=row[7],
            injuries=row[8],
            allergies=row[9],
            medications=row[10],
            routine_vaccines=row[11],
            vaccines=row[12],
            childhood_vaccinations_status=row[13],
            additional_vaccine_notes=row[14],
            family_health_notes=row[15],
            disability=row[16],
            disability_needs_details=row[17],
            created_date=row[18],
        )

    def update_user_survey_reproductive_health_five(self, arg: UpdateUserSurveyReproductiveHealthFiveParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_FIVE), {
            "p1": arg.sub,
            "p2": arg.diagnosed_conditions,
            "p3": arg.other_conditions_details,
            "p4": arg.pcos_screener_irregular_periods,
            "p5": arg.pcos_screener_excessive_hair_growth,
            "p6": arg.pcos_screener_overweight_16_40,
            "p7": arg.pcos_screener_nipple_discharge,
            "p8": arg.endopain_period_pain,
            "p9": arg.endopain_pain_between_periods,
            "p10": arg.endopain_worsening_pain,
            "p11": arg.endopain_prolonged_period_pain,
            "p12": arg.endopain_stabbing_pain,
            "p13": arg.endopain_radiating_back_pain,
            "p14": arg.endopain_hip_or_leg_pain,
            "p15": arg.endopain_limits_daily_activities,
            "p16": arg.endopain_disabling_pain,
            "p17": arg.endopain_sexual_severe_pain,
            "p18": arg.endopain_sexual_position_specific_pain,
            "p19": arg.endopain_sexual_interrupts_sex,
            "p20": arg.endopain_bowel_and_bladder_pain_bowel_movements,
            "p21": arg.endopain_bowel_and_bladder_diarrhoea_constipation,
            "p22": arg.endopain_bowel_and_bladder_bowel_cramps,
            "p23": arg.endopain_bowel_and_bladder_urination_pain,
            "p24": arg.endopain_bowel_and_bladder_bladder_discomfort,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def update_user_survey_reproductive_health_four(self, arg: UpdateUserSurveyReproductiveHealthFourParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_FOUR), {
            "p1": arg.sub,
            "p2": arg.currently_pregnant,
            "p3": arg.pregnancies_total,
            "p4": arg.pregnancies_live_births,
            "p5": arg.pregnancies_stillbirths,
            "p6": arg.pregnancies_ectopics,
            "p7": arg.pregnancies_miscarriages,
            "p8": arg.pregnancies_terminations,
            "p9": arg.currently_breastfeeding,
            "p10": arg.tried_to_conceive_12_months,
            "p11": arg.fertility_testing,
            "p12": arg.fertility_testing_details,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def update_user_survey_reproductive_health_one(self, arg: UpdateUserSurveyReproductiveHealthOneParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_ONE), {
            "p1": arg.sub,
            "p2": arg.reproductive_organs,
            "p3": arg.reproductive_organ_details,
            "p4": arg.reproductive_surgeries,
            "p5": arg.surgery_other_details,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def update_user_survey_reproductive_health_six(self, arg: UpdateUserSurveyReproductiveHealthSixParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_SIX), {
            "p1": arg.sub,
            "p2": arg.cycles_irregular_past_12_months,
            "p3": arg.symptoms,
            "p4": arg.menopause_status,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def update_user_survey_reproductive_health_three(self, arg: UpdateUserSurveyReproductiveHealthThreeParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_THREE), {
            "p1": arg.sub,
            "p2": arg.currently_using_contraception,
            "p3": arg.current_contraception_details,
            "p4": arg.ever_used_contraception,
            "p5": arg.past_contraception_details,
            "p6": arg.currently_using_hrt,
            "p7": arg.current_hrt_details,
            "p8": arg.menstrual_status_at_hrt_start,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def update_user_survey_reproductive_health_two(self, arg: UpdateUserSurveyReproductiveHealthTwoParams) -> Optional[models.UserSurveyResponseReproductiveHealth]:
        row = self._conn.execute(sqlalchemy.text(UPDATE_USER_SURVEY_REPRODUCTIVE_HEALTH_TWO), {
            "p1": arg.sub,
            "p2": arg.ever_menstruated,
            "p3": arg.menarche_age,
            "p4": arg.menstruated_last_12_months,
            "p5": arg.last_period_date,
            "p6": arg.cycle_length,
            "p7": arg.menstrual_symptoms,
            "p8": arg.menstrual_symptoms_other,
        }).first()
        if row is None:
            return None
        return models.UserSurveyResponseReproductiveHealth(
            response_id=row[0],
            user_id=row[1],
            reproductive_organs=row[2],
            reproductive_organ_details=row[3],
            reproductive_surgeries=row[4],
            surgery_other_details=row[5],
            ever_menstruated=row[6],
            menarche_age=row[7],
            menstruated_last_12_months=row[8],
            last_period_date=row[9],
            cycle_length=row[10],
            menstrual_symptoms=row[11],
            menstrual_symptoms_other=row[12],
            currently_using_contraception=row[13],
            current_contraception_details=row[14],
            ever_used_contraception=row[15],
            past_contraception_details=row[16],
            currently_using_hrt=row[17],
            current_hrt_details=row[18],
            menstrual_status_at_hrt_start=row[19],
            currently_pregnant=row[20],
            pregnancies_total=row[21],
            pregnancies_live_births=row[22],
            pregnancies_stillbirths=row[23],
            pregnancies_ectopics=row[24],
            pregnancies_miscarriages=row[25],
            pregnancies_terminations=row[26],
            currently_breastfeeding=row[27],
            tried_to_conceive_12_months=row[28],
            fertility_testing=row[29],
            fertility_testing_details=row[30],
            diagnosed_conditions=row[31],
            other_conditions_details=row[32],
            pcos_screener_irregular_periods=row[33],
            pcos_screener_excessive_hair_growth=row[34],
            pcos_screener_overweight_16_40=row[35],
            pcos_screener_nipple_discharge=row[36],
            endopain_period_pain=row[37],
            endopain_pain_between_periods=row[38],
            endopain_worsening_pain=row[39],
            endopain_prolonged_period_pain=row[40],
            endopain_stabbing_pain=row[41],
            endopain_radiating_back_pain=row[42],
            endopain_hip_or_leg_pain=row[43],
            endopain_limits_daily_activities=row[44],
            endopain_disabling_pain=row[45],
            endopain_sexual_severe_pain=row[46],
            endopain_sexual_position_specific_pain=row[47],
            endopain_sexual_interrupts_sex=row[48],
            endopain_bowel_and_bladder_pain_bowel_movements=row[49],
            endopain_bowel_and_bladder_diarrhoea_constipation=row[50],
            endopain_bowel_and_bladder_bowel_cramps=row[51],
            endopain_bowel_and_bladder_urination_pain=row[52],
            endopain_bowel_and_bladder_bladder_discomfort=row[53],
            cycles_irregular_past_12_months=row[54],
            symptoms=row[55],
            menopause_status=row[56],
            created_date=row[57],
        )

    def valid_survey_question(self, *, survey_id: str, question_id: str) -> Optional[models.Question]:
        row = self._conn.execute(sqlalchemy.text(VALID_SURVEY_QUESTION), {"p1": survey_id, "p2": question_id}).first()
        if row is None:
            return None
        return models.Question(
            question_id=row[0],
            key=row[1],
            question=row[2],
            type=row[3],
            options=row[4],
            created_date=row[5],
            is_deleted=row[6],
            deleted_date=row[7],
        )

    def valid_user(self, *, sub: str) -> Optional[str]:
        row = self._conn.execute(sqlalchemy.text(VALID_USER), {"p1": sub}).first()
        if row is None:
            return None
        return row[0]

    def worker_create_user_screening_alert(self, arg: WorkerCreateUserScreeningAlertParams) -> Optional[models.UserScreeningAlert]:
        row = self._conn.execute(sqlalchemy.text(WORKER_CREATE_USER_SCREENING_ALERT), {
            "p1": arg.user_id,
            "p2": arg.type,
            "p3": arg.subtype,
            "p4": arg.next_date,
            "p5": arg.suggested_months_between_appointments,
            "p6": arg.notes,
        }).first()
        if row is None:
            return None
        return models.UserScreeningAlert(
            alert_id=row[0],
            user_id=row[1],
            type=row[2],
            subtype=row[3],
            next_date=row[4],
            suggested_months_between_appointments=row[5],
            notes=row[6],
            status=row[7],
            created_date=row[8],
            is_deleted=row[9],
            deleted_date=row[10],
        )

    def worker_get_user_document_by_id(self, *, document_id: str) -> Optional[models.UserDocument]:
        row = self._conn.execute(sqlalchemy.text(WORKER_GET_USER_DOCUMENT_BY_ID), {"p1": document_id}).first()
        if row is None:
            return None
        return models.UserDocument(
            document_id=row[0],
            user_id=row[1],
            label=row[2],
            type=row[3],
            mime_type=row[4],
            json_output=row[5],
            created_date=row[6],
            is_deleted=row[7],
            deleted_date=row[8],
        )

    def worker_list_shared_health_events(self, *, sharee_id: str) -> Iterator[WorkerListSharedHealthEventsRow]:
        result = self._conn.execute(sqlalchemy.text(WORKER_LIST_SHARED_HEALTH_EVENTS), {"p1": sharee_id})
        for row in result:
            yield WorkerListSharedHealthEventsRow(
                event_id=row[0],
                user_id=row[1],
                type=row[2],
                details=row[3],
                notes=row[4],
                start_date=row[5],
                end_date=row[6],
                ongoing=row[7],
                genetic=row[8],
                is_reviewed=row[9],
                created_date=row[10],
                is_deleted=row[11],
                deleted_date=row[12],
                share_id=row[13],
                sharer_id=row[14],
                sharee_id=row[15],
                sharee_relationship_to_sharer=row[16],
                label_for_sharer=row[17],
                label_for_sharee=row[18],
                approved=row[19],
                approved_date=row[20],
                created_date_2=row[21],
                is_deleted_2=row[22],
                deleted_date_2=row[23],
            )

    def worker_list_shares_by_sharer_id(self, *, sharer_id: str) -> Iterator[models.UserShare]:
        result = self._conn.execute(sqlalchemy.text(WORKER_LIST_SHARES_BY_SHARER_ID), {"p1": sharer_id})
        for row in result:
            yield models.UserShare(
                share_id=row[0],
                sharer_id=row[1],
                sharee_id=row[2],
                sharee_relationship_to_sharer=row[3],
                label_for_sharer=row[4],
                label_for_sharee=row[5],
                approved=row[6],
                approved_date=row[7],
                created_date=row[8],
                is_deleted=row[9],
                deleted_date=row[10],
            )

    def worker_list_user_health_events(self, *, user_id: str) -> Iterator[models.UserHealthEvent]:
        result = self._conn.execute(sqlalchemy.text(WORKER_LIST_USER_HEALTH_EVENTS), {"p1": user_id})
        for row in result:
            yield models.UserHealthEvent(
                event_id=row[0],
                user_id=row[1],
                type=row[2],
                details=row[3],
                notes=row[4],
                start_date=row[5],
                end_date=row[6],
                ongoing=row[7],
                genetic=row[8],
                is_reviewed=row[9],
                created_date=row[10],
                is_deleted=row[11],
                deleted_date=row[12],
            )

    def worker_update_user_document_by_id(self, *, document_id: str, json_output: Optional[Any]) -> Optional[str]:
        row = self._conn.execute(sqlalchemy.text(WORKER_UPDATE_USER_DOCUMENT_BY_ID), {"p1": document_id, "p2": json_output}).first()
        if row is None:
            return None
        return row[0]
