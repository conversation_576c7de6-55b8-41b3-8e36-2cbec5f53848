
from manual.instagramcheck.azure_storage import queue_client, parse_message
from manual.instagramcheck.browser import InstagramBrowser
from manual.instagramcheck.db import award_reward_point, health_check
import logging
import time

logging.basicConfig(level=logging.INFO)
logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)


def main() -> None:
  health_check()
  logging.info("database connection looks good")
  instagram_browser = InstagramBrowser.init()  # type: ignore[no-untyped-call]
  while True:
    message = queue_client.receive_message()
    if message is None:
      logging.info("no messages, sleeping")
      time.sleep(300)
      continue
    assert isinstance(message.content, str)
    contents = parse_message(message=message.content)
    if instagram_browser.check_instagram_follower(instagram_handle=contents.instagram_handle):
      logging.info(f"{contents.user_id} {contents.instagram_handle} is a follower")
      award_reward_point(user_id=contents.user_id)
    else:
      logging.info(f"{contents.user_id} {contents.instagram_handle} is not a follower")
    queue_client.delete_message(message=message)


if __name__ == "__main__":
  main()
