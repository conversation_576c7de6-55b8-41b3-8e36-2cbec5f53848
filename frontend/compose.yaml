services:
  frontend:
    build: frontend
    pull_policy: build
    ports:
    - '80:80'
    environment:
      POSTGRES_HOST: frontend-db
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DATABASE: postgres
      POSTGRES_SSLMODE: disable
      <PERSON>CKEND_HOST: backend-db
      <PERSON>CKEND_USERNAME: postgres
      BACKEND_PASSWORD: password
      BACKEND_DATABASE: postgres
      BACKEND_SSLMODE: disable
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-318012792699-l64l6qaobsuf77ngvlod65h5n67nu5si.apps.googleusercontent.com}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      SECRET_KEY: foo
      INHERIT_SUPERUSER_PASSWORD: admin
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT:-https://inherit-nonproduction-openai.openai.azure.com/}
      AZURE_OPENAI_MODEL: ${AZURE_OPENAI_MODEL:-nonproduction-openai-gpt-4o-mini}
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY}
      REDIS_HOST: redis
      REDIS_PORT: 6379
    
    depends_on:
      - redis

  redis:
    image: redis:8.0.0
    ports:
      - "6379:6379"
    restart: always
