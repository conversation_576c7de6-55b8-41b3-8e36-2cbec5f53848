from django.urls import path
from . import views


urlpatterns = [
    path('', views.staff_home, name='staff-dashboard'),
    path('health-events/', views.HealthEventsView.as_view(), name='health_events'),
    path('health-events/<str:event_id>/', views.HealthEventDetailView.as_view(), name='health_event_detail'),
    path('health-events/<str:event_id>/update/', views.HealthEventUpdateView.as_view(), name='health_event_update'),
    path('data', views.user_data, name='data'),
    # path('data_pull', views.pull_user_data, name='data_pull'),
    # path('delete_counts', views.delete_daily_counts, name='delete_counts'),
]
