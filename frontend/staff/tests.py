from django.test import TestCase, Client
from django.urls import reverse
from core.psql_models import User, UserHealthEvent, DailyUserCount
from surveys.models import Feedback
from staff.forms import UserHealthEventForm
from users.models import CustomUser


class StaffViewsTests(TestCase):
    databases = '__all__'

    def setUp(self):
        self.client = Client()
        self.staff_user = CustomUser.objects.create_user(email='<EMAIL>', password='password', role='STF')
        self.mob_test_user = User.objects.create(
            user_id="mobileTestUser",
            sub="mobile_test_user",
            username="Mobile Test User",
            email="<EMAIL>",
            birth_month=5,
            birth_year=1990,
            sex_assigned_at_birth='Male',
            reward_points=0,
            reward_point_awarded_for_instagram=False,
            reward_point_awarded_for_personal_health_survey=False,
            reward_point_awarded_for_reproductive_health_survey=False,
            reward_point_awarded_for_lifestyle_survey=False,
            reward_point_awarded_for_sharing=False,
            reward_point_awarded_to_referrer=False,
            referral_code="REF123",
            impact_points=0,
            is_deleted=False,
            created_date="2023-05-29T12:00:00Z",
        )
        self.health_event = UserHealthEvent.objects.create(
            event_id="event123",
            user_id=self.mob_test_user.user_id,
            type='Diagnosis',
            details="This is a test health event.",
            start_date="2023-04-29",
            ongoing=False,
            genetic=False,
            is_reviewed=False,
            created_date="2023-05-29T12:00:00Z",
            is_deleted=False,
        )
        self.daily_user_count = DailyUserCount.objects.create(
            date_id="testcount_123",
            date="2025-05-29",
            d_new_user_count=5,
            d_total_user_count=100,
            d_growth_rate=0.05,
            wk_new_user_count=10,
            wk_total_user_count=50,
            wk_growth_rate=0.1,
            mth_new_user_count=20,
            mth_total_user_count=200,
            mth_growth_rate=0.1,
            created_date="2025-05-29T12:00:00Z"
        )
        self.feedback = Feedback.objects.create(
            sub="test_user",
            category="1",
            details="This is a test feedback."
        )

    def test_user_data_view(self):
        """Test user_data view"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('data'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff/user_count.html')
        self.assertIn(self.daily_user_count, response.context['user_count'])

    def test_staff_home_view(self):
        """Test staff_home view"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('staff_home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff/dashboard.html')
        self.assertEqual(response.context['user_health_events_count'], 1)
        self.assertEqual(response.context['user_questions_count'], 0)
        self.assertEqual(response.context['user_feedback_count'], 1)

    def test_health_events_view(self):
        """Test HealthEventsView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('health_events'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff/user_health_events.html')
        self.assertIn(self.health_event, response.context['user_health_events'])

    def test_health_event_detail_view(self):
        """Test HealthEventDetailView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('health_event_detail', kwargs={'event_id': self.health_event.event_id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff/health_event_detail.html')
        self.assertEqual(response.context['event'], self.health_event)

    def test_health_event_update_view_get(self):
        """Test GET request to HealthEventUpdateView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('health_event_update', kwargs={'event_id': self.health_event.event_id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'staff/health_event_update.html')
        self.assertIsInstance(response.context['form'], UserHealthEventForm)

    def test_health_event_update_view_post(self):
        """Test POST request to HealthEventUpdateView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.post(reverse('health_event_update', kwargs={'event_id': self.health_event.event_id}))
        self.assertEqual(response.status_code, 302)  # Redirect after successful update
        self.assertRedirects(response, reverse('health_events'))
        self.health_event.refresh_from_db()
        self.assertEqual(self.health_event.details, "This is a test health event.")
        self.assertTrue(self.health_event.is_reviewed)
