{% extends 'core/base.html' %}
{% load widget_tweaks %}
{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="flex">
  <!-- Main Dash-->
  <div class="flex-1 p-7">
    <div class="flex flex-col-reverse md:grid md:grid-cols-6 gap-4">
      {% include 'staff/partials/health_events_container.html' %}
      <div id="filters" class="col-span-1 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Filters</h3>
        <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
          <div class="w-full">

             <form hx-get="{% url 'health_events' %}" hx-target="#health-events-container" hx-swap="outerHTML">
                <div class="flex flex-col mb-2 form-control">
                  {{ filter.form.type | add_label_class:"label text-gray-500 dark:text-white"}}
                  {% render_field filter.form.type class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mb-4 form-control">
                  {{ filter.form.order_by_date | add_label_class:"label text-gray-500 dark:text-white" }}
                  {% render_field filter.form.order_by_date class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mb-2 form-control">
                  {{ filter.form.reviewed | add_label_class:"label text-gray-500 dark:text-white"}}
                  {% render_field filter.form.reviewed class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mb-2 form-control">
                  {{ filter.form.genetic | add_label_class:"label text-gray-500 dark:text-white"}}
                  {% render_field filter.form.genetic class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mt-2 mb-2 form-control">
                  <button type="submit" class="btn bg-inherit-blue-dark">Search</button>
                </div>
                <!--
                <div class="flex flex-col mt-2 mb-2 form-control">
                  <button type="button"
                    class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark hover:border-inherit-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-inherit-dark focus:ring-inherit-dark hover:ring hover:ring-inherit-dark"
                    onclick="clearFilters()">
                    Clear
                  </button>
              </div>
              -->
            </form>

          </div>
      </div>
    </div>
  </div>
</div>
<script>
  function searchHealthEvents() {
      var input, filter, table, tr, td, i, j, txtValue;
      input = document.getElementById("searchBox");
      filter = input.value.toUpperCase();
      table = document.getElementById("healthEventsTable");
      tr = table.getElementsByTagName("tr");

      for (i = 1; i < tr.length; i++) { // Start from 1 to skip the header row
          tr[i].style.display = "none"; // Hide the row by default
          td = tr[i].getElementsByTagName("td");
          for (j = 0; j < td.length; j++) { // Loop through all columns in the row
              if (td[j]) {
                  txtValue = td[j].textContent || td[j].innerText;
                  if (txtValue.toUpperCase().indexOf(filter) > -1) {
                      tr[i].style.display = ""; // Show the row if a match is found
                      break; // Stop checking other columns for this row
                  }
              }
          }
      }
  }
  function clearFilters() {
    // Reload the page without query parameters to reset filters
    window.location.href = "{% url 'health_events' %}";
  }
</script>
{% endblock %}