{% extends 'core/base.html' %}

{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}

<!-- Main Dash-->
<div class="flex-1 p-7">
  <!-- First Row-->
  <div class="grid w-full grid-cols-1 mt-2 gap-4 xl:grid-cols-2 2xl:grid-cols-3">
    <!-- Col 1 -->
    <div id="health-events" class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <a href="{% url 'health_events' %}">
        <div class="w-full">
          <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Health Events</h3>
          <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ user_health_events_count }}</span>
          <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
            {% if user_health_events_count > 0 %}
            <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
              <svg class="w-4 h-4 flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
              </svg>
              # Health Events
            </span>
            {% else %}
            <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
              <svg class="w-4 h-4 flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400" fill="currentColor" height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
                  viewBox="0 0 20 20" xml:space="preserve">
                <path id="XMLID_30_" d="M154.394,325.606C157.322,328.535,161.161,330,165,330s7.678-1.465,10.607-4.394l75-75
                  c5.858-5.857,5.858-15.355,0-21.213c-5.858-5.857-15.356-5.857-21.213,0L180,278.787V15c0-8.284-6.716-15-15-15
                  c-8.284,0-15,6.716-15,15v263.787l-49.394-49.394c-5.858-5.857-15.355-5.857-21.213,0c-5.858,5.857-5.858,15.355,0,21.213
                  L154.394,325.606z"/>
              </svg>
              # Health Events
              {% endif %}
              
            </span>
          </p>
        </div>
      </a>
    </div>
    <!-- Col 2 -->
    <div id="user-questions" class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <a href="{% url 'expert_home' %}">
        <div class="w-full">
          <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Questions </h3>
          <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ user_questions_count }}</span>
          <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
            <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
              <!--
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
              </svg>
              -->
            </span>
            Ready for Review
          </p>
        </div>
      </a>
    </div>
    <!-- Col 3 -->
    <div id="user-feedback" class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <a href="{% url 'feedback' %}">
        <div class="w-full">
          <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Feedback </h3>
          <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ user_feedback_count }}</span>
          <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
            <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
              <!--
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
              </svg>
              -->
            </span>
            {{ user_feedback_count_week }} Received in the last week
          </p>
        </div>
      </a>
    </div>
  </div>
  
  <!-- Second Row -->
  <div class="grid w-full grid-cols-1 my-4 gap-4 xl:grid-cols-2 2xl:grid-cols-3">
    <div id="weekly-user-change" class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User weekly Change</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ signups_last_7_days }}</span>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          {% if wk_growth > 0 %}
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
            </svg>
            {{ wk_growth }}%
          </span>
          Last 7 days

          {% else %}
          <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 330 330" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path id="XMLID_30_" d="M154.394,325.606C157.322,328.535,161.161,330,165,330s7.678-1.465,10.607-4.394l75-75
                c5.858-5.857,5.858-15.355,0-21.213c-5.858-5.857-15.356-5.857-21.213,0L180,278.787V15c0-8.284-6.716-15-15-15
                c-8.284,0-15,6.716-15,15v263.787l-49.394-49.394c-5.858-5.857-15.355-5.857-21.213,0c-5.858,5.857-5.858,15.355,0,21.213
                L154.394,325.606z">
            </svg>
            {{ wk_growth }}%
          </span>
          last 7 days

          {% endif %}
        </p>
      </div>
      <div></div>
    </div>
    <div id="user-count"  class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Count</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ live_count }}</span>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          {% if mth_new > 0 %}
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
            </svg>
            {{ mth_new }}
          </span>
          Last Month

          {% else %}
          <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 330 330" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path id="XMLID_30_" d="M154.394,325.606C157.322,328.535,161.161,330,165,330s7.678-1.465,10.607-4.394l75-75
                c5.858-5.857,5.858-15.355,0-21.213c-5.858-5.857-15.356-5.857-21.213,0L180,278.787V15c0-8.284-6.716-15-15-15
                c-8.284,0-15,6.716-15,15v263.787l-49.394-49.394c-5.858-5.857-15.355-5.857-21.213,0c-5.858,5.857-5.858,15.355,0,21.213
                L154.394,325.606z">
            </svg>
            {{ mth_new }}
          </span>
          Last Month
          {% endif %}
        </p>
      </div>
      <div class="w-full" id="week-signups-chart"></div>
    </div>
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <div class="w-full">
        <h3 class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">Signups Yesterday</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ d_new }}</span>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          {% if d_growth > 0 %}
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z"></path>
            </svg>
            {{ d_growth }} 
          </span>
          % change on the day

          {% else %}
          <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 330 330" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path id="XMLID_30_" d="M154.394,325.606C157.322,328.535,161.161,330,165,330s7.678-1.465,10.607-4.394l75-75
                c5.858-5.857,5.858-15.355,0-21.213c-5.858-5.857-15.356-5.857-21.213,0L180,278.787V15c0-8.284-6.716-15-15-15
                c-8.284,0-15,6.716-15,15v263.787l-49.394-49.394c-5.858-5.857-15.355-5.857-21.213,0c-5.858,5.857-5.858,15.355,0,21.213
                L154.394,325.606z">
            </svg>
            {{ d_growth }} 
          </span>
          % change on the day
          {% endif %}
        </p>
      </div>
      <div></div>
    </div>
  </div>
  <!-- Third Row -->
  <div class="grid w-full grid-cols-1 my-4  gap-4 xl:grid-cols-2 2xl:grid-cols-3">
    <div id="weekly-user-change" class="items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="w-full">
          <h3 class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">Age Distribution</h3>
          <!-- plotly Table -->
          <div class="w-full">
              {{ age_category_graph|safe }}
           </div>
        </div>
    </div>

    <div id="cumulative-users" class="items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <div class="w-full">
        <h3 class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">User Growth</h3>
        <!-- plotly Table -->
        <div class="w-full">
            {{ cumulative_users_chart|safe }}
         </div>
      </div>
    </div>

    <div id="reward-points" class="items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
      <div class="w-full">
        <h3 class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">Reward Points Leaderboard</h3>
        <!-- plotly Table -->
        <div class="w-full">
            {{ reward_chart|safe }}
         </div>
      </div>
    </div>
  </div>
{% endblock %}