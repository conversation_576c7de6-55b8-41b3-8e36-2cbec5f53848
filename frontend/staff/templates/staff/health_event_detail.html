{% extends 'core/base.html' %}

{% load static %}
{% block title %}Question{% endblock %}
{% block content %}
<div class="flex-1 p-7">
    <div class="">
        <div id="user-details" class="w-1/2 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-300">Health Event Details: {{ event.user.email }}</h3>
            <div class="p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                <div class="flex flex-col">
                    <div class="grid grid-cols-2 gap-1">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Username:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ event.user.username}}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Details:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ event.details}}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Genetic:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ event.genetic}}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Ongoing:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ event.ongoing}}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Created Date:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ event.created_date}}</p>
                        </div>
                        
                    </div>
                    <div class="flex mt-4 align-center justify-center space-x-2">
                        <a href="{% url 'health_event_update' event.pk %}"
                            class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark"
                            >Edit
                        </a>
                        <a href="{% url 'health_events' %}"
                            class="inline-block px-4 py-2 text-sm font-medium text-inherit-dark hover:text-button-light bg-inherit-yellow hover:bg-button-dark focus:ring-4 focus:outline-none focus:ring-inherit-dark"
                            >
                            Back To List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}