<div>
    <form method="post" action="{% url 'data' %}" class="form-inline">
        {% csrf_token %}
        <button type="submit" class="btn btn-primary">
            <i class="fa fa-refresh"></i> Refresh
        </button>
    </form>
    <table>
    <thead>
        <tr>
            <th>date</th>
            <th>d-count</th>
            <th>d-total</th>
            <th>d-growth</th>
            <th>wk-new-count</th>
            <th>wk-total</th>
            <th>wk-growth</th>
            <th>mth-new-count</th>
            <th>mth-total</th>
            <th>mth-growth</th>
        </tr>
    </thead>
    <tbody>
        {% for item in user_count %}
        <tr>
            <td>{{ item.date }}</td>
            <td>{{ item.d_new_user_count }}</td>
            <td>{{ item.d_total_user_count }}</td>
            <td>{{ item.d_growth_rate }}</td>
            <td>{{ item.wk_new_user_count }}</td>
            <td>{{ item.wk_total_user_count }}</td>
            <td>{{ item.wk_growth_rate }}</td>
            <td>{{ item.mth_new_user_count }}</td>
            <td>{{ item.mth_total_user_count }}</td>
            <td>{{ item.mth_growth_rate }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
