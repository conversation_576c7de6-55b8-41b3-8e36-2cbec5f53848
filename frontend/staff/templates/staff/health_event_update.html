{% extends 'core/base.html' %}

{% load static %}
{% block title %}Answer{% endblock %}
{% block content %}
<div class="flex-1 p-7">
    <div class="grid w-full grid-cols-1 gap-4">
        <div id="response-container" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">{{ user.email }}</h3>
            <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                <div class="w-full overflow-x-auto">
                    <h2 class="text-2xl font-med text-gray-900 dark:text-gray-200">You are editing details for:</h2>
                        <p>A {{ event.type }} of {{event.details}} </p>
                    <form method="post">
                        {% csrf_token %}
                        {% if form.non_field_errors %}
                        <div class="mt-4">
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <strong class="font-bold">Error!</strong>
                                <span class="block sm:inline">{{ form.non_field_errors }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% for field in form %}
                        <div class="mt-4">
                            <button type="button" id="toggle-genetic" class="toggle-btn inherit-red text-white px-4 py-2 rounded" data-state="false">
                                Not Genetic
                            </button>
                            {{ form.genetic.as_widget }}
                            {% if form.genetic.errors %}
                                <small class="error text-red-600">{{ form.genetic.errors|striptags }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                        <div class="flex mt-4 align-left justify-left space-x-2">
                            <button type="submit" 
                                class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark"
                                >
                                Submit
                            </button>
                            <a href="{% url 'health_event_detail' event.event_id %}" 
                                class="inline-block px-4 py-2 text-sm font-medium text-inherit-dark hover:text-button-light bg-button-light hover:bg-button-dark focus:ring-4 focus:outline-none focus:ring-inherit-dark">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const toggleButton = document.getElementById('toggle-genetic');
        const checkbox = document.getElementById('{{ form.genetic.id_for_label }}');

        // Initialize the button state based on the checkbox value
        if (checkbox.checked) {
            toggleButton.classList.remove('bg-inherit-red');
            toggleButton.classList.add('bg-inherit-green');
            toggleButton.textContent = 'Genetic';
            toggleButton.dataset.state = 'true';
        }

        // Add click event listener to toggle the state
        toggleButton.addEventListener('click', function () {
            if (toggleButton.dataset.state === 'false') {
                toggleButton.classList.remove('bg-inherit-red');
                toggleButton.classList.add('bg-inherit-green');
                toggleButton.textContent = 'Genetic';
                toggleButton.dataset.state = 'true';
                checkbox.checked = true; // Update the checkbox value
            } else {
                toggleButton.classList.remove('bg-inherit-green');
                toggleButton.classList.add('bg-inherit-red');
                toggleButton.textContent = 'Not Genetic';
                toggleButton.dataset.state = 'false';
                checkbox.checked = false; // Update the checkbox value
            }
        });
    });
</script>
{% endblock %}