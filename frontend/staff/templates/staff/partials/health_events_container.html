<div id="health-events-container" class="col-span-5 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Health Events</h3>
    <input type="text" id="searchBox" onkeyup="searchHealthEvents()" placeholder="Search for health event details..." class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">  
    <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
      <div class="w-full overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 rounded" id="healthEventsTable">
            <thead class="bg-gray-50 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                <tr>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Type</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Details</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Start Date</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">End Date</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Ongoing</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Genetic</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Reviewed</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Created Date</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for event in user_health_events %}
                <tr class="bg-white dark:bg-gray-600">
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">{{ event.type }}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">{{ event.details }}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">{{ event.start_date }}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">
                            {% if event.end_date %}
                                {{ event.end_date }}
                            {% else %}
                                --
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">
                            {% if event.ongoing %}
                                Yes
                            {% else %}
                                No
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">{{ event.genetic }}</div>
                    </td>
                    {% if event.is_reviewed %}
                        <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="{% url 'health_event_detail' event.event_id %}" class="bg-inherit-green rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-green-dark hover:bg-inherit-green-dark">Reviewed</a>
                        </td>
                    {% else %}
                        <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="{% url 'health_event_update' event.event_id %}" class="bg-inherit-red rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-red-dark hover:bg-inherit-red-dark">Update</a>
                        </td>
                    {% endif %}
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 dark:text-white">{{ event.created_date|date:"Y-m-d" }}</div>
                    </td>     
                    <td class="px-4 py-4 whitespace-nowrap">
                        <a href="{% url 'health_event_detail' event.event_id %}" class="bg-inherit-green rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-green-dark hover:bg-inherit-green-dark">
                            Details
                        </a>
                    </td>       
                </tr>
                {% endfor %}
            </tbody>
        </table>
      </div>
    </div>
  </div>