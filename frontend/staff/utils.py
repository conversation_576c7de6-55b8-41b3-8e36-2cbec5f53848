import pandas as pd
import plotly.express as px
from core.psql_models import DailyUserCount, User
# from django.core.cache import cache
import logging


def age_fig():
    """ Try to retrive cache first"""
    age_count_key = "age_counts"
    age_counts = None  # cache.get(age_count_key)

    if age_counts:
        # If cache exists, use it
        logging.info(f"Using cached data for {age_count_key}")
        age_category_counts = pd.Series(age_counts)

    else:
        logging.info('no cache found, age_cat_fig, fetching from db')
        bins = range(0, 101, 10)  # Bins for ages 0-10, 10-20, ..., 90-100
        labels = [f"{i} - {i + 10}" for i in bins[:-1]]  # Labels for each bin (e.g., "10-20", "20-30")

        users = User.objects.all().values("birth_year",)
        # todays date for the year value today
        today = pd.to_datetime('today').year
        # Calculate age from birth year
        df = pd.DataFrame(users)
        df['Age'] = today - df['birth_year']
        # Step 2: Create age categories
        # Create a new column 'Age Category' based on the bins
        df['Age Category'] = pd.cut(df['Age'], bins=bins, labels=labels, right=False)

        # Step 3: Count users in each age category
        age_category_counts = df['Age Category'].value_counts().sort_index()

        # cache.set(age_count_key, age_category_counts.to_dict(), timeout=3600)  # Cache for 1 hour

    # Create a Plotly bar graph
    fig = px.bar(
        x=age_category_counts.index.astype(str),  # Convert categories to strings for display
        y=age_category_counts.values,
        labels={'x': 'Age Category', 'y': 'User Count'},
        title='User Count by Age Category',
        text=age_category_counts.values,  # Display counts on the bars
    )
    fig.update_traces(textposition='outside')  # Position text outside the bars
    fig.update_layout(
        autosize=True,
        height=350,
        xaxis_title='Age Category',
        yaxis_title='User Count',
        title=None,  # Center the title
        template='plotly_dark',
        yaxis=dict(
            dtick=10,  # Custom tick interval
            range=[0, 100]  # Custom tick values
        )
    )

    return fig


def overnight_totals():
    try:
        totals = DailyUserCount.objects.latest('date')
    except DailyUserCount.DoesNotExist:
        totals = None
    if totals is None:
        overight_data = {
            'd_total': 0,
            'd_new': 0,
            'd_growth': 0,
            'wk_new': 0,
            'wk_growth': 0,
            'mth_growth': 0,
            'mth_new': 0,
        }
    else:
        overight_data = {
            'd_total': totals.d_total_user_count,
            'd_new': totals.d_new_user_count,
            'd_growth': totals.d_growth_rate,
            'wk_new': totals.wk_new_user_count,
            'wk_growth': totals.wk_growth_rate,
            'mth_growth': totals.mth_growth_rate,
            'mth_new': totals.mth_new_user_count,
        }

    return overight_data


def daily_total():
    """ Try cache first """
    user_count_key = "user_counts"

    user_counts = None  # cache.get(user_count_key)

    if user_counts:
        # If cache exists, use it
        logging.info(f"Using cached data for {user_count_key}")
        user_counts = pd.DataFrame(user_counts)
        # convert date format to datetime
        user_counts['Date'] = pd.to_datetime(user_counts['Date'], unit='ms').dt.date

    else:
        logging.info('No cache found, daily_total, fetching from database')
        """ fetch manually and reset cache"""
        # Group users by their signup date and count the number of users for each day
        daily_totals = (
            DailyUserCount.objects.all()
            .values('date', 'd_total_user_count')
            .order_by('date')
        )

        total_users = User.objects.filter(is_deleted=False).count()
        # If today's date is not in the daily_totals, add it
        today = pd.to_datetime('today').date()
        # Convert the queryset to a DataFrame
        records = []
        for record in daily_totals:
            records.append({
                'Date': record['date'],
                'CumulativeUsers': record['d_total_user_count'],
            })
        # Add today's data
        if not any(record['Date'] == today for record in records):
            records.append({
                'Date': today,
                'CumulativeUsers': total_users,
            })
        df = pd.DataFrame(records)

        # Ensure SignUpDate is in datetime format
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Calculate the cumulative sum of users
        user_counts = df[['Date', 'CumulativeUsers']]

        # cache.set(user_count_key, user_counts.to_dict(orient='records'), timeout=3600)  # Cache for 1 hour

    # Create the Plotly line chart
    fig = px.line(
        user_counts,
        x='Date',
        y='CumulativeUsers',
        title='Cumulative Number of Users Over Time',
        labels={'Date': 'Date', 'CumulativeUsers': 'Total Users'}
    )

    # Customize the layout
    fig.update_layout(
        autosize=True,
        height=350,
        xaxis_title='Date',
        yaxis_title='Total Users',
        template='plotly_dark',
        margin=dict(l=40, r=40, t=40, b=40),
    )

    return fig


def top_users_fig():
    # Extract data for the chart
    # Get top 5 users based on reward points
    top_users = User.objects.order_by('-reward_points')[:5]
    top_users = top_users.values('email', 'reward_points')
    top_users_list = list(top_users)
    emails = [user['email'] for user in top_users_list]
    reward_points = [user['reward_points'] for user in top_users_list]

    # Create the bar chart
    fig = px.bar(
        x=emails,
        y=reward_points,
        color=emails,  # Different colors for each user
        title="Top 5 Users by Reward Points",
        labels={'y': 'Reward Points'},
    )

    # Customize the layout
    fig.update_layout(
        autosize=True,
        height=350,
        xaxis_title="User",
        yaxis_title="Reward Points",
        xaxis=dict(
            showticklabels=False  # Hide x-axis tick labels
        ),
        template="plotly_dark",
        margin=dict(l=40, r=40, t=40, b=40),
    )
    return fig


# Create a fucntion that will retunr a df of daily user counts
# each row needs to contain the date, count of users signed up that day, and the total of all users on that day
def manual_user_counts():
    # Get all users and their created dates
    users = User.objects.all().values('created_date')

    # Convert to DataFrame
    df = pd.DataFrame(users)
    logging.info(df)

    # Ensure 'created_date' is in datetime format
    df['created_date'] = pd.to_datetime(df['created_date'])

    # Group by date and count users signed up that day
    daily_counts = df.groupby(df['created_date'].dt.date).size().reset_index(name='daily_count')

    # Calculate cumulative total of users up to each date
    daily_counts['total_users'] = daily_counts['daily_count'].cumsum()

    # convert the df to a csv file
    # daily_counts.to_csv('daily_user_counts.csv', index=False)
    logging.info(df)

    return daily_counts
