import django_filters
from core.psql_models import UserHealthEvent


class UserHealthEventFilter(django_filters.FilterSet):
    type = django_filters.ChoiceFilter(
        choices=[
            ('Diagnosis', 'Diagnosis'),
            ('Injury', 'Injury'),
            ('Prescription', 'Prescription'),
            ('Life Event', 'Life Event'),
            ('Procedure', 'Procedure'),
            ('Appointment', 'Appointment'),
            ('Health Update', 'Health Update'),
            ('Vaccination', 'Vaccination'),
            ('Screening', 'Screening'),
            ('Health Check', 'Health Check'),
            ('Mental Health Event', 'Mental Health Event'),
            ('Other', 'Other'),
        ],
        field_name='type',
        lookup_expr='iexact',
        empty_label='Select All',
    )
    order_by_date = django_filters.OrderingFilter(
        fields=(
            ('created_date', 'created_date'),
        ),
        field_labels={
            'created_date': 'Date',
        },
        label='Sort by Date',
        choices=[
            ('created_date', 'Oldest to Newest'),
            ('-created_date', 'Newest to Oldest'),
        ]
    )
    reviewed = django_filters.BooleanFilter(
        field_name='is_reviewed',
        label='Reviewed',
    )
    genetic = django_filters.BooleanFilter(
        field_name='genetic',
        label='Genetic',
    )

    class Meta:
        model = UserHealthEvent
        fields = ['type', 'reviewed', 'genetic']
