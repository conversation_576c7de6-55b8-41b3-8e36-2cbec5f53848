from django.shortcuts import render
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from core.psql_models import User, UserQuestion, UserHealthEvent, DailyUserCount
from core.decorators import role_required, RoleRequiredMixin
from core.role import RoleList<PERSON>iew, RoleUpdateView, RoleDetailView
from surveys.models import Feedback
from .forms import UserHealthEventForm
from .utils import age_fig, daily_total, top_users_fig, overnight_totals
from .filters import UserHealthEventFilter
import pandas as pd


@login_required
@role_required('STF')
def user_data(request):
    # Fetch user data from the database
    user_count = DailyUserCount.objects.all()

    if request.POST:
        DailyUserCount.objects.all().delete()

        return render(request, 'staff/user_count.html', {'user_count': user_count, 'message': 'Daily counts deleted successfully.'})
    return render(request, 'staff/user_count.html', {'user_count': user_count})


@login_required
@role_required('STF')
def staff_home(request):
    role = request.user.role
    # Fetch data for the dashboard

    # user health events for checking
    user_health_events = UserHealthEvent.objects.filter(is_reviewed=False).order_by('-created_date')
    user_health_events_count = user_health_events.count()

    # User Questions
    user_questions = UserQuestion.objects.filter(moderation_status__in=['Pending']).order_by('-created_date')
    user_questions_count = user_questions.count()

    # User Feedback
    # Set today's date
    today = pd.to_datetime('today').normalize()
    seven_days_ago = today - pd.DateOffset(days=7)

    user_feedback = Feedback.objects.all()
    user_feedback_count = user_feedback.count()

    user_feedback_count_week = Feedback.objects.filter(created_at__gte=seven_days_ago).count()

    # get user sign up data from db
    nightly_totals = overnight_totals()

    # Age group chart
    age_group_fig = age_fig()

    # Convert graph to HTML
    age_category_graph = age_group_fig.to_html(full_html=False)

    users_fig = daily_total()
    # Convert graphto HTML
    cumulative_users_chart = users_fig.to_html(full_html=False)

    # reward points fig
    top_users_chart = top_users_fig()
    reward_chart = top_users_chart.to_html(full_html=False)

    # Live User Count
    live_user_count = User.objects.filter(is_deleted=False).count()

    context = {
        'role': role,
        'signups_last_7_days': nightly_totals['wk_new'],
        'wk_growth': nightly_totals['wk_growth'],
        'd_new': nightly_totals['d_new'],
        'mth_new': nightly_totals['mth_new'],
        'd_growth': nightly_totals['d_growth'],
        'd_total': nightly_totals['d_total'],
        'live_count': live_user_count,

        'age_category_graph': age_category_graph,
        'user_health_events_count': user_health_events_count,
        "user_questions_count": user_questions_count,
        "user_feedback_count": user_feedback_count,
        "user_feedback_count_week": user_feedback_count_week,

        "cumulative_users_chart": cumulative_users_chart,
        "reward_chart": reward_chart
    }

    return render(request, 'staff/dashboard.html', context)


class HealthEventsView(LoginRequiredMixin, RoleRequiredMixin, RoleListView):
    model = UserHealthEvent
    template_name = 'staff/user_health_events.html'
    context_object_name = 'user_health_events'
    allowed_roles = ['STF']

    def get_queryset(self):
        # Apply the filter to the queryset
        queryset = UserHealthEvent.objects.all().order_by('-created_date')
        self.filter = UserHealthEventFilter(self.request.GET, queryset=queryset)
        return self.filter.qs  # Return the filtered queryset

    def get_context_data(self, **kwargs):
        # Add the filter and user's role to the context
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filter
        if self.request.htmx:
            self.template_name = 'staff/partials/health_events_container.html'
        return context


class HealthEventDetailView(LoginRequiredMixin, RoleRequiredMixin, RoleDetailView):
    model = UserHealthEvent
    template_name = 'staff/health_event_detail.html'
    context_object_name = 'event'
    pk_url_kwarg = "event_id"
    allowed_roles = ['STF']


class HealthEventUpdateView(LoginRequiredMixin, RoleRequiredMixin, RoleUpdateView):
    model = UserHealthEvent
    form_class = UserHealthEventForm
    template_name = 'staff/health_event_update.html'
    pk_url_kwarg = "event_id"
    context_object_name = 'event'
    success_url = reverse_lazy('health_events')
    allowed_roles = ['STF']

    def form_valid(self, form):
        event = form.save(commit=False)
        # Update the updated_date field
        original_event = UserHealthEvent.objects.get(pk=event.pk)
        event.created_date = original_event.created_date
        event.genetic = event.genetic
        event.is_reviewed = True

        event.save()
        return super().form_valid(form)


# @login_required
# @role_required('STF')
# def pull_user_data(request):
#     counts = manual_user_counts()
#     return HttpResponse(
#         f"User counts pulled successfully: {counts}",
#         status=200,
#         content_type='text/plain')

# @login_required
# @role_required('STF')
# def delete_daily_counts(request):
#     """
#     Deletes all daily user counts from the database.
#     """
#     DailyUserCount.objects.all().delete()

#     return HttpResponse(
#         "All daily user counts deleted successfully.",
#         status=200,
#         content_type='text/plain'
#     )
