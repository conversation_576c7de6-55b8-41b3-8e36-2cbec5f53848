from django.db import models


# Create your models here.
class Feedback(models.Model):
    CATEGORY_CHOICES = [
        ('0', 'I have noticed a bug'),
        ('1', 'I have an idea'),
        ('2', 'Something else'),
    ]
    sub = models.CharField(max_length=100)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, blank=False, null=False, default='0')
    details = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_category_display()} - {self.details[:50]}"
