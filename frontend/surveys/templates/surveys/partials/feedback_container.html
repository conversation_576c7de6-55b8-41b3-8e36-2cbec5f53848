<div id="feedback-container" class="col-span-5 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Feedback</h3>
    <input type="text" id="searchBox" onkeyup="searchFeedback()" placeholder="Search feedback.." class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">  
    <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
      <div class="w-full overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 rounded" id="feedbackTable">
            <thead class="bg-gray-50 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                <tr>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Category</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Details</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Created At</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for item in filter.qs %}
              <tr class="bg-white dark:bg-gray-600">
                <td class="px-4 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ item.get_category_display}}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ item.details }}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ item.created_at }}</div>
                </td>
              </tr>
            {% empty %}
            <tr>
                <td colspan="5">No feedback available.</td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
      </div>
    </div>
  </div>