{% extends 'surveys/feedback_base.html' %}
{% block title %}Submit Feedback{% endblock %}

{% block content %}
<!-- Full-width background container -->
<div class="w-screen overflow-x-hidden bg-gray-800 py-4">
    
    <!-- Centered form wrapper -->
    <div class="mx-auto w-full sm:w-3/4 md:w-1/2 max-w-lg p-2 border border-gray-200 rounded-lg shadow-md bg-gray-800 border-gray-600">
        
        <div class="flex ml-2 mb-4">
            <h2 class="text-2xl text-white mb-4 ml-2">Feedback</h2>
        </div>

        <div class="flex flex-col items-center justify-center">
            <form class="max-w-sm mx-auto" method="post">
                {% csrf_token %}
                {% if form.errors %}
                <div class="mt-4">
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold">Please check your answers</strong>
                        <span class="block sm:inline">{{ form.errors }}</span>
                    </div>
                </div>
                {% endif %}

                <!-- Category radios -->
                <div class="relative z-0 w-full mb-5 px-4">
                    <div class="flex flex-wrap gap-4">
                        {% for radio in form.category %}
                        <div>
                            <input id="{{ radio.id_for_label }}" type="radio" name="{{ form.category.name }}" value="{{ radio.data.value }}" class="peer hidden">
                            <label for="{{ radio.id_for_label }}" class="inline-block py-3 px-2 bg-gray-100 text-gray-500 rounded-full transition-all duration-300 ease-in-out hover:bg-inherit-green hover:border-2 hover:border-inherit-green-dark peer-checked:bg-inherit-green-dark peer-checked:text-white peer-checked:border-2 peer-checked:border-inherit-green-dark cursor-pointer whitespace-nowrap">
                                {{ radio.choice_label }}
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <div class="relative z-0 w-full mb-5 px-4">
                    <label for="id_details" class="text-lg rounded-full text-white mb-2">Please provide details:</label>
                    <div class="mt-2">
                        {{ form.details }}
                    </div>
                </div>

                <div>
                    {{ form.captcha }}
                </div>

                <div class="mb-4 px-4">
                    <button type="submit" class="w-full text-lg text-button-light border-2 border-button-dark bg-button-dark hover:bg-button-light hover:text-button-dark transition-all duration-300 ease-in-out focus:ring-4 focus:outline-none focus:ring-button-light font-medium rounded-full px-5 py-2.5 text-center">
                        <i class="far fa-save mr-2"></i><span>Save</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
