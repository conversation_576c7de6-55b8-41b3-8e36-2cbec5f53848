from django.views.generic import CreateView
from surveys.models import Feedback
from django.urls import reverse_lazy
from core.role import Role<PERSON>ist<PERSON>iew
from .forms import FeedbackForm
from .filters import FeedbackFilter
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin


class FeedbackCreateView(CreateView):
    model = Feedback
    form_class = FeedbackForm
    template_name = 'surveys/feedback_form.html'
    success_url = reverse_lazy('feedback_thank_you')

    def form_valid(self, form):
        # Extract the 'sub' parameter from the URL
        sub = self.kwargs.get('sub')
        form.instance.sub = sub
        return super().form_valid(form)

    def get(self, request, *args, **kwargs):
        self.object = None
        form = self.get_form()

        return self.render_to_response(self.get_context_data(form=form))

    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

# This view has no role restrictions, so it can be accessed by any authenticated user.


class FeedbackView(RoleListView, LoginRequiredMixin):
    model = Feedback
    template_name = 'surveys/feedback.html'
    context_object_name = 'feedback'
    queryset = Feedback.objects.all()
    filterset_class = FeedbackFilter

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        context['filter'] = FeedbackFilter(self.request.GET, queryset=self.get_queryset())
        context['feedback'] = Feedback.objects.all()
        if self.request.htmx:
            self.template_name = 'surveys/partials/feedback_container.html'
        return context

    def get_queryset(self):
        queryset = super().get_queryset()  # noqa: F841
        return Feedback.objects.order_by('-created_at')


class FeedbackThankYouView(TemplateView):
    template_name = 'surveys/feedback_thank_you.html'
