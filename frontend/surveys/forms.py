from django import forms
from django_recaptcha.fields import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from django_recaptcha.widgets import ReCaptchaV2Invisible
from surveys.models import Feedback


class FeedbackForm(forms.ModelForm):
    captcha = ReCaptchaField(widget=ReCaptchaV2Invisible)
    captcha.widget.attrs.update({
        'required_score': 0.85,
    })

    class Meta:
        model = Feedback
        # Exclude 'sub' since it will be set automatically
        fields = ['category', 'details']
        widgets = {
            'category': forms.RadioSelect(attrs={
                'class': 'peer',
                'required': True
            }),
            'details': forms.Textarea(attrs={
                'label': 'Please provide details:',
                'rows': 5,
                'class': 'form-control w-full max-w-full rounded-lg border bg-gray-200 text-gray-900',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Dynamically reset the choices for the 'category' field
        self.fields['category'].choices = [
            ('0', 'I have noticed a bug'),
            ('1', 'I have an idea'),
            ('2', 'Something else'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        category = cleaned_data.get('category')
        details = cleaned_data.get('details')

        if not category:
            self.add_error('category', 'Category is required.')

        if not details or len(details) < 10:
            self.add_error('details', 'Details must be at least 10 characters long.')

        return cleaned_data
