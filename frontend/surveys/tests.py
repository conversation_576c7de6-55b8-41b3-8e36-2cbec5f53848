from django.test import TestCase, Client
from django.urls import reverse
from surveys.models import Feedback
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from surveys.forms import FeedbackForm
from django.contrib.auth import get_user_model


User = get_user_model()


class FeedbackViewTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(email='<EMAIL>', password='password', role='STF')
        self.feedback = Feedback.objects.create(
            sub="test_user",
            category="1",
            details="This is a test feedback."
        )

    def test_feedback_create_view_get(self):
        """Test GET request to FeedbackCreateView"""
        # self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('feedback_create', kwargs={'sub': 'test_user'}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'surveys/feedback_form.html')
        self.assertIsInstance(response.context['form'], FeedbackForm)

    def test_feedback_create_view_post(self):
        """Test POST request to FeedbackCreateView"""
        # self.client.login(email='<EMAIL>', password='password')
        response = self.client.post(reverse('feedback_create', kwargs={'sub': 'test_user'}), {
            'category': '1',
            'details': 'This is a test feedback.',
        })
        self.assertEqual(response.status_code, 200)  # Redirect after successful form submission
        # self.assertRedirects(response, reverse('feedback_thank_you'))
        self.assertTrue(Feedback.objects.filter(sub='test_user', category='1', details='This is a test feedback.').exists())

    def test_feedback_view(self):
        """Test FeedbackView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('feedback'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'surveys/feedback.html')
        self.assertIn(self.feedback, response.context['feedback'])

    def test_feedback_thank_you_view(self):
        """Test FeedbackThankYouView"""
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('feedback_thank_you'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'surveys/feedback_thank_you.html')


class FeedbackModelTests(TestCase):
    def test_feedback_creation(self):
        """Test creating a Feedback instance"""
        feedback = Feedback.objects.create(
            sub="test_user",
            category="1",
            details="This is a test idea for improving the app."
        )
        self.assertEqual(feedback.sub, "test_user")
        self.assertEqual(feedback.category, "1")
        self.assertEqual(feedback.details, "This is a test idea for improving the app.")
        self.assertIsNotNone(feedback.created_at)
        self.assertIsNotNone(feedback.updated_at)

    def test_default_category(self):
        """Test that the default category is '0'"""
        feedback = Feedback.objects.create(
            sub="test_user",
            details="This is a test bug report."
        )
        self.assertEqual(feedback.category, "0")
        self.assertEqual(feedback.get_category_display(), "I have noticed a bug")

    def test_category_choices(self):
        """Test that invalid category choices raise a ValidationError"""
        feedback = Feedback(
            sub="test_user",
            category="invalid_choice",  # Invalid choice
            details="This should fail due to invalid category."
        )
        with self.assertRaises(ValidationError):
            feedback.full_clean()

    def test_blank_sub_field(self):
        """Test that the 'sub' field can be blank or null"""
        with self.assertRaises(IntegrityError):
            Feedback.objects.create(
                sub=None,  # This should now raise an error
                category="2",
                details="This is a test feedback with no sub."
            )

    def test_str_method(self):
        """Test the __str__ method of the Feedback model"""
        feedback = Feedback.objects.create(
            sub="test_user",
            category="1",
            details="This is a test idea for improving the app."
        )
        expected_str = "I have an idea - This is a test idea for improving the app."
        self.assertEqual(str(feedback), expected_str)
