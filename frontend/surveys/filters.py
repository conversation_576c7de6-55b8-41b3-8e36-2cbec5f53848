import django_filters
from .models import Feedback


class FeedbackFilter(django_filters.FilterSet):
    category = django_filters.ChoiceFilter(
        choices=[
            ('0', 'I have noticed a bug'),
            ('1', 'I have an idea'),
            ('2', 'Something else'),
        ],
        field_name='category',
        lookup_expr='iexact',
        empty_label='Select All',
    )
    created_at = django_filters.DateFromToRangeFilter(
        field_name='created_at',
        label='Date Range',
        widget=django_filters.widgets.RangeWidget(attrs={'placeholder': 'YYYY-MM-DD'}),
    )
    updated_at = django_filters.DateFromToRangeFilter(
        field_name='updated_at',
        label='Date Range',
        widget=django_filters.widgets.RangeWidget(attrs={'placeholder': 'YYYY-MM-DD'}),
    )
    order_by_date = django_filters.OrderingFilter(
        fields=(
            ('created_at', 'created_at'),
        ),
        field_labels={
            'created_at': 'Date',
        },
        label='Sort by Date',
        choices=[
            ('created_at', 'Oldest to Newest'),
            ('-created_at', 'Newest to Oldest'),
        ]
    )

    class Meta:
        model = Feedback
        fields = ['category', 'created_at', 'updated_at']
