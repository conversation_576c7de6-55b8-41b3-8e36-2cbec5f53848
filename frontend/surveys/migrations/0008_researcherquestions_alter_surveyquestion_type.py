# Generated by Django 5.1.6 on 2025-04-03 10:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("surveys", "0007_alter_feedback_category"),
    ]

    operations = [
        migrations.CreateModel(
            name="ResearcherQuestions",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dob",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date of Birth"
                    ),
                ),
                (
                    "genetic",
                    models.<PERSON><PERSON>anField(
                        blank=True, null=True, verbose_name="Genetic Disorder"
                    ),
                ),
                (
                    "health_event_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("0", "Diagnosis"),
                            ("1", "Injury"),
                            ("2", "Prescription"),
                            ("3", "Life Event"),
                            ("4", "Procedure"),
                            ("5", "Appointment"),
                            ("6", "Health Update"),
                            ("7", "Vaccination"),
                            ("8", "Screening"),
                            ("9", "Health Check"),
                            ("10", "Mental Health Event"),
                            ("11", "Other"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Health Event Type",
                    ),
                ),
                (
                    "details",
                    models.TextField(blank=True, null=True, verbose_name="Details"),
                ),
                (
                    "start_date",
                    models.DateField(blank=True, null=True, verbose_name="Start Date"),
                ),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
            ],
        ),
        migrations.AlterField(
            model_name="surveyquestion",
            name="type",
            field=models.CharField(
                choices=[
                    ("text", "Text"),
                    ("radio", "Radio"),
                    ("checkbox", "Checkbox"),
                    ("multicheck", "MultiCheck"),
                    ("date", "Date"),
                ],
                max_length=10,
            ),
        ),
    ]
