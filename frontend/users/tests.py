from django.test import TestCase
from users.models import CustomUser


class TestCustomUserModel(TestCase):

    def setUp(self):
        self.user_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Test",
            "last_name": "User",
        }
        self.superuser_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
        }

    def test_create_user(self):
        user = CustomUser.objects.create_user(**self.user_data)
        self.assertEqual(user.email, self.user_data["email"])
        self.assertTrue(user.check_password(self.user_data["password"]))
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_superuser(self):
        superuser = CustomUser.objects.create_superuser(**self.superuser_data)
        self.assertEqual(superuser.email, self.superuser_data["email"])
        self.assertTrue(superuser.check_password(self.superuser_data["password"]))
        self.assertTrue(superuser.is_staff)
        self.assertTrue(superuser.is_superuser)

    def test_create_user_without_email_raises_error(self):
        with self.assertRaises(ValueError):
            CustomUser.objects.create_user(email=None, password="password123")

    def test_create_superuser_without_is_staff_raises_error(self):
        with self.assertRaises(ValueError):
            CustomUser.objects.create_superuser(
                email="<EMAIL>", password="password123", is_staff=False
            )

    def test_create_superuser_without_is_superuser_raises_error(self):
        with self.assertRaises(ValueError):
            CustomUser.objects.create_superuser(
                email="<EMAIL>", password="password123", is_superuser=False
            )

    def test_user_str_method(self):
        user = CustomUser.objects.create_user(**self.user_data)
        self.assertEqual(str(user), "Test User (<EMAIL>)")

    def test_user_has_perm(self):
        user = CustomUser.objects.create_user(**self.user_data)
        self.assertFalse(user.has_perm("some_perm"))

    def test_user_has_module_perms(self):
        user = CustomUser.objects.create_user(**self.user_data)
        self.assertFalse(user.has_module_perms("some_app"))

    def test_superuser_has_perm(self):
        superuser = CustomUser.objects.create_superuser(**self.superuser_data)
        self.assertTrue(superuser.has_perm("some_perm"))

    def test_superuser_has_module_perms(self):
        superuser = CustomUser.objects.create_superuser(**self.superuser_data)
        self.assertTrue(superuser.has_module_perms("some_app"))
