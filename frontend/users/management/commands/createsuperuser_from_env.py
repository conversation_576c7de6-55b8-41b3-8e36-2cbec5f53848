from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings
import logging


class Command(BaseCommand):
    help = 'Create a superuser from environment variables.'

    def handle(self, *args, **options):
        User = get_user_model()
        email = settings.INHERIT_SUPERUSER_EMAIL

        if not User.objects.filter(email=email).exists():
            User.objects.create_superuser(
                email=email,
                password=settings.INHERIT_SUPERUSER_PASSWORD,
                role='STF'
            )
            logging.info(f"Created superuser with email {email}")
