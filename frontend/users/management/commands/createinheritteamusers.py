from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
import logging


class Command(BaseCommand):
    help = 'Create Inherit team users.'

    def handle(self, *args, **options):
        logging.info("Creating Inherit team users...")
        User = get_user_model()
        emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ]

        for email in emails:
            if not User.objects.filter(email=email).exists():
                User.objects.create_user(
                    email=email,
                    password=None,
                    role='STF'
                )
                logging.info(f"Created staff user with email {email}")
