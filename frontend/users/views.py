from django.contrib.auth import logout
from django.shortcuts import redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from allauth.account.views import LoginView
from .forms import CustomLoginForm
from core.decorators import RoleRequiredMixin
from core.role import <PERSON><PERSON><PERSON><PERSON>iew, RoleUpdateView
from core.psql_models import Expert
from django.forms.widgets import Textarea
from django.utils import timezone
import uuid


def logout_view(request):
    logout(request)
    return redirect('account_login')


class CustomLoginView(LoginView):
    form_class = CustomLoginForm


class CreateExpertId(LoginRequiredMixin, RoleRequiredMixin, RoleCreateView):
    model = Expert
    fields = ['name', 'qualifications']
    template_name = 'users/create_expert_id.html'
    success_url = '/'
    allowed_roles = ['STF']

    def form_valid(self, form):
        form.instance.user = self.request.user
        form.instance.created_date = timezone.now()
        form.instance.is_deleted = False
        form.instance.expert_id = str(uuid.uuid4())
        response = super().form_valid(form)

        # Assign the created Expert object's ID to the current user's expert_id field
        self.request.user.expert_id = form.instance.expert_id
        self.request.user.save()

        return response

    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        for field_name, field in form.fields.items():
            if isinstance(field.widget, Textarea):
                field.widget.attrs.update({
                    'rows': '1',
                    'style': 'resize: none;'
                })
        return form


class UpdateExpertId(LoginRequiredMixin, RoleRequiredMixin, RoleUpdateView):
    model = Expert
    fields = ['name', 'qualifications']
    template_name = 'users/create_expert_id.html'
    success_url = '/'
    allowed_roles = ['STF', 'EXP']

    def get_object(self, queryset=None):
        # Fetch the Expert object associated with the current user
        return Expert.objects.get(expert_id=self.request.user.expert_id)

    def form_valid(self, form):
        # Update the Expert object
        form.instance.updated_date = timezone.now()
        response = super().form_valid(form)

        # Optionally update the user's expert_id field if needed
        self.request.user.expert_id = form.instance.expert_id
        self.request.user.save()

        return response

    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        for field_name, field in form.fields.items():
            if isinstance(field.widget, Textarea):
                field.widget.attrs.update({
                    'rows': '1',
                    'style': 'resize: none;'
                })
        return form
