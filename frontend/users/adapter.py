from allauth.account.adapter import Default<PERSON><PERSON>untAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from django.forms import ValidationError
from django.contrib.auth import get_user_model


class CustomAccountAdapter(DefaultAccountAdapter):
    def is_open_for_signup(self, request):
        return False

    def save_user(self, request, user, form, commit=True):
        if not user.pk:
            raise ValidationError("New account creation is disabled.")
        return super().save_user(request, user, form, commit)


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    def pre_social_login(self, request, sociallogin):
        # This method is called after a successful authentication from the provider
        # but before the login is actually processed.
        if sociallogin.is_existing:
            return

        # Try to find an existing user with the same email address
        email = sociallogin.account.extra_data.get('email')
        if email:
            try:
                user = get_user_model().objects.get(email=email)
                sociallogin.connect(request, user)
            except get_user_model().DoesNotExist:
                pass  # User does not exist, do nothing
