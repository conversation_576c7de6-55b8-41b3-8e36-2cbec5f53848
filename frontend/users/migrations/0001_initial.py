# Generated by Django 5.1.6 on 2025-02-05 16:33

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.Char<PERSON><PERSON>(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('role', models.CharField(choices=[('STF', 'Staff'), ('SUP', 'Support'), ('RES', 'Research'), ('LED', 'Lead')], default='STF', max_length=3, verbose_name='User Role')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=30)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=30)),
                ('is_active', models.<PERSON><PERSON>anField(default=True)),
                ('is_staff', models.<PERSON><PERSON>anField(default=False)),
                ('is_superuser', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
