document.addEventListener('DOMContentLoaded', function() {
    const toggleSidebarMobile = document.getElementById('toggleSidebarMobile');
    const closeSidebarMobile = document.getElementById('closeSidebarMobile');
    const sidebar = document.getElementById('sidebar');
    const toggleSidebarMobileHamburger = document.getElementById('toggleSidebarMobileHamburger');
    const toggleSidebarMobileClose = document.getElementById('toggleSidebarMobileClose');
  
    toggleSidebarMobile.addEventListener('click', function() {
      sidebar.classList.toggle('-translate-x-full');
      toggleSidebarMobileHamburger.classList.toggle('hidden');
      toggleSidebarMobileClose.classList.toggle('hidden');
    });
  
    closeSidebarMobile.addEventListener('click', function() {
      sidebar.classList.add('-translate-x-full');
      toggleSidebarMobileHamburger.classList.remove('hidden');
      toggleSidebarMobileClose.classList.add('hidden');
    });
  });