worker_processes 1;
user www-data;
pid /run/nginx.pid;

events {
  worker_connections 1024;
  accept_mutex off; # set to 'on' if nginx worker_processes > 1
  use epoll;
}

http {
  access_log /var/log/nginx/access.log;
  error_log /var/log/nginx/error.log;
  default_type application/octet-stream; # fallback in case we can't determine a type
  include /etc/nginx/mime.types;
  sendfile on;
  tcp_nopush on;
  gzip on;

  upstream app_server {
    server 127.0.0.1:8001 fail_timeout=0; # fail_timeout=0 means we always retry an upstream even if it failed to return a good HTTP response
  }

  server {
    listen 80 deferred;

    client_max_body_size 4G;
    keepalive_timeout 5;

    location /static {
      alias /app/frontend/static;
    }
    location / {
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header Host $http_host;
      proxy_redirect off;
      proxy_buffering off;
      proxy_ignore_client_abort on;
      proxy_pass http://app_server; # app_server is defined as an upstream above
    }

    #error_page 500 502 503 504 /500.html;
    #location = /500.html {
    #  root /path/to/app/current/public;
    #}
  }
}
