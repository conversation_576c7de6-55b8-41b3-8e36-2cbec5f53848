from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from core.psql_models import UserHealthEvent
from django.db import connection


class ResearchHomeViewTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            role='RES'
        )
        self.client.login(email='<EMAIL>', password='testpassword')

        # Create the table if it does not exist
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_health_event (
                    event_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    type TEXT,
                    details TEXT,
                    notes TEXT,
                    start_date DATE,
                    end_date DATE,
                    ongoing BOOLEAN,
                    created_date TIMESTAMP,
                    is_deleted BOOLEAN,
                    deleted_date TIMESTAMP
                )
            """)

        # Create test data in the allowed table
        UserHealthEvent.objects.create(
            event_id='randomstring',
            user_id='user_id',
            type='type',
            details='details',
            notes='notes',
            start_date='2021-01-01',
            end_date='2021-01-01',
            ongoing=False,
            created_date='2021-01-01',
            is_deleted=False,
            deleted_date=None
        )

    def test_only_select_queries_allowed(self):
        response = self.client.post(reverse('research_home'), {'sql_query': 'DELETE FROM user'})
        self.assertContains(response, 'Only SELECT queries are allowed', status_code=200)

    def test_query_allowed_tables(self):
        response = self.client.post(reverse('research_home'), {'sql_query': 'SELECT * FROM "user_health_event";'})
        self.assertNotContains(response, 'Query not allowed on the specified tables', status_code=200)

    def test_query_disallowed_tables(self):
        response = self.client.post(reverse('research_home'), {'sql_query': 'SELECT * FROM user'})
        self.assertContains(response, 'Query not allowed on the specified tables', status_code=200)
