from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

User = get_user_model()


class RoleAccessTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.staff_user = User.objects.create_user(email='staffuser', password='password', role='STF')
        self.research_user = User.objects.create_user(email='researchuser', password='password', role='RES')
        self.support_user = User.objects.create_user(email='supportuser', password='password', role='SUP')
        self.leadership_user = User.objects.create_user(email='leadershipuser', password='password', role='LEAD')

    def test_staff_access(self):
        self.client.login(email='staffuser', password='password')
        response = self.client.get(reverse('home'))
        self.assertTemplateUsed(response, 'core/staff_home.html')

        response = self.client.get(reverse('research_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('support_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('leadership_home'))
        self.assertRedirects(response, reverse('home'))

    def test_research_access(self):
        self.client.login(email='researchuser', password='password')
        response = self.client.get(reverse('home'))
        self.assertTemplateUsed(response, 'core/research_home.html')

        response = self.client.get(reverse('staff_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('support_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('leadership_home'))
        self.assertRedirects(response, reverse('home'))

    def test_support_access(self):
        self.client.login(email='supportuser', password='password')
        response = self.client.get(reverse('home'))
        self.assertTemplateUsed(response, 'core/support_home.html')

        response = self.client.get(reverse('staff_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('research_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('leadership_home'))
        self.assertRedirects(response, reverse('home'))

    def test_leadership_access(self):
        self.client.login(email='leadershipuser', password='password')
        response = self.client.get(reverse('home'))
        self.assertTemplateUsed(response, 'core/leadership_home.html')

        response = self.client.get(reverse('staff_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('research_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('support_home'))
        self.assertRedirects(response, reverse('home'))
