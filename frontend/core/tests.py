from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .psql_models import User


CustomUser = get_user_model()


class RoleAccessTests(TestCase):
    multi_db = True
    databases = {'default', 'backend'}

    def setUp(self):
        self.client = Client()
        self.staff_user = CustomUser.objects.create_user(email='<EMAIL>', password='password', role='STF')
        self.research_user = CustomUser.objects.create_user(email='<EMAIL>', password='password', role='RES')
        self.support_user = CustomUser.objects.create_user(email='<EMAIL>', password='password', role='SUP')
        self.leadership_user = CustomUser.objects.create_user(email='<EMAIL>', password='password', role='LED')

        User.objects.create(
            user_id="some_ids",
            sub='secondsub',
            email='<EMAIL>',
            username='seconduser',
            birth_month=6,
            birth_year=1990,
            sex_assigned_at_birth='Male',
            referral_code='1234',
            reward_points=0,
            reward_point_awarded_for_instagram=False,
            reward_point_awarded_for_personal_health_survey=False,
            reward_point_awarded_for_reproductive_health_survey=False,
            reward_point_awarded_for_lifestyle_survey=False,
            reward_point_awarded_for_sharing=False,
            reward_point_awarded_to_referrer=False,
            impact_points=0,
            created_date='2025-05-18',
            is_deleted=False,
        )

        User.objects.create(
            user_id="some_id",
            sub='firstsub',
            email='<EMAIL>',
            username='firstuser',
            birth_month=7,
            birth_year=1985,
            sex_assigned_at_birth='Female',
            referral_code='2345',
            reward_points=0,
            reward_point_awarded_for_instagram=False,
            reward_point_awarded_for_personal_health_survey=False,
            reward_point_awarded_for_reproductive_health_survey=False,
            reward_point_awarded_for_lifestyle_survey=False,
            reward_point_awarded_for_sharing=False,
            reward_point_awarded_to_referrer=False,
            impact_points=0,
            created_date='2025-05-18',
            is_deleted=False,
        )

    def test_staff_access(self):
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('home'))
        self.assertTemplateUsed(response, 'staff/dashboard.html')

        response = self.client.get(reverse('research_home'))
        self.assertTemplateUsed(response, 'researchers/research_home.html')

        response = self.client.get(reverse('support_home'))
        self.assertTemplateUsed(response, 'core/support_home.html')

        response = self.client.get(reverse('leadership_home'))
        self.assertTemplateUsed(response, 'core/leadership_home.html')

    def test_research_access(self):
        self.client.login(email='<EMAIL>', password='password')
        response = self.client.get(reverse('home'))

        self.assertTemplateUsed(response, 'researchers/research_home.html')

        response = self.client.get(reverse('staff_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('support_home'))
        self.assertRedirects(response, reverse('home'))

        response = self.client.get(reverse('leadership_home'))
        self.assertRedirects(response, reverse('home'))

    # def test_support_access(self):
    #     self.client.login(email='supportuser', password='password')
    #     response = self.client.get(reverse('home'))
    #     self.assertTemplateUsed(response, 'core/support_home.html')

    #     response = self.client.get(reverse('staff_home'))
    #     self.assertRedirects(response, reverse('home'))

    #     response = self.client.get(reverse('research_home'))
    #     self.assertRedirects(response, reverse('home'))

    #     response = self.client.get(reverse('leadership_home'))
    #     self.assertRedirects(response, reverse('home'))

    # def test_leadership_access(self):
    #     self.client.login(email='leadershipuser', password='password')
    #     response = self.client.get(reverse('home'))
    #     self.assertTemplateUsed(response, 'core/leadership_home.html')

    #     response = self.client.get(reverse('staff_home'))
    #     self.assertRedirects(response, reverse('home'))

    #     response = self.client.get(reverse('research_home'))
    #     self.assertRedirects(response, reverse('home'))

    #     response = self.client.get(reverse('support_home'))
    #     self.assertRedirects(response, reverse('home'))
