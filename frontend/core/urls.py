from django.urls import path
from core import views

urlpatterns = [
    path('', views.home, name='home'),
    path('staff_home/', views.staff_home, name='staff_home'),
    path('research/', views.research_home, name='research_home'),
    path('support/', views.support_home, name='support_home'),
    path('leadership/', views.leadership_home, name='leadership_home'),
    path('download_results/', views.download_results, name='download_results'),
    path('user_management/', views.UserListView.as_view(), name='user_management'),
    path('app-user/<str:user_id>/', views.UserDetailView.as_view(), name='user_detail'),
    path('app-user/<str:user_id>/update/', views.UserUpdateView.as_view(), name='user_update'),
    path('app-user/<str:user_id>/delete/', views.UserDeleteView.as_view(), name='user_delete'),
]
