
from django.views.generic import View, ListView, CreateView, UpdateView, DetailView, DeleteView
from django.shortcuts import render


class RoleView(View):
    """
    Sub-class the view to add role to context
    """
    template_name = None  # Define this in subclasses

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        return render(request, self.template_name, context)

    def get_context_data(self, **kwargs):
        context = kwargs
        context['role'] = self.request.user.role
        return context


class RoleListView(ListView):
    """
    sub-class the list view to add role to context
    """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


class RoleCreateView(CreateView):
    """
    sub-class the create view to add role to context
    """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


class RoleUpdateView(UpdateView):
    """
    sub-class the update view to add role to context
    """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


class RoleDetailView(DetailView):
    """
    sub-class the detail view to add role to context
    """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


class RoleDeleteView(DeleteView):
    """
    sub-class the delete view to add role to context
    """
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context
