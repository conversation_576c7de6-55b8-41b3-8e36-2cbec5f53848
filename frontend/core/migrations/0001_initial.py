# Generated by Django 5.1.6 on 2025-05-23 10:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DailyUserCount",
            fields=[
                ("date_id", models.TextField(primary_key=True, serialize=False)),
                ("date", models.DateField()),
                ("d_new_user_count", models.IntegerField()),
                ("d_total_user_count", models.IntegerField()),
                ("d_growth_rate", models.FloatField()),
                ("wk_new_user_count", models.IntegerField()),
                ("wk_growth_rate", models.FloatField()),
                ("wk_total_user_count", models.IntegerField()),
                ("mth_new_user_count", models.IntegerField()),
                ("mth_total_user_count", models.IntegerField()),
                ("mth_growth_rate", models.FloatField()),
                ("created_date", models.DateTimeField()),
            ],
            options={
                "db_table": "daily_user_count",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Expert",
            fields=[
                ("expert_id", models.TextField(primary_key=True, serialize=False)),
                ("sub", models.TextField(unique=True)),
                ("name", models.TextField()),
                ("qualifications", models.TextField()),
                ("email", models.TextField(unique=True)),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "expert",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ExpertAnswer",
            fields=[
                ("answer_id", models.TextField(primary_key=True, serialize=False)),
                ("question_text", models.TextField()),
                ("answer_text", models.TextField()),
                ("position_in_tree", models.IntegerField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "expert_answer",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ExpertAnswerTag",
            fields=[
                ("tag_id", models.TextField(primary_key=True, serialize=False)),
                ("tag_name", models.TextField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "expert_answer_tag",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("user_id", models.TextField(primary_key=True, serialize=False)),
                ("sub", models.TextField(unique=True)),
                ("username", models.TextField(unique=True)),
                ("email", models.TextField(unique=True)),
                ("birth_month", models.IntegerField()),
                ("birth_year", models.IntegerField()),
                ("sex_assigned_at_birth", models.TextField()),
                ("reward_points", models.IntegerField()),
                ("reward_point_awarded_for_instagram", models.BooleanField()),
                (
                    "reward_point_awarded_for_personal_health_survey",
                    models.BooleanField(),
                ),
                (
                    "reward_point_awarded_for_reproductive_health_survey",
                    models.BooleanField(),
                ),
                ("reward_point_awarded_for_lifestyle_survey", models.BooleanField()),
                ("reward_point_awarded_for_sharing", models.BooleanField()),
                ("reward_point_awarded_to_referrer", models.BooleanField()),
                ("referral_code", models.TextField(unique=True)),
                ("referrer", models.TextField(blank=True, null=True)),
                ("instagram_handle", models.TextField(blank=True, null=True)),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserAppointmentSupportRequest",
            fields=[
                ("request_id", models.TextField(primary_key=True, serialize=False)),
                ("response_output", models.TextField()),
                ("appointment_type", models.TextField()),
                ("appointment_details", models.TextField(blank=True, null=True)),
                ("rating", models.IntegerField(blank=True, null=True)),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_appointment_support_request",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserHealthEvent",
            fields=[
                ("event_id", models.TextField(primary_key=True, serialize=False)),
                ("type", models.TextField()),
                ("details", models.TextField()),
                ("notes", models.JSONField(blank=True, null=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("ongoing", models.BooleanField()),
                ("genetic", models.BooleanField()),
                ("is_reviewed", models.BooleanField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_health_event",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Username",
            fields=[
                ("username", models.TextField(primary_key=True, serialize=False)),
                ("referral_code", models.TextField(unique=True)),
                ("assigned", models.BooleanField()),
            ],
            options={
                "db_table": "username",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserQuestion",
            fields=[
                ("question_id", models.TextField(primary_key=True, serialize=False)),
                ("question_text", models.TextField()),
                ("moderation_status", models.TextField()),
                ("votes", models.IntegerField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_question",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserScreening",
            fields=[
                ("screening_id", models.TextField(primary_key=True, serialize=False)),
                ("type", models.TextField()),
                ("subtype", models.TextField(blank=True, null=True)),
                ("next_date", models.DateField(blank=True, null=True)),
                ("last_date", models.DateField(blank=True, null=True)),
                ("attended_date", models.DateField(blank=True, null=True)),
                (
                    "months_between_appointments",
                    models.IntegerField(blank=True, null=True),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("status", models.TextField()),
                ("user_managed_schedule", models.BooleanField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_screening",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserShare",
            fields=[
                ("share_id", models.TextField(primary_key=True, serialize=False)),
                ("sharee_relationship_to_sharer", models.TextField()),
                ("label_for_sharer", models.TextField()),
                ("label_for_sharee", models.TextField()),
                ("approved", models.BooleanField()),
                ("approved_date", models.DateTimeField(blank=True, null=True)),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_share",
                "managed": False,
            },
        ),
    ]
