# Generated by Django 5.1.6 on 2025-06-11 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserScreeningAlert",
            fields=[
                ("alert_id", models.TextField(primary_key=True, serialize=False)),
                ("type", models.TextField()),
                ("subtype", models.TextField(blank=True, null=True)),
                ("next_date", models.DateField(blank=True, null=True)),
                (
                    "suggested_months_between_appointments",
                    models.IntegerField(blank=True, null=True),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("status", models.TextField()),
                ("created_date", models.DateTimeField()),
                ("is_deleted", models.BooleanField()),
                ("deleted_date", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_screening_alert",
                "managed": False,
            },
        ),
    ]
