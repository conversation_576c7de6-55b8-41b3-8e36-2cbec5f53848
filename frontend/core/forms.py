from django import forms
from core.psql_models import User


class UserUpdateForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ['email', 'reward_points', 'is_deleted']
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-control text-gray-900'}),
            'reward_points': forms.NumberInput(attrs={'class': 'form-control text-gray-900'}),
            'is_deleted': forms.CheckboxInput(attrs={'class': 'form-check-input text-gray-900'}),
        }
