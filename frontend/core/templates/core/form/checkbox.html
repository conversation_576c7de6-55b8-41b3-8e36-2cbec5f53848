{% load widget_tweaks %}

<div class="flex flex-col {% if hide == True %}hidden{% endif %}" id="fieldWrapper-{{ field.id_for_label }}">
    
    <div class="inline-flex gap-2 items-center text-white">
        {{ field }}
        <label for="{{ field.id_for_label }}">
            <span class="checkbox-field-label font-light text-sm">{{ field.label }}</span>
        </label>
        {% if tooltip %}
        <span>
            {% include 'core/form/tooltip-icon.html' with id=field.id_for_label %}
            <div id="tooltip-{{field.id_for_label}}" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                {{ tooltip }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </span>
        {% endif %}
    </div>
    
    {% if field.help_text %}
        <div>
            <p class="text-sm mt-3">
                {{ field.help_text }}
            </p>
        </div>
    {% endif %}
    
    {# Field errors #}
    <div class="text-red-600 text-sm">
        {{ field.errors }}
    </div>
</div>

