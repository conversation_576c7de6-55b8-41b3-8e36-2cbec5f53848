{% load widget_tweaks %}

<div name="{{ field.name }}" class="field-wrapper {% if hide == True %}hidden{% endif %}" id="fieldWrapper-{{ field.auto_id }}">
    
    <label for="{{ field.id_for_label }}" class="block text-black text-md font-bold mb-2 text-m">
        {{ field.label }}
        {% if tooltip %}
        <span>
            {% include 'core/form/tooltip-icon.html' with id=field.id_for_label %}
            <div id="tooltip-{{field.id_for_label}}" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-seomain-600 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip dark:bg-seomain-600">
                {{ tooltip }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </span>
        {% endif %}
    </label>

    <div class="text-sm px-2 border-gray-600 rounded border-2 focus:outline-none focus:shadow-outline">
        <div class="checkbox-container">
            {% render_field field class="mr-1 p-1" %}
        </div>
    </div>
    
    {% if field.help_text %}
        <div>
            <p class="text-md">
                {{ field.help_text }}
            </p>
        </div>
    {% endif %}
    
    {# Field errors #}
    <div class="text-red-600 text-sm">
        {{ field.errors }}
    </div>
</div>
