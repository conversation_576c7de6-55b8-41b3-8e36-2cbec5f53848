{% load widget_tweaks %}

<!-- TODO: improve this -->
<div class="fieldWrapper {% if hide == True %}hidden{% endif %}" id="fieldWrapper-{{ field.id_for_label }}">

    <!-- Field Label -->
    <div class="inline-flex items-start mb-2">
        <span>
            {{ field|add_label_class:"text-white text-md font-bold mb-2" }}
        </span>
    
        <!-- Tooltip -->
        {% if tooltip %}
        <span class="ml-2">
            {% include 'core/form/tooltip-icon.html' with id=field.id_for_label %}
            <div id="tooltip-{{field.id_for_label}}" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-md font-bold text-white rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                {{ tooltip }}
                <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
        </span>
        {% endif %}
    </div>

    <!-- Input field -->
    <div class="flex">
        {{ field.field.data }}     
        <select id="{{ field.id_for_label }}" 
                name="{{ field.html_name }}"
                
                {% if multiple %} multiple {% endif %}
                {% if readonly %} disabled {% endif %}
                class="{% if field_class %}{{field_class }}{% endif %} shadow appearance-none border rounded w-full py-2 px-3 text-gray-800 bg-white leading-tight focus:outline-none focus:shadow-outline"
                {% if field.field.required %} required {% endif %}>

            {% for x, y in field.field.choices %}

                {% if multiple %}
                    <option value="{{ x }}"{% if x in field.value %} selected{% endif %}>{{ y }}</option>
                {% else %}
                    <option value="{{ x }}"{% if field.value == x %} selected{% endif %}>{{ y }}</option>
                {% endif %}
                
            {% endfor %}
        </select>
    </div>
    
    {# Field errors #}
    <div class="text-red-600 text-sm">
        {{ field.errors }}
    </div>

</div>