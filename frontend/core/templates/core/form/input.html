{% load widget_tweaks %}


<div class="fieldWrapper {% if hide == True %}hidden{% endif %}" id="fieldWrapper-{{ field.id_for_label }}">
        <!-- Field Label -->
        <label for="{{ field.id_for_label }}" class="block text-white text-md font-bold mb-2 text-m">
            {{ field.label| safe }}
            <!-- Tooltip -->
            {% if tooltip|default:None %}

            <span class="ml-2">
                {% include 'core/form/tooltip-icon.html' with id=field.id_for_label %}
                <div id="tooltip-{{field.id_for_label}}" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-gray-800 bg-gray-900 rounded-lg opacity-0 transition-opacity duration-300 tooltip">
                    {{ tooltip }}
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </span>
            {% endif %}
        </label>

        <!-- Input field -->
        <div class="flex items-center ">
            {% render_field field class="appearance-none border rounded border-2 w-full py-2 px-3 text-gray-800 leading-tight focus:outline-none focus:shadow-outline" %}
        </div>
</div>