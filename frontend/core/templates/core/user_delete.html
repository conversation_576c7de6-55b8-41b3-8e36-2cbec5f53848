{% extends 'core/base.html' %}

{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}

<div class="flex-1 p-7">
    <div>
        <div id="user-delete" class="w-full md:w-1/2 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Details: {{ user.email }}</h3>
            <div class="p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                <div class="flex flex-col">
                    <h2 class="text-2xl font-medium text-gray-900 dark:text-gray-200">Are you sure you want to delete this user?</h2>
                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-300">Username: <span class="text-sm text-gray-500 dark:text-inherit-light">{{ user.username.username }}</span></p>
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-300">Email: <span class="text-sm text-gray-500 dark:text-inherit-light">{{ user.email }}</span></p>
                    </div>
                    <div class="mt-4">
                        <form action="{% url 'user_delete' user.user_id %}" method="post" class="inline-block">
                            {% csrf_token %}
                            <div class="flex mt-4 align-center justify-center space-x-2">
                            <button type="submit" 
                                class="inline-block px-4 py-2 text-sm font-medium text-white bg-red-600 rounded hover:bg-red-700">
                                Confirm Delete
                            </button>
                            <a href="{% url 'user_detail' user.user_id %}" 
                                class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark">
                                Cancel
                            </a>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}