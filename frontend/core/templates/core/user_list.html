{% extends 'core/base.html' %}

{% load static %}
{% block title %}User Management{% endblock %}
{% block content %}

<div class="flex-1 p-7">
    <div class="grid w-full grid-cols-1 gap-4">
        <div id="response-container" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">All Users</h3>
            <input type="text" id="searchBox" onkeyup="searchUserIDs()" placeholder="Search for user IDs.." class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                <div class="w-full overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="userTable">
                        <thead class="bg-gray-50 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">
                                    User ID
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">
                                    Email
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">
                                    Is Deleted
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">
                                    Edit
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for user in users %}
                            <tr class="bg-white dark:bg-gray-600">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ user.user_id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ user.email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ user.is_deleted }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <a href="{% url 'user_detail' user.user_id %}" class="bg-inherit-yellow rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-yellow-dark hover:bg-inherit-yellow-dark">Edit</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function searchUserIDs() {
        var input, filter, table, tr, td, i, txtValue;
        input = document.getElementById("searchBox");
        filter = input.value.toUpperCase();
        table = document.getElementById("userTable");
        tr = table.getElementsByTagName("tr");
        for (i = 1; i < tr.length; i++) { // Start from 1 to skip the header row
            td = tr[i].getElementsByTagName("td")[0]; // Assuming user_id is in the first column
            if (td) {
                txtValue = td.textContent || td.innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    tr[i].style.display = "";
                } else {
                    tr[i].style.display = "none";
                }
            }
        }
    }
    </script>
{% endblock content %}