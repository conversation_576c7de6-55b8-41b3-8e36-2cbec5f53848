{% extends 'core/base.html' %}

{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}

<div class="flex-1 p-7">
    <div class="">
        <div id="user-details" class="w-1/2 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-300">User Details: {{ user.email }}</h3>
            <div class="p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                <div class="flex flex-col">
                    <div class="grid grid-cols-2 gap-1">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Username:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.username.username }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Email:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.email }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Birth Month:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.birth_month }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Birth Year:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.birth_year }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Sex Assigned at Birth:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.sex_assigned_at_birth }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Reward Points:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.reward_points }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Instagram Follower:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.instagram_follower }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Referral Code:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.referral_code }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Referrer:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.referrer }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Onboarding Page:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.onboarding_page }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Created Date:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.created_date }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Is Deleted:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.is_deleted }}</p>
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-300 text-right">
                            <p>Deleted Date:</p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-inherit-light">
                            <p>{{ user.deleted_date }}</p>
                        </div>
                    </div>
                    <div class="flex mt-4 align-center justify-center space-x-2">
                        <a href="{% url 'user_update' user.user_id %}"
                            class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark">Edit</a>
                        <a href="{% url 'user_delete' user.user_id %}" 
                            class="inline-block px-4 py-2 text-sm font-medium text-inherit-dark hover:text-button-light bg-inherit-yellow hover:bg-button-dark focus:ring-4 focus:outline-none focus:ring-inherit-dark">Delete</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}