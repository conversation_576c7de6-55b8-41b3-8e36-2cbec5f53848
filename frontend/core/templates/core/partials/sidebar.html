<div class="flex-1 px-3 space-y-1 bg-white dark:bg-gray-800">
  <svg 
  id="toggleSidebar"
  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" 
  class="w-8 h-8 text-inherit-dark dark:text-inherit-dark p-1 absolute -right-2 top-6 border dark:border-gray-700 bg-gray-200 dark:bg-gray-200 rounded-full cursor-pointer duration-300">
    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
  </svg>
  

    <ul class="mt-4 pb-2 pr-2 space-y-2">
      <li>
          <a href="{% url 'home' %}" class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
              <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path></svg>
              <span class="ml-3 sidebar-text">Dashboard</span>
          </a>
      </li>
      {% if role == 'STF'%}
      <li>
        <a href="{% url 'user_management' %}" class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
          <i class="fa-solid fa-users text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
            <span class="ml-3 sidebar-text">Users</span>
        </a>
      </li>
      <li>
        <button onclick="toggleSublist('surveys-sublist', this)" class="flex items-center w-full p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
            <i class="fa-solid fa-clipboard-list text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
            <span class="ml-3 sidebar-text pr-2">Surveys</span>
            <i class="fa-solid fa-chevron-right ml-auto pl-4 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white chevron-icon"></i>
        </button>
        <ul id="surveys-sublist" class="hidden ml-6 space-y-2">
            <li>
                <a href="{% url 'feedback' %}" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                    <i class="fa-solid fa-comment-dots text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                    <span class="ml-3">Feedback</span>
                </a>
            </li>
            <!--
            <li>
                <a href="#" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                    <i class="fa-solid fa-plus text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                    <span class="ml-3">Create Survey</span>
                </a>
            </li>
            <li>
              <a href="#" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                  <i class="fa-solid fa-plus text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                  <span class="ml-3">Add Question</span>
              </a>
          </li>
            
            <li>
                <a href="#" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                    <i class="fa-solid fa-edit text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                    <span class="ml-3">Edit Survey</span>
                </a>
            </li>
            -->
        </ul>
      </li>
      {% endif %}
      {% if role == 'STF' or role == 'RES'%}
      <li>
        <button onclick="toggleSublist('research-sublist', this)" class="flex items-center w-full p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
          <i class="fa-solid fa-database text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
          <span class="ml-3 sidebar-text">Research</span>
          <i class="fa-solid fa-chevron-right ml-auto pl-4 text-gray-500 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white chevron-icon"></i>
        </button>
        <ul id="research-sublist" class="hidden ml-6 space-y-2">
            <li>
                <a href="{% url 'research_home' %}" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                    <i class="fa-solid fa-user-graduate text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                    <span class="ml-3">Queries</span>
                </a>
            </li>
            <li>
              <a href="{% url 'create_survey' %}" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                  <i class="fa-solid fa-solid fa-square-poll-vertical text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                  <span class="ml-3">Create Survey</span>
              </a>
            </li>
            <li>
              <a href="{% url 'survey_list' %}" class="flex items-center p-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700">
                  <i class="fa-solid fa-list-ul text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
                  <span class="ml-3">Surveys</span>
              </a>
            </li>
          </ul>
      </li>
      {% endif%}
      {% if role == 'STF' or role == 'SUP'%}
      <li>
        <a href="#" class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
          <i class="fa-solid fa-headset text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
            <span class="ml-3 sidebar-text">Support</span>
        </a>
      </li>
      {% endif%}
      {% if role == 'STF' or role == 'EXP'%}
      <li>
        <a href="{% url 'expert_home' %}" class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
          <i class="fa-solid fa-circle-question text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
            <span class="ml-3 sidebar-text">User Questions</span>
        </a>
      </li>
      {% endif%}
      {% if role == 'STF' or role == 'LED'%}
      <li>
        <a href="#" class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700">
          <i class="fa-solid fa-chart-line text-xl text-gray-500 p-1 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"></i>
            <span class="ml-3 sidebar-text">KPI's</span>
        </a>
      </li>
      {% endif%}
    </ul>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const toggleSidebar = document.getElementById('toggleSidebar');
      const sidebar = document.getElementById('sidebar');
      const sidebarTexts = document.querySelectorAll('.sidebar-text');
    
      toggleSidebar.addEventListener('click', function() {
          if (sidebar.classList.contains('w-64')) {
              sidebar.classList.remove('w-64');
              sidebar.classList.add('w-20');
              toggleSidebar.classList.add('rotate-180');
              sidebarTexts.forEach(text => text.classList.add('hidden'));
          } else {
              sidebar.classList.remove('w-20');
              sidebar.classList.add('w-64');
              toggleSidebar.classList.remove('rotate-180');
              sidebarTexts.forEach(text => text.classList.remove('hidden'));
          }
      });
    });
    function toggleSublist(sublistId, button) {
      const sublist = document.getElementById(sublistId);
      sublist.classList.toggle('hidden');

      // Find the chevron icon within the button
      const chevronIcon = button.querySelector('.chevron-icon');

      // Toggle the chevron icon class
      if (sublist.classList.contains('hidden')) {
          chevronIcon.classList.remove('fa-chevron-down');
          chevronIcon.classList.add('fa-chevron-right');
      } else {
          chevronIcon.classList.remove('fa-chevron-right');
          chevronIcon.classList.add('fa-chevron-down');
      }
    }
    </script>