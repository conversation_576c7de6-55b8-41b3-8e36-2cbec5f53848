from django.db import connections
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
import json
from django.views.generic import DetailView, DeleteView, ListView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from .forms import UserUpdateForm
from .decorators import role_required
from core.psql_models import User
from researchers.views import research_home
import plotly.graph_objects as go
from datetime import datetime
from django.utils import timezone
import logging
from staff.views import staff_home


logger = logging.getLogger("core.views")


@login_required
def home(request):
    role = request.user.role

    if role == 'STF':
        return staff_home(request)

    elif role == 'RES':
        return research_home(request)

    elif role == 'SUP':
        return support_home(request)
    else:
        return leadership_home(request)


@method_decorator(role_required(['STF']), name='dispatch')
class UserListView(ListView, LoginRequiredMixin):
    model = User
    template_name = 'core/user_list.html'
    context_object_name = 'users'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


@method_decorator(role_required(['STF']), name='dispatch')
class UserDetailView(DetailView, LoginRequiredMixin):
    model = User
    template_name = 'core/user_detail.html'
    context_object_name = 'user'
    pk_url_kwarg = 'user_id'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context


@method_decorator(role_required(['STF']), name='dispatch')
class UserUpdateView(UpdateView, LoginRequiredMixin):
    model = User
    form_class = UserUpdateForm
    template_name = 'core/user_update.html'
    context_object_name = 'user'
    pk_url_kwarg = 'user_id'
    success_url = '/user_management/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context

    # manage deleted_date and created_date on form submissions
    def form_valid(self, form):
        user = form.save(commit=False)
        # Preserve the original created_date and deleted_date
        original_user = User.objects.get(pk=user.pk)
        user.created_date = original_user.created_date
        if not user.is_deleted:
            user.deleted_date = original_user.deleted_date
        else:
            user.deleted_date = timezone.now()
        user.save()
        return super().form_valid(form)


@method_decorator(role_required(['STF']), name='dispatch')
class UserDeleteView(DeleteView, LoginRequiredMixin):
    model = User
    fields = []
    template_name = 'core/user_delete.html'
    context_object_name = 'user'
    pk_url_kwarg = 'user_id'
    success_url = '/user_management/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['role'] = self.request.user.role
        return context

    # here we overide the delete method and set is_deleted to true
    def post(self, request, *args, **kwargs):
        user = self.get_object()
        user.is_deleted = True
        user.deleted_date = timezone.now()
        user.save()
        return redirect(self.success_url)


@login_required
@role_required(['LEAD', 'STF'])
def leadership_home(request):
    role = request.user.role
    return render(request, 'core/leadership_home.html', {'role': role})


@login_required
@role_required(['SUP', 'STF'])
def support_home(request):
    role = request.user.role
    # test example of dsiplaying data
    users = User.objects.using("backend").all()
    # Prepare data for Plotly table
    header = dict(
        values=['Username', 'Email', 'Reward Points', 'Birth Month'],
        fill_color='paleturquoise',
        align='left',
    )
    cells = dict(
        values=[
            [user.username for user in users],
            [user.email for user in users],
            [user.reward_points for user in users],
            [user.birth_month for user in users]
        ],
        fill_color='lavender',
        align='left'
    )

    fig = go.Figure(data=[go.Table(header=header, cells=cells)])

    table_html = fig.to_html(full_html=False)

    return render(request, 'core/support_home.html', {'role': role, 'table_html': table_html})


@login_required
@role_required(['RES', 'STF'])
def download_results(request):
    sql_query = request.GET.get('sql_query')
    if sql_query:
        try:
            with connections['backend'].cursor() as cursor:
                cursor.execute(sql_query)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

                # Convert datetime objects to strings and structure the data
                def convert_to_serializable(value):
                    if isinstance(value, datetime):
                        return value.isoformat()
                    return value

                data = [
                    {columns[i]: convert_to_serializable(cell) for i, cell in enumerate(row)}for row in rows
                ]
                response = HttpResponse(json.dumps(data, indent=4), content_type='application/json')
                response['Content-Disposition'] = 'attachment; filename="query_results.json"'
                return response
        except Exception as e:
            return HttpResponse(f"An error occurred: {e}", status=500)
    else:
        return HttpResponse("No SQL query submitted", status=400)
