# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class User(models.Model):
    user_id = models.TextField(primary_key=True)
    sub = models.TextField(unique=True)
    username = models.TextField(unique=True)
    email = models.TextField(unique=True)
    birth_month = models.IntegerField()
    birth_year = models.IntegerField()
    sex_assigned_at_birth = models.TextField()  # This field type is a guess.
    reward_points = models.IntegerField()
    reward_point_awarded_for_instagram = models.BooleanField()
    reward_point_awarded_for_personal_health_survey = models.BooleanField()
    reward_point_awarded_for_reproductive_health_survey = models.Bo<PERSON>anField()
    reward_point_awarded_for_lifestyle_survey = models.<PERSON><PERSON>an<PERSON>ield()
    reward_point_awarded_for_sharing = models.BooleanField()
    reward_point_awarded_to_referrer = models.BooleanField()
    referral_code = models.TextField(unique=True)
    referrer = models.TextField(blank=True, null=True)
    instagram_handle = models.TextField(blank=True, null=True)
    impact_points = models.IntegerField()
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user'


class Username(models.Model):
    username = models.TextField(primary_key=True)
    referral_code = models.TextField(unique=True)
    assigned = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'username'


class UserAppointmentSupportRequest(models.Model):
    request_id = models.TextField(primary_key=True)
    user = models.ForeignKey(User, models.DO_NOTHING)
    response_output = models.TextField()
    appointment_type = models.TextField()
    appointment_details = models.TextField(blank=True, null=True)
    rating = models.IntegerField(blank=True, null=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_appointment_support_request'


class UserHealthEvent(models.Model):
    event_id = models.TextField(primary_key=True)
    user = models.ForeignKey(User, models.DO_NOTHING)
    type = models.TextField()  # This field type is a guess.
    details = models.TextField()
    notes = models.JSONField(blank=True, null=True)
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    ongoing = models.BooleanField()
    genetic = models.BooleanField()
    is_reviewed = models.BooleanField()
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_health_event'


class UserScreeningAlert(models.Model):
    alert_id = models.TextField(primary_key=True)
    user = models.ForeignKey(User, models.DO_NOTHING)
    type = models.TextField()  # This field type is a guess.
    subtype = models.TextField(blank=True, null=True)
    next_date = models.DateField(blank=True, null=True)
    suggested_months_between_appointments = models.IntegerField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    status = models.TextField()  # This field type is a guess.
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_screening_alert'
        unique_together = (('user', 'type', 'subtype', 'status'),)


class UserScreening(models.Model):
    screening_id = models.TextField(primary_key=True)
    user = models.ForeignKey(User, models.DO_NOTHING)
    type = models.TextField()  # This field type is a guess.
    subtype = models.TextField(blank=True, null=True)
    next_date = models.DateField(blank=True, null=True)
    last_date = models.DateField(blank=True, null=True)
    attended_date = models.DateField(blank=True, null=True)
    months_between_appointments = models.IntegerField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    status = models.TextField()  # This field type is a guess.
    user_managed_schedule = models.BooleanField()
    alert = models.ForeignKey(UserScreeningAlert, models.DO_NOTHING, blank=True, null=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_screening'
        unique_together = (('user', 'type', 'subtype'),)


class UserShare(models.Model):
    share_id = models.TextField(primary_key=True)
    sharer = models.ForeignKey(User, models.DO_NOTHING)
    sharee = models.ForeignKey(User, models.DO_NOTHING, related_name='usershare_sharee_set')
    sharee_relationship_to_sharer = models.TextField()  # This field type is a guess.
    label_for_sharer = models.TextField()
    label_for_sharee = models.TextField()
    approved = models.BooleanField()
    approved_date = models.DateTimeField(blank=True, null=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_share'
        unique_together = (('sharer', 'sharee'),)


class Expert(models.Model):
    expert_id = models.TextField(primary_key=True)
    sub = models.TextField(unique=True)
    name = models.TextField()
    qualifications = models.TextField()
    email = models.TextField(unique=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'expert'


class ExpertAnswerTag(models.Model):
    tag_id = models.TextField(primary_key=True)
    expert = models.ForeignKey(Expert, models.DO_NOTHING)
    tag_name = models.TextField()
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'expert_answer_tag'


class ExpertAnswer(models.Model):
    answer_id = models.TextField(primary_key=True)
    expert = models.ForeignKey(Expert, models.DO_NOTHING)
    question_text = models.TextField()
    answer_text = models.TextField()
    parent_answer = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True)
    position_in_tree = models.IntegerField()
    answer_tag = models.ForeignKey(ExpertAnswerTag, models.DO_NOTHING, db_column='answer_tag', blank=True, null=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'expert_answer'


class UserQuestion(models.Model):
    question_id = models.TextField(primary_key=True)
    user = models.ForeignKey(User, models.DO_NOTHING)
    question_text = models.TextField()
    moderation_status = models.TextField()  # This field type is a guess.
    votes = models.IntegerField()
    answer = models.ForeignKey(ExpertAnswer, models.DO_NOTHING, blank=True, null=True)
    created_date = models.DateTimeField()
    is_deleted = models.BooleanField()
    deleted_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_question'


class DailyUserCount(models.Model):
    date_id = models.TextField(primary_key=True)
    date = models.DateField()
    d_new_user_count = models.IntegerField()
    d_total_user_count = models.IntegerField()
    d_growth_rate = models.FloatField()
    wk_new_user_count = models.IntegerField()
    wk_growth_rate = models.FloatField()
    wk_total_user_count = models.IntegerField()
    mth_new_user_count = models.IntegerField()
    mth_total_user_count = models.IntegerField()
    mth_growth_rate = models.FloatField()
    created_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'daily_user_count'
