document.addEventListener('DOMContentLoaded', function() {
    const theme = localStorage.getItem('color-theme');
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)').matches;

    const themeLogo = document.getElementById('theme-logo');
    if (themeLogo) {
        if (theme === 'dark' || (!theme && prefersDarkScheme)) {
            console.log('Applying dark theme');
            document.documentElement.classList.add('dark');
            themeLogo.src = "/static/images/dark word logo.png";
        } else {
            console.log('Applying light theme');
            document.documentElement.classList.remove('dark');
            themeLogo.src = "/static/images/3.png";
        }
    }
});

function toggleTheme() {
    const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    const themeLogo = document.getElementById('theme-logo');
    if (currentTheme === 'dark') {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('color-theme', 'light');
        if (themeLogo) {
            themeLogo.src = "/static/images/3.png";
        }
    } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('color-theme', 'dark');
        if (themeLogo) {
            themeLogo.src = "/static/images/dark word logo.png";
        }
    }
}