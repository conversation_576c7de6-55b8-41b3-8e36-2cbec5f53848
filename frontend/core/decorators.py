from django.shortcuts import redirect
from functools import wraps
from django.core.exceptions import PermissionDenied


def role_required(required_roles):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.role not in required_roles:
                return redirect('home')
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


class RoleRequiredMixin:
    """
    Mixin to restrict access to users with specific roles.
    """
    allowed_roles = []

    def dispatch(self, request, *args, **kwargs):
        # Check if the user's role is in the allowed roles
        if not hasattr(request.user, 'role') or request.user.role not in self.allowed_roles:
            raise PermissionDenied("You do not have permission to access this page.")
        return super().dispatch(request, *args, **kwargs)
