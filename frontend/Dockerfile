FROM python:3.12-slim
WORKDIR /app
RUN apt-get update && apt-get install -y nginx && apt-get clean
COPY nginx.conf /etc/nginx/nginx.conf
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . frontend
ENV DJANGO_SETTINGS_MODULE=inherit.docker_settings
EXPOSE 80
CMD ["/app/frontend/entrypoint.sh", "uvicorn", "inherit.asgi:application", "--app-dir=frontend", "--log-config=frontend/uvicorn_log_config.yaml", "--host=127.0.0.1", "--port=8001"]
#CMD ["/app/frontend/entrypoint.sh", "uvicorn", "inherit.asgi:application", "--app-dir=frontend", "--host=127.0.0.1", "--port=8001"]
