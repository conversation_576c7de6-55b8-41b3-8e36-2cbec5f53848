from django import forms
from .models import Survey, Question
from django.forms import modelformset_factory


class SurveyForm(forms.ModelForm):
    class Meta:
        model = Survey
        fields = ['title', 'description']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(
                attrs={
                    'class': 'form-control',
                    'rows': 3,
                }),
        }
        labels = {
            'title': 'Survey Title',
            'description': 'Survey Description',
        }


class QuestionForm(forms.ModelForm):
    class Meta:
        model = Question
        exclude = ['id']
        widgets = {
            'type': forms.Select(attrs={'class': 'type-field'}),
            'required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            "options": forms.Textarea(
                attrs={
                    'class': 'form-control',
                    'rows': 3,
                    'placeholder': 'Enter options on a new line',  # Initially hide the options field
                }),
        }
        labels = {
            'required': 'Is Required',
            'conditional_answer': 'Conditional Value',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        # question = cleaned_data.get('question')
        question_type = cleaned_data.get('type')
        options = cleaned_data.get('options')

        # Validate that options are provided for multiple choice questions
        if question_type in ['multiple_choice', 'checkbox', 'radio'] and not options:
            self.add_error('options', 'Options are required for multiple choice questions.')

        if question_type in ['multiple_choice', 'checkbox', 'radio'] and options:
            # Split options by new line and strip whitespace and join with semi-colon
            options = [option.strip() for option in options.split('\n')]
            options = ';'.join(options)
            cleaned_data['options'] = options

        return cleaned_data


QuestionFormSet = modelformset_factory(
    Question,
    form=QuestionForm,
    extra=1,  # Start with one empty form
)
