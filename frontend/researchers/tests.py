
from django.test import TestCase
from .models import Survey, Question, Answer, SurveyResponse, CustomUser
import json


class SurveyTestCase(TestCase):
    def setUp(self):
        # Create a test user
        self.user = CustomUser.objects.create_user(email="<EMAIL>", password="password123")

    def test_create_survey_with_all_question_types(self):
        # Define the question types
        question_types = [
            ('text', 'Text'),
            ('multiple_choice', 'Multiple Choice'),
            ('checkbox', 'Checkbox'),
            ('radio', 'Radio'),
            ('number', 'Number'),
            ('date', 'Date'),
            ('email', 'Email'),
            ('url', 'URL'),
            ('textarea', 'Textarea')
        ]

        # Create a survey
        survey = Survey.objects.create(
            title="Test Survey",
            description="A survey with one question for each type.",
            user=self.user
        )

        # Create one question for each type and add it to the survey
        for type_choice, _ in question_types:
            question = Question.objects.create(
                question=f"Sample question for {type_choice}",
                required=True,
                type=type_choice,
                options="Option 1, Option 2" if type_choice in ['multiple_choice', 'checkbox', 'radio'] else None
            )
            survey.questions.add(question)

        # Assert the survey has the correct number of questions
        self.assertEqual(survey.questions.count(), len(question_types))

        # Assert each question type is present in the survey
        for type_choice, _ in question_types:
            self.assertTrue(
                survey.questions.filter(type=type_choice).exists(),
                f"Question of type {type_choice} not found in the survey."
            )


class SurveyResponseTestCase(TestCase):
    def setUp(self):
        # Create a test user
        self.user = CustomUser.objects.create_user(email="<EMAIL>", password="password123")

        # Create a survey
        self.survey = Survey.objects.create(
            title="Test Survey",
            description="A survey with one question for each type.",
            user=self.user
        )

        # Define the question types
        self.question_types = [
            ('text', 'Text'),
            ('multiple_choice', 'Multiple Choice'),
            ('checkbox', 'Checkbox'),
            ('radio', 'Radio'),
            ('number', 'Number'),
            ('date', 'Date'),
            ('email', 'Email'),
            ('url', 'URL'),
            ('textarea', 'Textarea')
        ]

        # Create one question for each type and add it to the survey
        for type_choice, _ in self.question_types:
            question = Question.objects.create(
                question=f"Sample question for {type_choice}",
                required=True,
                type=type_choice,
                options="Option 1, Option 2" if type_choice in ['multiple_choice', 'checkbox', 'radio'] else None
            )
            self.survey.questions.add(question)

    def test_provide_survey_response(self):
        # Simulate user responses for each question

        answers = []
        for question in self.survey.questions.all():
            response_text = "Sample response"
            if question.type == "multiple_choice":
                response_text = "Option 1"
            elif question.type == "checkbox":
                response_text = "Option 1, Option 2"
            elif question.type == "number":
                response_text = "42"
            elif question.type == "date":
                response_text = "2023-01-01"
            elif question.type == "email":
                response_text = "<EMAIL>"
            elif question.type == "url":
                response_text = "https://example.com"
            elif question.type == "textarea":
                response_text = "This is a longer response."

            answer = Answer.objects.create(
                question=question,
                response=response_text,
                user=self.user
            )
            answers.append(answer)

        answer_text = [answer.response for answer in answers]

        json_str = json.dumps(answer_text)
        # Create a SurveyResponse object
        survey_response = SurveyResponse.objects.create(
            survey=self.survey,
            user=self.user,
            response=json_str
        )

        # Assert the SurveyResponse is saved correctly
        self.assertEqual(survey_response.response, json_str)
