import openai
from django.conf import settings
import logging


client = openai.AzureOpenAI(
    azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version="2024-10-21",
)


def gpt_response(self, messages):
    """
    Conduct a query to GPT using the current field and user input.
    Args:
        messages (list): The conversation history.
        current_field (dict): The current field being processed.
        user_input (str): The user's input for the current field (if any).
    Returns:
        dict: A dictionary containing the GPT response or an error message.
    """

    try:

        # Send the conversation to GPT
        response = client.chat.completions.create(
            model=settings.AZURE_OPENAI_MODEL,
            messages=messages,
            max_tokens=800,
            temperature=0.7,
            top_p=0.95,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None,
            stream=False,
        )
        # Extract GPT's response
        gpt_reply = response.choices[0].message.content.strip()
        # print(f"GPT Reply: {gpt_reply}")
        # print(f"Type of gpt_reply: {type(gpt_reply)}")
        return {"status": "success", "response": gpt_reply}

    except Exception as e:
        # Handle errors (e.g., API issues)
        return {
            "status": "error",
            "message": f"An error occurred while interacting with GPT: {str(e)}",
        }


def get_gpt_response(selected_fields):
    """
    Conduct a query to GPT using the selected fields from the form.
    Args:
        selected_fields (list): A list of dictionaries containing field details.
    """
    # Prepare the prompt for GPT
    system_message = (
        "You are a helpful assistant designed to collect health-related information. "
        "You will ask the user questions about the following fields only:\n"
        + "\n".join([f"- {field['verbose_name']} (Type: {field['type']})" for field in selected_fields]) +  # noqa: W503,W504
        "\nEnsure the answers are in the correct format and type for each field. "
        "Do not ask about any fields that are not listed above."
    )

    fields_to_ask = [
        {
            "name": field["name"],
            "type": field["type"],
            "verbose_name": field["verbose_name"],
            "answered": False,
            "answer": None,
        }
        for field in selected_fields
    ]

    # Initialize the conversation
    messages = [{"role": "system", "content": system_message}]
    completed_fields = []

    try:
        while len(completed_fields) < len(fields_to_ask):
            # Find the next unanswered field
            current_field = next(field for field in fields_to_ask if not field["answered"])

            # Generate a question for the current field
            question = (
                f"{current_field['verbose_name']} "
                f"(Type: {current_field['type']})."
            )
            messages.append({"role": "assistant", "content": question})

            # Simulate user input (replace this with actual user input in a real application)
            user_input = input(f"{question}\n")  # Replace with frontend integration
            messages.append({"role": "user", "content": user_input})

            # Validate the user's response
            validation_prompt = (
                f"The user provided the following answer for the field '{current_field['verbose_name']}' "
                f"(Type: {current_field['type']}): '{user_input}'. "
                f"Is this answer valid? If not, explain why and ask the user to provide a valid answer."
            )
            messages.append({"role": "assistant", "content": validation_prompt})

            # Send the conversation to GPT
            response = client.chat.completions.create(
                model="nonproduction-openai-gpt-35-turbo",
                messages=messages,
                max_tokens=800,
                temperature=0.7,
                top_p=0.95,
                frequency_penalty=0,
                presence_penalty=0,
                stop=None,
                stream=False,
            )

            # Extract GPT's response
            gpt_reply = response.choices[0].message.content.strip()
            # logging.info(f"GPT Reply: {gpt_reply}")
            # logging.info(f'Current field: {current_field["verbose_name"]}')

            # Check if GPT acknowledged the field
            if "thank you" in gpt_reply.lower() or "acknowledged" in gpt_reply.lower():
                # Mark the field as answered
                current_field["answered"] = True
                current_field["answer"] = user_input
                completed_fields.append(current_field)
            elif current_field["verbose_name"].lower() in gpt_reply.lower():
                # GPT is still asking about the current field
                logging.info(f"GPT is still asking about the current field: {current_field['verbose_name']}")
            else:
                # GPT generated an unrelated response
                logging.info(f"GPT generated an unrelated response: {gpt_reply}")
                gpt_reply = f"{current_field['verbose_name']} (Type: {current_field['type']})."

        # Return the completed fields with answers
        return {
            "status": "success",
            "completed_fields": completed_fields,
        }

    except Exception as e:
        # Handle errors (e.g., API issues)
        return {
            "status": "error",
            "message": f"An error occurred while interacting with GPT: {str(e)}",
        }
