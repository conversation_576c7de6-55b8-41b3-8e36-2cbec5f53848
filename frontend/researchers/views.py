import re
import json
from django.db import connections
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from core.decorators import role_required, RoleRequiredMixin
from core.role import RoleListView, RoleView, RoleDetailView
from .utils import ALLOWED_TABLES
from .forms import SurveyForm, QuestionFormSet
from .question_prompt import gpt_response
from .models import Survey, Question, SurveyResponse
from .prompt import System_Prompt
import logging


@login_required
@role_required(['RES', 'STF'])
def research_home(request):
    role = request.user.role
    query_results = None

    if request.method == 'POST':
        sql_query = request.POST.get('sql_query')
        if sql_query:
            # Check if the query is a SELECT statement
            if not sql_query.strip().lower().startswith('select'):
                query_error = 'Only SELECT queries are allowed'
                return render(request, 'researchers/research_home.html', {'role': role, 'query_error': query_error})

            # Extract table names from the query
            table_names = re.findall(r'from\s+["]?(\w+)["]?', sql_query, re.IGNORECASE)

            # Check if all table names are in the allowed list
            if not all(table in ALLOWED_TABLES for table in table_names):
                query_error = 'Query not allowed on the specified tables'
                return render(request, 'researchers/research_home.html', {'role': role, 'query_error': query_error})

            try:
                # Explicitly use the 'backend' database connection
                with connections['backend'].cursor() as cursor:
                    cursor.execute(sql_query)
                    columns = [col[0] for col in cursor.description]
                    rows = cursor.fetchall()
                    query_results = {
                        'columns': columns,
                        'rows': rows
                    }
            except Exception as e:
                return HttpResponse(f"An error occurred: {e}", status=500)
        else:
            return HttpResponse("No query provided", status=400)
    return render(request, 'researchers/research_home.html', {'role': role, 'query_results': query_results})


class CreateSurveyView(LoginRequiredMixin, RoleRequiredMixin, RoleView):
    template_name = 'researchers/create_survey.html'
    allowed_roles = ['RES', 'STF']

    def get(self, request, *args, **kwargs):
        survey_form = SurveyForm()
        question_formset = QuestionFormSet(queryset=Question.objects.none())
        context = self.get_context_data(
            survey_form=survey_form,
            question_formset=question_formset,
        )
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        survey_form = SurveyForm(request.POST)
        question_formset = QuestionFormSet(request.POST, queryset=Question.objects.none())

        if survey_form.is_valid() and question_formset.is_valid():
            # Save the survey
            user = request.user
            survey = survey_form.save(commit=False)
            survey.user = user
            survey.save()

            # Save the questions and link them to the survey
            for question_form in question_formset:
                if question_form.cleaned_data:  # Skip empty forms
                    question = question_form.save(commit=False)
                    question.save()
                    survey.questions.add(question)

            return redirect('survey_list')

        # If the form is invalid, re-render the page with the forms
        return render(request, self.template_name, {
            'survey_form': survey_form,
            'question_formset': question_formset,
        })


class SurveyListView(LoginRequiredMixin, RoleRequiredMixin, RoleListView):
    model = Survey
    template_name = 'researchers/survey_list.html'
    context_object_name = 'surveys'
    allowed_roles = ['RES', 'STF']

    def get_queryset(self):
        # Filter surveys based on the logged-in user
        surveys = super().get_queryset()

        # get count of questions for each survey
        for survey in surveys:
            survey.question_count = survey.questions.count()

        # get count of responses
        for survey in surveys:
            survey.response_count = survey.surveyresponse_set.count()
        return surveys


class SurveyResponseView(LoginRequiredMixin, RoleRequiredMixin, RoleView):
    model = Survey
    template_name = 'researchers/survey_response.html'
    context_object_name = 'survey'
    gpt_response = gpt_response
    allowed_roles = ['RES', 'STF']

    def get(self, request, *args, **kwargs):
        survey_id = kwargs.get('pk')
        survey = Survey.objects.get(id=survey_id)
        questions = survey.questions.all()

        survey_questions = []
        for question in questions:
            question_data = {
                'id': question.id,
                'question': question.question,
                'type': question.type,
                'options': question.options.split('\n') if question.options else [],
            }

            survey_questions.append(question_data)

        # Store the survey questions in the session
        request.session['survey_questions'] = survey_questions
        # Format the Sys_Prompt with the fields_list
        formatted_prompt = System_Prompt.format(fields_list=survey_questions)

        # Initialize conversation state
        conversation = {
            "messages": [{"role": "system", "content": formatted_prompt}],
            "completed_fields": [],
            "survey_questions": survey_questions,
        }

        response = self.gpt_response(conversation["messages"])
        gpt_reply = response.get("response", "An error occurred while interacting with GPT.")
        logging.info(f"GPT Reply: {gpt_reply}")
        context = self.get_context_data(
            survey_id=survey_id,
            survey=survey,
            survey_questions=survey_questions,
            gpt_reply=gpt_reply
        )
        conversation["messages"].append({"role": "assistant", "content": gpt_reply})
        request.session["conversation"] = conversation

        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        # Handle user input and generate GPT response
        user_message = request.POST.get('message')

        # Retrieve conversation state from the session
        conversation = request.session.get("conversation", {
            "messages": [{"role": "system", "content": System_Prompt}],
        })

        if not user_message:
            return JsonResponse({'error': 'No message provided'}, status=400)

        conversation["messages"].append({"role": "user", "content": user_message})
        # print(f"Conversation Messages: {conversation['messages']}")
        response = self.gpt_response(conversation["messages"])
        gpt_reply = response.get("response", "An error occurred while interacting with GPT.")
        # print(f"GPT Reply: {gpt_reply}")
        # Add GPT's reply to the conversation
        conversation["messages"].append({"role": "assistant", "content": gpt_reply})
        # Save the updated conversation state in the session
        request.session["conversation"] = conversation
        # Check if the conversation is complete
        if "Survey Complete" in gpt_reply:
            # Extract JSON from GPT reply
            json_data = None
            try:
                # Use regex to extract the JSON block between triple backticks
                match = re.search(r'```json\s*(.*?)\s*```', gpt_reply, re.DOTALL)
                if match:
                    json_string = match.group(1)  # Extract the JSON string
                    json_data = json.loads(json_string)  # Parse the JSON string into a Python object

                    # Save the JSON data
                    SurveyResponse.objects.create(
                        survey=Survey.objects.get(id=kwargs.get('pk')),
                        response=json_data,
                        user=request.user
                    )

                    # fix response for better UI
                    return JsonResponse({'completion_status': 'success', 'response': response})
                else:
                    return JsonResponse({'error': 'No JSON data found in GPT reply.'}, status=400)
            except json.JSONDecodeError as e:
                return JsonResponse({'error': f'Failed to parse JSON: {str(e)}'}, status=400)
        return JsonResponse({'response': response})


class SurveyResponseList(LoginRequiredMixin, RoleRequiredMixin, RoleListView):
    model = SurveyResponse
    template_name = 'researchers/survey_response_list.html'
    context_object_name = 'survey_responses'
    allowed_roles = ['RES', 'STF']


class SurveyResponseDetail(LoginRequiredMixin, RoleRequiredMixin, RoleDetailView):
    model = SurveyResponse
    template_name = 'researchers/survey_response_detail.html'
    context_object_name = 'survey_response'
    allowed_roles = ['RES', 'STF']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['survey_response'] = self.get_object()
        context['response_data'] = context['survey_response'].response
        return context
