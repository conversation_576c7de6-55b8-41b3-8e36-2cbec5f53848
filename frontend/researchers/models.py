from django.db import models
from users.models import CustomUser


class Question(models.Model):
    question = models.CharField(max_length=255)
    required = models.BooleanField(default=False)
    type = models.CharField(max_length=50, choices=[
        ('text', 'Text'),
        ('multiple_choice', 'Multiple Choice'),
        ('checkbox', 'Checkbox'),
        ('radio', 'Radio'),
        ('number', 'Number'),
        ('date', 'Date'),
        ('email', 'Email'),
        ('url', 'URL'),
        ('textarea', 'Textarea')
    ])
    options = models.TextField(null=True, blank=True)
    is_conditional = models.BooleanField(default=False)
    parent_question = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL, related_name='conditional_questions')
    conditional_answer = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.question


class Answer(models.Model):
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    response = models.TextField()
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.question} - {self.response}"


class Survey(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    questions = models.ManyToManyField(Question, related_name='surveys')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title


class SurveyResponse(models.Model):
    survey = models.ForeignKey(Survey, on_delete=models.CASCADE)
    response = models.JSONField()
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
