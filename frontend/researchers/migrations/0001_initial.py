# Generated by Django 5.1.6 on 2025-04-09 12:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="QuestionTest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dob",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date of Birth"
                    ),
                ),
                (
                    "ethnicity",
                    models.CharField(
                        choices=[
                            ("White", "White"),
                            ("Black", "Black"),
                            ("Asian", "Asian"),
                            ("Mixed", "Mixed"),
                            ("Other", "Other"),
                            ("Prefer not to say", "Prefer not to say"),
                        ],
                        max_length=50,
                        null=True,
                        verbose_name="What is your ethnicity?",
                    ),
                ),
                (
                    "conditions",
                    models.Char<PERSON>ield(
                        choices=[
                            ("Diabetes", "Diabetes"),
                            ("Heart Disease", "Heart Disease"),
                            ("Hypertension", "Hypertension"),
                            ("Asthma", "Asthma"),
                            ("Mental Health Condition", "Mental Health Condition"),
                            ("None of the above", "None of the above"),
                        ],
                        max_length=50,
                        null=True,
                        verbose_name="Do you have any of the following conditions?",
                    ),
                ),
                (
                    "major_treatment",
                    models.<PERSON>oleanField(
                        blank=True,
                        null=True,
                        verbose_name="Have you ever had any major treatment? (e.g., surgery, medication, hospitalisation)",
                    ),
                ),
                (
                    "treatment_history",
                    models.TextField(
                        blank=True,
                        null=True,
                        verbose_name="Please provide details of your treatment history",
                    ),
                ),
                (
                    "vaccinations",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("COVID-19", "COVID-19"),
                            ("Flu", "Flu"),
                            ("Hepatitis B", "Hepatitis B"),
                            ("HPV", "HPV"),
                            ("None", "None"),
                        ],
                        null=True,
                        verbose_name="Which vaccinations have you received?",
                    ),
                ),
                (
                    "sleep",
                    models.TextField(
                        max_length=50,
                        null=True,
                        verbose_name="How many hours of sleep do you get on average per night?",
                    ),
                ),
                (
                    "review",
                    models.CharField(
                        choices=[("Yes", "Yes"), ("No", "No")],
                        max_length=50,
                        null=True,
                        verbose_name="Please review your responses below. Do you want to make any changes before submission?",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
