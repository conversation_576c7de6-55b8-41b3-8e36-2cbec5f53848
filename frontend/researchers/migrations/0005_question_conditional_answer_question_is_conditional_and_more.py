# Generated by Django 5.1.6 on 2025-05-08 08:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("researchers", "0004_delete_questiontest"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="question",
            name="conditional_answer",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="question",
            name="is_conditional",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="question",
            name="parent_question",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="conditional_questions",
                to="researchers.question",
            ),
        ),
    ]
