{% extends 'core/base.html' %}

{% load static %}
{% block title %}Response Detail{% endblock %}
{% block content %}
<div class="flex">
    <!-- Main Dash-->
    <div class="flex-1 p-7">
        <div class="w-full gap-4">
            <div id="question-container" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Response Details:</h3>

                <div class="pt-2 px-10 bg-gray-100 border border-gray-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    {% for item in response_data %}
                    <div class="border-b">
                        <p class="p-2 text-sm text-gray-900 dark:text-white"><span class="text-inherit-dark">Id: </span>{{ item.id }}</p>
                        <p class="p-2 text-sm text-gray-900 dark:text-white"><span class="text-inherit-dark">Question:</span> {{ item.question }}</p>
                        <p class="p-2 text-sm text-gray-900 dark:text-white"><span class="text-inherit-dark">Answer:</span> {{ item.answer }}</p>
                    </div>
                    {% endfor %}
                </div>  
            </div>
        </div>
    </div>
</div>
{% endblock %}
