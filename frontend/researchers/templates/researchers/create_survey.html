{% extends "core/base.html" %}
{% load widget_tweaks %}
{% load crispy_forms_tags %}
{% load tailwind_filters %}
{% load static %}
{% block title %}Question Test{% endblock %}
{% block content %}
<div class="container mx-auto w-4/5 mt-4 p-2">
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
        <div class="flex flex-col items-center justify-center">
            <h1 class="text-2xl text-gray-700 dark:text-white font-semi-bold mb-4">Create A Survey</h1>
            <form method="post" class="flex flex-col w-full items-center justify-center">
                {% csrf_token %}
                <div class="flex flex-col w-full items-center justify-center">
                    <div class="mb-4 w-3/4" id="field-title">
                        {% if survey_form.title%}
                            {% include 'core/form/input.html' with field=survey_form.title %}
                        {% endif %}
                    </div>
                    <div class="mb-4 w-3/4" id="field-description">
                        {% if survey_form.description%}
                            {% include 'core/form/input.html' with field=survey_form.description %}
                        {% endif %}
                    </div>
                </div>
                <div class="flex flex-col w-full items-center justify-center mt-4">
                    <h2 class="text-2xl text-gray-700 dark:text-white font-semi-bold">Add Your Questions</h2>
                    <div id="question-formset" class="flex flex-col w-full items-center justify-center">
                        {{ question_formset.management_form }}
                        {% for form in question_formset %}
                            <div class="question-form flex flex-col w-3/4 border border-2 p-2 mb-4 items-center justify-center">
                                <div class="flex flex-col w-full items-center justify-center">
                                    {% if form.question%}
                                    <div class="mb-4 w-full" id="field-question">
                                            {% include 'core/form/input.html' with field=form.question %}    
                                    </div>
                                    {% endif %}
                                </div> 
                                <div class="flex flex-row w-full justify-between">
                                    <div class="flex w-full space-x-auto items-start justify-start">
                                        {% if form.type %}
                                            <div id="type-field" >
                                                {% include 'core/form/select.html' with field=form.type %}
    
                                                <div id="field-options" class="pt-4 mb-4 hidden">
                                                    {% include 'core/form/input.html' with field=form.options %}
                                                </div>
    
                                                 
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex flex-col w-full mt-2 space-x-auto items-start justify-start">
                                        <div class="pt-4 mb-4">
                                            {% include 'core/form/checkbox.html' with field=form.required %} 
                                        </div>
                                        {% if form.is_conditional %}
                                            <div id="conditional-field" class="pt-4 mb-4">
                                                {% include 'core/form/checkbox.html' with field=form.is_conditional %} 
                                            </div>
                                        {% endif %}
                                        {% if form.conditional_answer %}
                                            <div id="field-conditional-answer" class="pt-2 mb-2 hidden">
                                                {% include 'core/form/input.html' with field=form.conditional_answer %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                </div>
                                <div class="flex flex-col w-full items-start justify-start mt-4">       
                                    <button 
                                        type="button"
                                        class="inline-block px-4 py-2 text-sm font-medium text-white rounded bg-inherit-yellow hover:bg-inherit-yellow-dark focus:ring-4 focus:outline-none focus:ring-inherit-yellow"
                                    >Remove
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="flex flex-col w-3/4 items-start justify-start mt-4">
                        <button type="button"
                            id="add-question"
                            class="inline-block px-4 py-2 text-sm font-medium text-white rounded bg-inherit-green hover:bg-inherit-green-dark focus:ring-4 focus:outline-none focus:ring-inherit-green"
                            >Add Question</button>
                    </div>
                </div>
                <div class="flex flex-col w-3/4 items-start justify-start mt-4">
                    <button 
                    type="submit"
                    class="inline-block px-5 py-2 text-sm font-medium text-white rounded hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark"
                    >Save Survey</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js" ></script>
<script>
    // This script handles the dynamic addition and removal of question forms in the formset
    $(document).ready(function() {
        let formsetDiv = $('#question-formset');
        let totalForms = $('#id_form-TOTAL_FORMS');

        $('#add-question').click(function() {
            let newFormIndex = totalForms.val();
            let newFormHtml = formsetDiv.children('.question-form:first').clone();
            newFormHtml.find(':input[name]').each(function() {
                let name = $(this).attr('name').replace('-0-', `-${newFormIndex}-`);
                let id = $(this).attr('id').replace('-0-', `-${newFormIndex}-`);
                $(this).attr({'name': name, 'id': id}).val('');
            });
            newFormHtml.find('#field-options').addClass('hidden'); // Hide choices field
            newFormHtml.find('#field-conditional-answer').addClass('hidden');
            newFormHtml.find('.remove-question').show();
            formsetDiv.append(newFormHtml);
            totalForms.val(parseInt(newFormIndex) + 1);
        });

        $(document).on('click', '.remove-question', function() {
            $(this).closest('.question-form').remove();
        });

        // Delegate event listener for type field changes
        $(document).on('change', '#type-field select', function() {
            let selectedValue = $(this).val();
            let optionsField = $(this).closest('.question-form').find('#field-options');
            if (selectedValue === 'multiple_choice' || selectedValue === 'checkbox' || selectedValue === 'radio') {
                optionsField.removeClass('hidden');
            } else {
                optionsField.addClass('hidden');
            }
        });

        // Delegate event listener for conditional checkbox changes
        $(document).on('change', '#conditional-field input[type="checkbox"]', function() {
            let conditionalAnswerField = $(this).closest('.question-form').find('#field-conditional-answer');
            if ($(this).is(':checked')) {
                conditionalAnswerField.removeClass('hidden');
            } else {
                conditionalAnswerField.addClass('hidden');
            }
        });

        
    });
</script>
{% endblock %}