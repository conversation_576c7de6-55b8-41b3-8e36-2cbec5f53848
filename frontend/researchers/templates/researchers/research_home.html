<!-- filepath: /Users/<USER>/Desktop/Kinnelhead/Inherit/Project/SRC/inherit/frontend/inherit/core/templates/core/research_home.html -->
{% extends 'core/base.html' %}

{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="flex">

  <!-- Main Dash-->
  <div class="flex-1 p-7">
    <div class="grid w-full grid-cols-1 gap-4">
      <!-- SQL Command Container -->
      <div class="col-span-1 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">SQL Command</h3>
        <form method="POST" action="{% url 'research_home' %}">
          {% csrf_token %}
          <textarea name="sql_query" class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" rows="6" placeholder="Enter your SQL command here..."></textarea>
          <button id="execute-command" type="submit" class="px-4 py-2 mt-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600">Execute</button>
        </form>
      </div>
      <!-- Response Container -->
      <div id="response-container" class="col-span-1 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Response</h3>
        <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
          <!-- Response will be displayed here -->
           {% if query_error %}
           <div class="flex flex-row justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-700 dark:text-gray-300">Query Results</h2>
            <a href="{% url 'download_results' %}?sql_query={{ request.POST.sql_query }}" class="px-4 py-2 mt-2 text-white bg-green-500 rounded-lg hover:bg-green-600">Download Results</a>
          </div>
          <div class="w-full overflow-x-auto">
              <p class="text-red">{{ query_error }}</p>
          </div>
          {% endif %}

          {% if query_results %}
          <div class="flex flex-row justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-700 dark:text-gray-300">Query Results</h2>
            <a href="{% url 'download_results' %}?sql_query={{ request.POST.sql_query }}" class="px-4 py-2 mt-2 text-white bg-inherit-dark rounded-lg hover:border-inherit-dark">Download Results</a>
          </div>
          <div class="w-full overflow-x-auto">
              <table class="w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                      <tr>
                          {% for column in query_results.columns %}
                              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">{{ column }}</th>
                          {% endfor %}
                      </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                      {% for row in query_results.rows %}
                          <tr>
                              {% for cell in row %}
                                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">{{ cell }}</td>
                              {% endfor %}
                          </tr>
                      {% endfor %}
                  </tbody>
              </table>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const executeCommandButton = document.getElementById('execute-command');
  const sqlCommandTextarea = document.getElementById('sql-command');
  const responseContainer = document.getElementById('response-container');

  executeCommandButton.addEventListener('click', function() {
    const sqlCommand = sqlCommandTextarea.value;
    // For now, just display the SQL command in the response container
    responseContainer.textContent = `Executing: ${sqlCommand}`;
    // TODO: Implement the backend request to execute the SQL command and display the response
  });
});
</script>
{% endblock %}