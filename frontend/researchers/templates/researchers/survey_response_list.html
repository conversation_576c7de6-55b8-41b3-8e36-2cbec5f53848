{% extends 'core/base.html' %}
{% load widget_tweaks %}
{% load static %}
{% block title %}Survey List{% endblock %}
{% block content %}
<div class="flex">
  <!-- Main Dash-->
    <div class="flex-1 p-7">
        <div class="flex flex-col-reverse md:grid md:grid-cols-6 gap-4">
            <div id="question-container" class="col-span-5 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Survey Responses</h3>
                <input type="text" id="searchBox" onkeyup="searchQuestions()" placeholder="Search surveys.." class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">  
                <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                    <div class="w-full overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 rounded" id="responsesTable">
                            <thead class="bg-gray-50 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">User</th>
                                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Created Date</th>
                                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Response</th>

    
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for response in survey_responses %}
                                
                                    <tr class="bg-white dark:bg-gray-600">
                                       
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <a href="{% url 'survey_response_detail' response.id %}">
                                                <div class="text-sm text-gray-900 dark:text-white">{{ response.user.id}}</div>
                                            </a>
                                            </td>
                                        
                                        <td class="px-4 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white">{{ response.created_at }}</div>
                                        </td>
                                        <td class="px-4 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white">{{ response.response }}</div>
                                        </td>
                                    </tr>
                                
                                {% endfor %}
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
