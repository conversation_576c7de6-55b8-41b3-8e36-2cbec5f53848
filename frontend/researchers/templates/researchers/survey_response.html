{% extends 'core/base.html' %}

{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="flex max-w-screen-lg">

    <!-- Main Dash-->
    <div class="flex-1 w-full p-2 text-white">
        <div class="flex flex-col w-full gap-4 p-8">
            <div id="chat-container" class="w-full p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h1 class="text-2xl font-normal text-gray-500 dark:text-gray-400" >Model Question Check</h1>
                <div class="pt-2 ml-2">
                    <p>Title: {{ survey.title }}</p>
                    <p>Description: {{ survey.description }}</p>
                </div>
                <div class="w-full flex items-center justify-center p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
                    <div class="w-full h-full flex flex-col justify-between">
                        <div class="flex flex-col space-y-2">
                            <!-- Chat messages will be displayed here -->
                            <div id="chat-box" class="flex flex-col space-y-4 p-4 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-600 dark:border-gray-600 dark:text-white overflow-y-auto flex-grow" style="max-height: 400px;">
                                {% if gpt_reply %}
                                    <div class="flex flex-col justify-start">
                                        <span class="text-md font-bold text-gray-700 dark:text-gray-300">GPT:</span>
                                        <div class="bg-gray-300 text-black p-3 rounded-lg max-w-md p-4">
                                            {{ gpt_reply }}
                                        </div>
                                    </div>
                                    {% endif %}
                            </div>
                        </div>
                        <div class="bg-white p-4 flex items-center rounded dark:bg-gray-800">
                            <form id="chat-form" class="w-full rounded flex items-center">
                                <input type="text" id="message" name="message" placeholder="Type your message..." required
                                class="flex flex-grow p-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <button type="submit" class="ml-2 px-4 py-2 bg-inherit-dark text-white rounded-lg hover:bg-blue-600">Send</button>
                            </form>
                        </div>
                    </div>
                    <!-- if json repsonse containse status == success show button to respnse list -->
                    
                </div>

            </div>

            <div id="result-container" class="flex flex-col w-full p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h2>Survey Details</h2>
                <p>Survey ID: {{ survey.id }}</p>
                <p>Survey Title: {{ survey.title }}</p>
                <p>Survey Description: {{ survey.description }}</p>
                <p>Survey Questions:</p>
                <ul class="ml-4 pl-4">
                    {% for question in survey_questions %}
                        <li>- {{ question.question }} (Type: {{ question.type }}) {% if question.options %} {{question.options}} {% endif %}</li>
                    {% endfor %}
                </ul>
            </div>
            <div>

            </div>
        </div>
    </div>
</div>


<script>
    const form = document.getElementById('chat-form');
    const chatBox = document.getElementById('chat-box');
    const surveyId = {{ survey_id }};

    // Function to scroll to the bottom of the chat box
    function scrollToBottom() {
        chatBox.scrollTo({
            top: chatBox.scrollHeight,
            behavior: 'smooth'
        });
    }

    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const message = document.getElementById('message').value;

        // Display user message
        chatBox.innerHTML += `
        <div class="flex justify-end">
            <div class="flex flex-col">
                <span class="text-md font-bold text-gray-700 dark:text-gray-300">You:</span>
                <div class="bg-inherit-dark text-black p-3 rounded-lg max-w-md">
                    ${message}
                </div>
            </div>
        </div>`;
        scrollToBottom();
        // Send message to server
        const response = await fetch(`/researchers/survey/${surveyId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}',
            },
            body: new URLSearchParams({ message }),
        });

        const data = await response.json();
        console.log(data);
        if (data.response.status === 'success') {
            const gptReply = data.response.response;

            chatBox.innerHTML += `
            <div class="flex flex-col justify-start">
                <span class="text-md font-bold text-gray-700 dark:text-gray-300">GPT:</span>
                <div class="bg-gray-300 text-black p-3 rounded-lg max-w-md">    
                    ${gptReply}
                </div>
            </div>`;
            scrollToBottom();
            if (data.completion_status === 'success') {
                // Show success button
                const successButton = document.createElement('a');
                successButton.textContent = 'Responses';
                successButton.href = `/researchers/responses/${surveyId}/`;
                successButton.className = 'flex flex-col items-start justify-start mt-4 px-4 py-2 bg-inherit-green text-white rounded-lg hover:bg-inherit-green-dark';
                document.getElementById('chat-container').appendChild(successButton);
            }
        } else {
            chatBox.innerHTML += `<p><strong>Error:</strong> ${data.response.error}</p>`;
            scrollToBottom();
        }

        

        form.reset();
    });
</script>

{% endblock %}