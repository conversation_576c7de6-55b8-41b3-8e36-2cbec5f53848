#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Log environment
echo "DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE"
echo "POSTGRES_SSLMODE=$POSTGRES_SSLMODE"

# Wait for database
url="**********************************************************************/$POSTGRES_DATABASE?sslmode=$POSTGRES_SSLMODE"
echo -n "waiting for database to be ready ..."
iteration=0
timeout=30
ready=0
until [[ $ready -eq 1 ]]; do
  if python -c "import psycopg2 as p;co=p.connect('$url');cu=co.cursor();cu.execute('SELECT 1;');cu.fetchall()" &> /dev/null; then
    ready=1
  else
    iteration=$(( iteration + 1 ))
    if [[ $iteration -gt $timeout ]]; then
      echo " timed out after $timeout seconds"
      exit 1
    fi
    sleep 1
    echo -n "."
  fi
done
echo " ok"

# Run Django management commands
python frontend/manage.py makemigrations
python frontend/manage.py migrate
python frontend/manage.py createsuperuser_from_env

# Start nginx
nginx

# Start gunicorn
exec "$@"
