from django import forms
from core.psql_models import ExpertAnswer


class ExpertAnswerForm(forms.ModelForm):
    add_to_topic = forms.BooleanField(
        label='Add to an existing topic?',
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox h-4 w-4 text-inherit-dark border-gray-300 rounded focus:ring-inherit-dark',
        }),
    )
    tag_name = forms.CharField(
        label='Set a Tag Name',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-2 border dark:bg-gray-700 dark:text-white border-gray-300 rounded rounded-xl focus:ring focus:ring-inherit-dark focus:outline-none',
            'placeholder': 'Tag Name',
        }),
    )

    class Meta:
        model = ExpertAnswer
        fields = ['add_to_topic', 'parent_answer', 'answer_text']
        widgets = {
            'answer_text': forms.Textarea(attrs={
                'class': 'w-full dark:bg-gray-700 dark:text-white p-2 border border-gray-300 rounded-md focus:ring focus:ring-inherit-dark focus:outline-none',
                'placeholder': 'Write your answer here...',
                'rows': 5,
            }),
            'parent_answer': forms.Select(attrs={
                'class': 'w-full dark:bg-gray-700 dark:text-white p-2 border border-gray-300 rounded-md focus:ring focus:ring-inherit-dark focus:outline-none',
            }),
            'position_in_tree': forms.HiddenInput(),
        }
        labels = {
            'answer_text': 'Your Answer',
            'parent_answer': 'Please select the parent answer that precededs this one',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Prepare a hierarchical structure for parent_answer
        answers = ExpertAnswer.objects.filter(is_deleted=False).order_by('position_in_tree')
        self.fields['parent_answer'].choices = self.get_hierarchical_choices(answers)

    def get_hierarchical_choices(self, answers):
        """Organize answers into a hierarchical structure."""
        choices = []
        tag_map = {}

        # Group answers by tag
        for answer in answers:
            tag_name = answer.answer_tag.tag_name if answer.answer_tag else "Untagged"
            if tag_name not in tag_map:
                tag_map[tag_name] = []
            tag_map[tag_name].append(answer)

        # Sort tags alphabetically
        sorted_tags = sorted(tag_map.keys())

        # Build choices grouped by tag
        for tag_name in sorted_tags:
            choices.append((None, f"{tag_name}"))  # Add tag as a non-selectable option
            parent_map = {}

            # Filter answers for the current tag
            tag_answers = [answer for answer in answers if (answer.answer_tag and answer.answer_tag.tag_name == tag_name) or (not answer.answer_tag and tag_name == "Untagged")]
            # Group answers by tag then parent
            for answer in tag_answers:
                if answer.position_in_tree == 1:
                    parent_map[answer.answer_id] = {
                        'parent': answer,
                        'children': [],
                    }
                elif answer.parent_answer_id in parent_map:
                    parent_map[answer.parent_answer_id]['children'].append(answer)

            # Build choices with grouping
            for parent_id, group in parent_map.items():
                parent = group['parent']
                children = group['children']
                # Add parent answer
                choices.append((parent.answer_id, f"-{parent.answer_text}"))
                # Add child answers with indentation
                for child in children:
                    choices.append((child.answer_id, f"-- {child.answer_text}"))
            # print(choices)
        return choices
