from django.test import TestCase, Client
from django.urls import reverse
from core.psql_models import <PERSON><PERSON>, ExpertAnswer, UserQuestion, User
from experts.forms import ExpertAnswerForm
from django.utils.timezone import now
from users.models import CustomUser


class ExpertViewsTests(TestCase):
    databases = '__all__'

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create(
            user_id=330,
            sub="somestring",
            username="expert_user",
            email="<EMAIL>",
            birth_month=1,
            birth_year=1990,
            sex_assigned_at_birth="Male",
            reward_points=0,
            reward_point_awarded_for_instagram=False,
            reward_point_awarded_for_personal_health_survey=False,
            reward_point_awarded_for_reproductive_health_survey=False,
            reward_point_awarded_for_lifestyle_survey=False,
            reward_point_awarded_for_sharing=False,
            reward_point_awarded_to_referrer=False,
            referral_code="referral_123",
            impact_points=0,
            created_date=now(),
            is_deleted=False,
        )
        self.expert_user = Expert.objects.create(
            sub="placeholder",
            expert_id="expert_123",
            name="Test Expert",
            qualifications="MD, PhD",
            email="<EMAIL>",
            created_date=now(),
            is_deleted=False
        )

        self.user_question = UserQuestion.objects.create(
            question_id="question_123",
            user_id=self.user.user_id,
            question_text="Test question?",
            moderation_status="Pending",
            votes=1,
            created_date=now(),
            is_deleted=False
        )

        self.expert_answer = ExpertAnswer.objects.create(
            answer_id="answer_123",
            question_text="Test question?",
            answer_text="Test answer.",
            expert_id=self.expert_user.expert_id,
            position_in_tree=1,
            created_date=now(),
            is_deleted=False
        )
        self.stf_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Test",
            "last_name": "User",
            "role": "STF",
        }
        self.exp_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Expert",
            "last_name": "USer",
            "role": "EXP",
            "expert_id": self.expert_user.expert_id
        }
        self.stf_user = CustomUser.objects.create_user(**self.stf_data)
        self.exp_user = CustomUser.objects.create_user(**self.exp_data)

    def test_expert_home_view(self):
        """Test ExpertHome view"""
        # test staff user access
        self.client.login(email='<EMAIL>', password='securepassword123', role="STF")
        response = self.client.get(reverse('expert_home'))
        self.assertEqual(response.status_code, 200)
        # test expert user access
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.get(reverse('expert_home'))
        self.assertEqual(response.status_code, 200)

        self.assertTemplateUsed(response, 'experts/expert_home.html')
        self.assertIn(self.user_question, response.context['questions'])

    def test_expert_answer_view_get(self):
        """Test GET request to ExpertAnswerView"""
        # test staff user access
        self.client.login(email='<EMAIL>', password='securepassword123', role="STF")
        response = self.client.get(reverse('expert_answer', kwargs={'pk': self.user_question.question_id}))
        self.assertEqual(response.status_code, 200)
        # test expert user access
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.get(reverse('expert_answer', kwargs={'pk': self.user_question.question_id}))
        self.assertEqual(response.status_code, 200)

        self.assertTemplateUsed(response, 'experts/expert_answer.html')
        self.assertIsInstance(response.context['form'], ExpertAnswerForm)
        self.assertEqual(response.context['question_text'], self.user_question.question_text)

    def test_expert_answer_view_post(self):
        """Test POST request to ExpertAnswerView"""
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.post(reverse('expert_answer', kwargs={'pk': self.user_question.question_id}), {
            'answer_text': 'New answer text',
            'tag_name': 'New tag',
            'add_to_topic': False
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful form submission
        self.assertRedirects(response, '/experts/')
        self.user_question.refresh_from_db()
        self.assertEqual(self.user_question.moderation_status, 'Answered')

    def test_expert_answer_update_view_get(self):
        """Test GET request to ExpertAnswerUpdateView"""
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.get(reverse('expert_answer_update', kwargs={'pk': self.expert_answer.answer_id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'experts/update_expert_answer.html')
        self.assertEqual(response.context['question_text'], self.expert_answer.question_text)

    def test_expert_answer_update_view_post(self):
        """Test POST request to ExpertAnswerUpdateView"""
        # add the answer object to the question
        self.user_question.answer = self.expert_answer
        self.user_question.save()
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        # test approval button
        response = self.client.post(reverse('expert_answer_update', kwargs={'pk': self.expert_answer.answer_id}), {
            'action': 'Approved'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful update
        self.assertRedirects(response, '/experts/')

        # test answer change
        response = self.client.post(reverse('expert_answer_update', kwargs={'pk': self.expert_answer.answer_id}), {
            'answer_text': 'Updated answer text',
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful update
        self.assertRedirects(response, '/experts/')
        self.expert_answer.refresh_from_db()
        self.assertEqual(self.expert_answer.answer_text, 'Updated answer text')

    def test_expert_question_view_get(self):
        """Test GET request to ExpertQuestionView"""
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.get(reverse('expert_question', kwargs={'pk': self.user_question.question_id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'experts/expert_question_detail.html')
        self.assertEqual(response.context['question'], self.user_question)

    def test_expert_question_view_post_reject(self):
        """Test POST request to ExpertQuestionView for rejecting a question"""
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.post(reverse('expert_question', kwargs={'pk': self.user_question.question_id}), {
            'action': 'Rejected'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after rejecting the question
        self.assertRedirects(response, '/experts/')
        self.user_question.refresh_from_db()
        self.assertEqual(self.user_question.moderation_status, 'Rejected')

    def test_get_parent_answer(self):
        """Test get_parent_answer function"""
        self.client.login(email='<EMAIL>', password='securepassword123', role="EXP")
        response = self.client.get(reverse('get_parent_answer', kwargs={'parent_answer_id': self.expert_answer.answer_id}))
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, {
            'question': self.expert_answer.question_text,
            'answer': self.expert_answer.answer_text
        })
