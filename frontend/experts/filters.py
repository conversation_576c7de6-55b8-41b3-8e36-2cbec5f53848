import django_filters
from core.psql_models import UserQuestion


class QuestionFilter(django_filters.FilterSet):
    status = django_filters.ChoiceFilter(
        choices=[
            ('Pending', 'Pending'),
            ('Answered', 'Answered'),
            ('Approved', 'Approved'),
            ('Rejected', 'Rejected'),
        ],
        field_name='moderation_status',
        lookup_expr='iexact',
        empty_label='Select All',
    )
    order_by_date = django_filters.OrderingFilter(
        fields=(
            ('created_date', 'created_date'),
        ),
        field_labels={
            'created_date': 'Date',
        },
        label='Sort by Date',
        choices=[
            ('created_date', 'Oldest to Newest'),
            ('-created_date', 'Newest to Oldest'),
        ]
    )

    class Meta:
        model = UserQuestion
        fields = ['status', 'votes']
