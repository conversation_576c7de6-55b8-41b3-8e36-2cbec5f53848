from django.urls import path
from . import views


urlpatterns = [
    path('', views.ExpertHome.as_view(), name='expert_home'),
    path('answer/<str:pk>/', views.ExpertAnswerView.as_view(), name='expert_answer'),
    path('answer/<str:pk>/update', views.ExpertAnswerUpdateView.as_view(), name='expert_answer_update'),
    # path('answer/<str:pk>/delete', views.ExpertAnswerDeleteView.as_view(), name='expert_answer_delete'),
    path('question/<str:pk>', views.ExpertQuestionView.as_view(), name='expert_question'),
    path('get-parent-answer/<str:parent_answer_id>/', views.get_parent_answer, name='get_parent_answer'),
]
