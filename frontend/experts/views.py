from django.shortcuts import get_object_or_404
from django.http import JsonResponse, HttpResponseRedirect
from django.core.exceptions import PermissionDenied
from django.contrib.auth.decorators import login_required
from core.role import RoleCreate<PERSON>iew, RoleListView, RoleUpdateView, RoleDetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from core.psql_models import Expert, ExpertAnswer, UserQuestion, ExpertAnswerTag
from django.db.models import Q
from django.utils import timezone
from core.decorators import RoleRequiredMixin
from .filters import QuestionFilter
from .forms import ExpertAnswerForm
import uuid
import logging


class ExpertHome(LoginRequiredMixin, RoleRequiredMixin, RoleListView):
    model = UserQuestion
    template_name = 'experts/expert_home.html'
    context_object_name = 'questions'
    filterset_class = QuestionFilter
    allowed_roles = ['STF', 'EXP']

    def get_queryset(self):
        # TODO: Add filter for current user expert_id to only see q's assigned to them
        queryset = super().get_queryset()  # noqa: F841
        return UserQuestion.objects.filter(
            Q(is_deleted=False) & Q(moderation_status__in=['Pending', 'Answered', 'Approved', 'Rejected'])
        ).order_by('-created_date').select_related('answer')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context['filter'] = QuestionFilter(self.request.GET, queryset=self.get_queryset())
        context['answers'] = ExpertAnswer.objects.all()
        if self.request.htmx:
            self.template_name = 'experts/partials/questions_container.html'
        return context


class ExpertAnswerView(LoginRequiredMixin, RoleRequiredMixin, RoleCreateView):
    """ Use this view to answer a question """
    model = ExpertAnswer
    form_class = ExpertAnswerForm
    template_name = 'experts/expert_answer.html'
    allowed_roles = ['STF', 'EXP']

    # will need to add logic for parent answers to this field list later
    success_url = '/experts/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        question_id = self.kwargs['pk']
        question = get_object_or_404(UserQuestion, question_id=question_id)
        context['question_text'] = question.question_text
        context['question'] = question

        if self.request.user.role == 'EXP':
            expert = get_object_or_404(Expert, expert_id=self.request.user.expert_id)  # noqa: F841

        elif self.request.user.role == 'STF':
            expert = "Staff"  # noqa: F841

        return context

    def form_valid(self, form):
        question = get_object_or_404(UserQuestion, question_id=self.kwargs['pk'])
        form.instance.question_text = question.question_text

        # Generate a random UUID for the expert_id field
        form.instance.answer_id = uuid.uuid4()

        if self.request.user.expert_id is not None:
            expert = get_object_or_404(Expert, expert_id=self.request.user.expert_id)
            form.instance.expert_id = expert.expert_id
        else:
            logging.error("User does not have an expert_id, cannot save answer")
            raise PermissionDenied("You do not have permission to access this resource")

        # Set the position_in_tree to 1 will need to update this in next stage
        if form.cleaned_data.get('add_to_topic'):
            # Get the parent answer from the form data
            parent_answer = form.cleaned_data.get('parent_answer')
            if parent_answer:
                # Set the position_in_tree to next int for child answers
                next_pos = parent_answer.position_in_tree + 1
                tag = parent_answer.answer_tag
                form.instance.position_in_tree = next_pos
                form.instance.answer_tag = tag
            else:
                # If no parent answer is selected, set it to None
                logging.info("No parent answer selected, setting position_in_tree to 1")
                form.instance.parent_answer = None

        else:
            form.instance.parent_answer = None
            form.instance.position_in_tree = 1
            # create tag instance
            tag_name = form.cleaned_data.get('tag_name')
            tag, created = ExpertAnswerTag.objects.get_or_create(
                tag_name=tag_name,
                expert_id=expert.expert_id,
                created_date=timezone.now(),
                defaults={'is_deleted': False}
            )
            form.instance.answer_tag = tag

        form.instance.created_date = timezone.now()
        form.instance.is_deleted = False

        # Save the form instance of ExpertAnswer
        saved_answer = form.save()

        # Update the UserQuestion instance
        question.answer = form.instance
        question.moderation_status = 'Answered'

        saved_answer.save()
        question.save()

        response = super().form_valid(form)
        return response


class ExpertAnswerUpdateView(LoginRequiredMixin, RoleRequiredMixin, RoleUpdateView):
    """ Use this view to update an answer or update answer status """
    model = ExpertAnswer
    template_name = 'experts/update_expert_answer.html'
    fields = ['answer_text']
    success_url = '/experts/'
    allowed_roles = ['STF', 'EXP']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # get question text
        answer_id = self.kwargs['pk']
        answer = get_object_or_404(ExpertAnswer, answer_id=answer_id)
        context['question_text'] = answer.question_text
        return context

    def post(self, request, *args, **kwargs):
        # Check if the "Approve" button was clicked
        if request.POST.get('action') == 'Approved':
            # Get the related question
            answer = self.get_object()
            question = get_object_or_404(UserQuestion, answer=answer)
            # Update the question's status to "Approved"
            question.moderation_status = 'Approved'
            question.save()
            # Redirect to the success URL
            return HttpResponseRedirect(self.success_url)

        # For other actions, fallback to default form submission behavior
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.created_date = timezone.now()
        form.instance.is_deleted = False
        form.instance.answer_text = form.cleaned_data.get('answer_text')
        return super().form_valid(form)


class ExpertQuestionView(LoginRequiredMixin, RoleRequiredMixin, RoleDetailView):
    """ Use this view to reject a question """
    model = UserQuestion
    template_name = 'experts/expert_question_detail.html'
    context_object_name = 'question'
    success_url = '/experts/'
    allowed_roles = ['STF', 'EXP']

    def post(self, request, *args, **kwargs):
        # Check if the "Approve" button was clicked
        if request.POST.get('action') == 'Rejected':
            # Get the related question
            question = self.get_object()
            # Update the question's status to "Rejected"
            question.moderation_status = 'Rejected'
            question.save()
            # Redirect to the success URL
            return HttpResponseRedirect(self.success_url)


@login_required
def get_parent_answer(request, parent_answer_id):
    if request.method == 'GET':
        # Retrieve the parent answer object
        parent_answer = get_object_or_404(ExpertAnswer, answer_id=parent_answer_id)

        # Return the data as JSON
        return JsonResponse({
            'question': parent_answer.question_text,
            'answer': parent_answer.answer_text,
        })

    return JsonResponse({'error': 'Invalid request'}, status=400)
