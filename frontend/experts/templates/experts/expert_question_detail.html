{% extends 'core/base.html' %}

{% load static %}
{% block title %}Question{% endblock %}
{% block content %}
<div class="flex w-full">
    <!-- Main Dash-->
    <div class="flex-1 p-7">
        <div class="w-3/4 gap-4">
            <div id="question-container" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Details for Question:</h3>

                <div class="pt-2 bg-gray-100 border border-gray-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <p class="p-2 text-sm text-gray-900 dark:text-white">{{ question.question_text }}</p>
                </div>
                <div>
                    <form method="post">
                        {% csrf_token %}
                        {% if form.non_field_errors %}
                        <div class="mt-4">
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <strong class="font-bold">Error!</strong>
                                <span class="block sm:inline">{{ form.non_field_errors }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% for field in form %}
                        <div class="mt-4">
                            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-text-inherit-dark dark:text-inherit-dark">{{ field.label }}</label>
                            <textarea name="{{ field.name }}" id="{{ field.id_for_label }}" 
                                class="w-full p-2 border border-gray-300 rounded-md focus:ring focus:ring-blue-500 focus:outline-none">
                                {{ field.value|default_if_none:"" }}
                            </textarea>

                            {% if field.errors %}
                            <small class="error text-red-600">{{ field.errors|striptags }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                        <div class="flex mt-4 align-left justify-left space-x-2">
                            <a href="{% url 'expert_home' %}" 
                                class="inline-block px-4 py-2 text-sm font-medium text-inherit-dark hover:text-button-light bg-button-light hover:bg-button-dark focus:ring-4 focus:outline-none focus:ring-inherit-dark">
                                Cancel
                            </a>
                            <!-- Reject Link -->
                            <button type="submit" name="action" value="Rejected"
                                class="inline-block px-4 py-2 text-sm font-medium text-white bg-inherit-yellow hover:bg-inherit-yellow-dark focus:ring-4 focus:outline-none focus:ring-inherit-yellow">
                                Reject
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}