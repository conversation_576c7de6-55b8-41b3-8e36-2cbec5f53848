{% extends 'core/base.html' %}
{% load widget_tweaks %}
{% load static %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="flex">
  <!-- Main Dash-->
  <div class="flex-1 p-7">
    <div class="flex flex-col-reverse md:grid md:grid-cols-6 gap-4">
      {% include 'experts/partials/questions_container.html' %}
      <div id="filters" class="col-span-1 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Filters</h3>
        <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
          <div class="w-full">
            <form hx-get="{% url 'expert_home' %}" hx-target="#question-container" hx-swap="outerHTML">
                <div class="flex flex-col mb-2 form-control">
                  {{ filter.form.status | add_label_class:"label text-gray-500 dark:text-white"}}
                  {% render_field filter.form.status class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mb-4 form-control">
                  {{ filter.form.order_by_date | add_label_class:"label text-gray-500 dark:text-white" }}
                  {% render_field filter.form.order_by_date class="input bg-gray-50 text-gray-900" %}
                </div>
                <div class="flex flex-col mt-2 mb-2 form-control">
                  <button type="submit" class="btn bg-inherit-blue-dark">Search</button>
                </div>
            </form>
          </div>
      </div>
    </div>
  </div>
</div>
<script>
    function searchQuestions() {
        var input, filter, table, tr, td, i, txtValue;
        input = document.getElementById("searchBox");
        filter = input.value.toUpperCase();
        table = document.getElementById("questionTable");
        tr = table.getElementsByTagName("tr");
        for (i = 0; i < tr.length; i++) {
            td = tr[i].getElementsByTagName("td")[0];
            if (td) {
                txtValue = td.textContent || td.innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    tr[i].style.display = "";
                } else {
                    tr[i].style.display = "none";
                }
            }
        }
    }
</script>
{% endblock %}