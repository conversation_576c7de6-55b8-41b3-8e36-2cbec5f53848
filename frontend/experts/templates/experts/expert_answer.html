{% extends 'core/base.html' %}

{% load static %}
{% block title %}Answer{% endblock %}
{% block content %}
<div class="flex w-full">
    <!-- Main Dash-->
    <div class="flex-1 p-7">
        <div class="w-3/4 gap-4">
            <div id="question-container" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
                <h3 class="text-base font-normal text-inherit-dark dark:text-inherit-dark">You are answering the following question:</h3>

                <div class="pt-2 bg-gray-100 rounded-lg border border-gray-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <p class="p-2 text-sm text-gray-900 dark:text-white">{{ question.question_text }}</p>
                </div>
                <div>
                    <form method="post">
                        {% csrf_token %}
                        {% if form.non_field_errors %}
                        <div class="mt-4">
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                                <strong class="font-bold">Error!</strong>
                                <span class="block sm:inline">{{ form.non_field_errors }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% for field in form %}
                            {% if field.name == "add_to_topic" %}
                            <div class="mt-4">
                                <label for="{{ field.id_for_label }}" class="block text-md font-medium text-text-inherit-dark dark:text-inherit-dark">{{ field.label }}</label>
                                {{ field }}
                                {% if field.errors %}
                                <small class="error text-red-600">{{ field.errors|striptags }}</small>
                                {% endif %}
                            </div>
                            {% elif field.name == "parent_answer" %}
                            <div class="mt-4" id="parent-answer-field">
                                <label for="{{ field.id_for_label }}" class="block text-md font-medium text-text-inherit-dark dark:text-inherit-dark">{{ field.label }}</label>
                                {{ field }}
                                {% if field.errors %}
                                <small class="error text-red-600">{{ field.errors|striptags }}</small>
                                {% endif %}
                            </div>
                            {% elif field.name == "tag_name" %}
                            <div class="mt-4" id="tag-name-field">
                                <label for="{{ field.id_for_label }}" class="block text-md font-medium text-text-inherit-dark dark:text-inherit-dark">{{ field.label }}</label>
                                {{ field }}
                                {% if field.errors %}
                                <small class="error text-red-600">{{ field.errors|striptags }}</small>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="mt-4">
                                <label for="{{ field.id_for_label }}" class="block text-md font-medium text-text-inherit-dark dark:text-inherit-dark">{{ field.label }}</label>
                                {{ field }}
                                {% if field.errors %}
                                <small class="error text-red-600">{{ field.errors|striptags }}</small>
                                {% endif %}
                            </div>
                            {% endif %}
                        {% endfor %}

                        <div 
                            id="parent-answer-details" 
                            style="display: none; margin-top: 20px;"
                        >
                            <!-- Parent question and answer details will be dynamically inserted here -->
                        </div>
                        <div class="flex mt-4 align-left justify-left space-x-2">
                            <button type="submit" 
                                class="inline-block px-4 py-2 text-sm font-medium text-white hover:text-button-dark bg-button-dark hover:bg-button-light focus:ring-4 focus:outline-none focus:ring-inherit-dark"
                                >
                                Submit
                            </button>
                            <a href="{% url 'expert_home' %}" 
                                class="inline-block px-4 py-2 text-sm font-medium text-inherit-dark hover:text-button-light bg-button-light hover:bg-button-dark focus:ring-4 focus:outline-none focus:ring-inherit-dark">
                                Cancel
                            </a>
                            <!-- Reject Link -->
                            <a href="{% url 'expert_question' question.question_id %}"
                                class="inline-block px-4 py-2 text-sm font-medium text-white bg-inherit-yellow hover:bg-inherit-yellow-dark focus:ring-4 focus:outline-none focus:ring-inherit-yellow">
                                Reject
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const isParentCheckbox = document.querySelector('#id_add_to_topic');
        const parentAnswerField = document.querySelector('#parent-answer-field');
        const tagNameField = document.querySelector('#tag-name-field');
        const parentAnswerInput = document.querySelector('#id_parent_answer');
        const parentAnswerDetails = document.querySelector('#parent-answer-details');

        function toggleParentAnswerField() {
            if (isParentCheckbox.checked) {
                parentAnswerField.style.display = 'block';
                tagNameField.style.display = 'none';
            } else {
                parentAnswerField.style.display = 'none';
                tagNameField.style.display = 'block';
            }
        }

        // Initial toggle based on the current state
        toggleParentAnswerField();

        // Add event listener to toggle visibility on change
        isParentCheckbox.addEventListener('change', toggleParentAnswerField);
        
        // Log the current value of the parent answer field when clicked
        parentAnswerInput.addEventListener('change', function () {
            const selectedValue = parentAnswerInput.value;
            console.log('Selected Parent Answer ID:', selectedValue);

            if (selectedValue) {
                // Make an AJAX call to fetch the parent answer details
                fetch(`/experts/get-parent-answer/${selectedValue}/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest', // Required for Django to recognize it as an AJAX request
                    },
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Parent Answer Details:', data);

                        // Update the DOM with the retrieved data
                        parentAnswerDetails.innerHTML = `
                        <div class="dark:text-white">
                            <h4 class="font-semibold mb-2">Related Answers:</h4>
                        </div>`;

                        data.answers.forEach(answer => {
                            parentAnswerDetails.innerHTML += `
                                <div class="mb-2 p-2 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <p class="pl-2 font-medium text-gray-100">Q: ${answer.question_text}</p>
                                    <p class="pl-2 text-gray-200">A: ${answer.answer_text}</p>
                                </div>
                            `;
                        });
                        parentAnswerDetails.style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error fetching parent answer details:', error);
                    });
            }
        });
    });
</script>
{% endblock %}