<div id="question-container" class="col-span-5 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 overflow-x-auto">
    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">User Questions</h3>
    <input type="text" id="searchBox" onkeyup="searchQuestions()" placeholder="Search for questions.." class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">  
    <div class="w-full p-2 mt-2 text-gray-900 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white" style="min-height: 150px;">
      <div class="w-full overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 rounded" id="questionTable">
            <thead class="bg-gray-50 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                <tr>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Question</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Status</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Votes</th>
                    <th scope="col" class="px-4 py-3 text-center text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Action</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Answered By</th>
                    <th scope="col" class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider dark:text-white">Created</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for question in filter.qs %}
                <tr class="bg-white dark:bg-gray-600">
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 dark:text-white">{{ question.question_text }}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 dark:text-white">{{ question.moderation_status }}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 text-center dark:text-white">{{ question.votes }}</div>
                    </td>
                    {% if question.moderation_status == 'Pending' %}
                        <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="{% url 'expert_answer' question.question_id %}" class="bg-inherit-green rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-green-dark hover:bg-inherit-green-dark">Respond</a>
                        </td>
                    {% elif question.moderation_status == 'Rejected' %}
                        <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="{% url 'expert_answer' question.question_id %}" class="bg-inherit-red rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-red-dark hover:bg-inherit-red-dark">Update</a>
                        </td>
                    {% else %}
                        <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <a href="{% url 'expert_answer_update' question.answer.answer_id %}" class="bg-inherit-yellow rounded text-white inline-block w-24 h-10 flex items-center justify-center text-sm font-medium focus:ring-4 focus:outline-none focus:ring-inherit-yellow-dark hover:bg-inherit-yellow-dark">Edit</a>
                        </td>
                    {% endif %}
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900 dark:text-white">
                        {% if question.answer %}
                        {{ question.answer.expert.name }}
                        {% else %} 
                        {% endif %}
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 dark:text-white">{{ question.created_date }}</div>
                    </td>
                    
                </tr>
                {% endfor %}
            </tbody>
        </table>
      </div>
    </div>
  </div>