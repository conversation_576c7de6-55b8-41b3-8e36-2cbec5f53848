import asyncio
import logging
import sys
import uuid
from typing import (
    TYPE_CHECKING,
    Any,
    AsyncGenerator,
    AsyncIterator,
    Awaitable,
    Callable,
    Generator,
    Iterator,
    Type,
    Union,
    cast,
)
import structlog
from asgiref import sync
from django.core.exceptions import PermissionDenied
from django.core.signals import got_request_exception
from django.http import Http404, StreamingHttpResponse


from inspect import (  # type: ignore[attr-defined]
    iscoroutinefunction,
    markcoroutinefunction,
)

if TYPE_CHECKING:  # pragma: no cover
    from types import TracebackType

    from django.http import HttpRequest, HttpResponse

logger = structlog.getLogger("inherit.structlog")


def sync_streaming_content_wrapper(
    streaming_content: Iterator[bytes], context: Any
) -> Generator[bytes, None, None]:
    with structlog.contextvars.bound_contextvars(**context):
        logger.info("streaming_started")
        try:
            for chunk in streaming_content:
                yield chunk
        except Exception:
            logger.exception("streaming_failed")
            raise
        else:
            logger.info("streaming_finished")


async def async_streaming_content_wrapper(
    streaming_content: AsyncIterator[bytes], context: Any
) -> AsyncGenerator[bytes, Any]:
    with structlog.contextvars.bound_contextvars(**context):
        logger.info("streaming_started")
        try:
            async for chunk in streaming_content:
                yield chunk
        except asyncio.CancelledError:
            logger.warning("streaming_cancelled")
            raise
        except Exception:
            logger.exception("streaming_failed")
            raise
        else:
            logger.info("streaming_finished")


class RequestMiddleware:
    """RequestMiddleware logs request/responses using structlog. Based on django_structlog."""

    sync_capable = True
    async_capable = True

    def __init__(self, get_response: Callable[["HttpRequest"], Union["HttpResponse", Awaitable["HttpResponse"]]]) -> None:
        self.get_response = get_response
        if iscoroutinefunction(self.get_response):
            markcoroutinefunction(self)
        got_request_exception.connect(self.process_got_request_exception)

    def __call__(self, request: "HttpRequest") -> Union["HttpResponse", Awaitable["HttpResponse"]]:
        if iscoroutinefunction(self):
            return cast(RequestMiddleware, self).__acall__(request)
        response = cast("HttpResponse", self.get_response(request))
        self.handle_response(request, response)
        return response

    async def __acall__(self, request: "HttpRequest") -> "HttpResponse":
        try:
            response = await cast(Awaitable["HttpResponse"], self.get_response(request))
        except asyncio.CancelledError:
            logger.warning("request_cancelled")
            raise
        await sync.sync_to_async(self.handle_response)(request, response)
        return response

    def handle_response(self, request: "HttpRequest", response: "HttpResponse") -> None:
        if not hasattr(request, "_raised_exception"):
            self.bind_user_id(request)
            context = structlog.contextvars.get_merged_contextvars(logger)

            log_kwargs = dict(
                status_code=response.status_code,
                path=request.get_full_path(),
                method=request.method,
            )

            if response.status_code >= 500:
                level = logging.ERROR
            elif response.status_code >= 400:
                level = logging.INFO
            else:
                level = logging.INFO
            logger.log(
                level,
                f"{request.method} {request.get_full_path()} {response.status_code}",
                **log_kwargs,
            )
            if isinstance(response, StreamingHttpResponse):
                streaming_content = response.streaming_content
                if response.is_async:
                    response.streaming_content = async_streaming_content_wrapper(
                        cast(AsyncIterator[bytes], streaming_content), context
                    )
                else:
                    response.streaming_content = sync_streaming_content_wrapper(
                        cast(Iterator[bytes], streaming_content), context
                    )

        else:
            delattr(request, "_raised_exception")
        structlog.contextvars.clear_contextvars()

    @staticmethod
    def bind_user_id(request: "HttpRequest") -> None:
        user_id_field = "pk"
        if hasattr(request, "user") and request.user is not None and user_id_field:
            user_id = None
            if hasattr(request.user, user_id_field):
                user_id = getattr(request.user, user_id_field)
                if isinstance(user_id, uuid.UUID):
                    user_id = str(user_id)
            structlog.contextvars.bind_contextvars(user_id=user_id)

    def process_got_request_exception(self, sender: Type[Any], request: "HttpRequest", **kwargs: Any) -> None:
        if not hasattr(request, "_raised_exception"):
            ex = cast(
                tuple[Type[Exception], Exception, "TracebackType"],
                sys.exc_info(),
            )
            self._process_exception(request, ex[1])

    def _process_exception(self, request: "HttpRequest", exception: Exception) -> None:
        if isinstance(exception, (Http404, PermissionDenied)):
            return  # don't log an exception and don't set that we handled an error
        setattr(request, "_raised_exception", exception)
        self.bind_user_id(request)
        log_kwargs = dict(
            status_code=500,
            path=request.get_full_path(),
            method=request.method,
        )
        logger.exception(
            f"{request.method} {request.get_full_path()} 500",
            **log_kwargs,
        )
