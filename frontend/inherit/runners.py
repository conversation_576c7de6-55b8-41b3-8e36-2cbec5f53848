# Credit:
# http://birdhouse.org/blog/2015/03/25/django-unit-tests-against-unmanaged-databases/
# https://www.caktusgroup.com/blog/2010/09/24/simplifying-the-testing-of-unmanaged-database-models-in-django/

from django.test.runner import Discover<PERSON>unner
import subprocess
from django.conf import settings
import os
import logging
from pathlib import Path


project_root = Path(__file__).resolve().parent.parent.parent


class ManagedModelTestRunner(DiscoverRunner):
    """
    Test runner that automatically makes all unmanaged models in your Django
    project managed for the duration of the test run, so that one doesn't need
    to execute the SQL manually to create them.
    """

    def __init__(self, **kwargs):
        from django.apps import apps

        super(ManagedModelTestRunner, self).__init__(**kwargs)

        # for a in apps.get_app_configs():
        #   print("Found app %s" % (a))

        # NOTE: apps must be registered in INSTALLED_APPS in settings.py before their models appear here
        all_models = apps.get_models()
        # for m in all_models:
        #     print("Found model %s - Managed:%s" % (m, m._meta.managed))

        self.unmanaged_models = [m for m in all_models if not m._meta.managed]

    # test DB is set to keep, run this when new tables are added
    def setup_databases(self, **kwargs):
        # Call the parent method to set up the test databases
        result = super().setup_databases(**kwargs)

        # Load the schema into the test database
        logging.info("Loading schema into test database...")
        schema_file_path = project_root / "repository/schema/schema.sql"
        test_db_name = settings.DATABASES['backend']['NAME']
        test_db_user = settings.DATABASES['backend']['USER']
        test_db_host = settings.DATABASES['backend']['HOST']
        test_db_port = settings.DATABASES['backend']['PORT']
        test_db_password = settings.DATABASES['backend']['PASSWORD']

        if not os.path.exists(schema_file_path):
            raise FileNotFoundError(f"Schema file not found at {schema_file_path}")

        try:
            subprocess.run(
                [
                    "psql",
                    "-U", test_db_user,
                    "-h", test_db_host,
                    "-p", str(test_db_port),
                    "-d", test_db_name,
                    "-f", schema_file_path,
                ],
                env={**os.environ, "PGPASSWORD": test_db_password},
                check=True,
            )
            logging.info("Schema loaded successfully.")
        except subprocess.CalledProcessError as e:
            logging.info(f"Error loading schema: {e}")
            raise

        return result

    def setup_test_environment(self, *args, **kwargs):
        for m in self.unmanaged_models:
            m._meta.managed = True
            logging.info("Modifying model %s to be managed for testing - Managed:%s" % (m, m._meta.managed))
        super(ManagedModelTestRunner, self).setup_test_environment(*args, **kwargs)

    def teardown_test_environment(self, *args, **kwargs):
        super(ManagedModelTestRunner, self).teardown_test_environment(*args, **kwargs)
        # reset unmanaged models
        for m in self.unmanaged_models:
            m._meta.managed = False
            # logging.info("Resetting model %s to be unmanaged - Managed:%s" % (m, m._meta.managed))
