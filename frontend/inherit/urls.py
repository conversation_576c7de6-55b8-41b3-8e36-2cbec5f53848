"""
URL configuration for inherit project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, re_path, include
from users.views import CustomLoginView
from debug_toolbar.toolbar import debug_toolbar_urls

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    path('accounts/login/', CustomLoginView.as_view(), name='account_login'),
    path('accounts/', include('users.urls')),
    path('experts/', include('experts.urls')),
    path('surveys/', include('surveys.urls')),
    path('researchers/', include('researchers.urls')),
    path('staff/', include('staff.urls')),
    re_path(r'^accounts/', include('allauth.urls')),
    re_path(r'^accounts/', include('allauth.socialaccount.urls')),
] + debug_toolbar_urls()
