class DatabaseRouter:
    """
    A router to control all database operations on models for different databases.
    Backend services are inthe core_app
    """

    def db_for_read(self, model, **hints):
        """
        Attempts to read models go to the appropriate database.
        """
        if model._meta.app_label == 'core':
            return 'backend'
        return 'default'

    def db_for_write(self, model, **hints):
        """
        Attempts to write models go to the appropriate database.
        """
        if model._meta.app_label == 'core':
            return 'backend'
        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if a model in the core is involved.
        """
        if obj1._meta.app_label == 'core' or obj2._meta.app_label == 'core':
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the core only appears in the 'backend' database.
        """
        if app_label == 'core':
            return db == 'backend'
        return db == 'default'
