/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class', // or 'media' or 'class'
  content: [
    "./experts/templates/**/*.{html, js}",
    "./core/templates/**/*.{html, js}",
    "./users/templates/**/*.{html, js}",
    "./templates/**/*.{html, js}",
    "./surveys/templates/**/*.{html, js}",
    "./researchers/templates/**/*.{html, js}",
    "./staff/templates/**/*.{html, js}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Poppins", 'ui-sans-serif', 'system-ui'],
       },
      colors: {
        "inherit-light": "#E3C3FF",
        "inherit-dark": "#A687E5",
        "inherit-bright": "#FC92E9",
        "inherit-yellow": "#FDD305",
        "inherit-yellow-dark": "#E6BC04",
        "inherit-green": "#29D6C6",
        "inherit-green-dark": "#25C0B2",
        "inherit-blue": "#91D1FF",
        "inherit-blue-dark": "#82BDE6",
        "inherit-red": "#FF5C5C", // Bright red
        "inherit-red-dark": "#B03030", // Slightly darker red
        "button-dark": "#2D346B",
        "button-light": "#F2F4F8",
      },
      screens: {
        'xs': '320px',
        'sm': '640px',
        'md': '768px',
        'lg': '1084px',
      },
    },
    plugins: [],
  },
  variants: {
    extend: {
      display: ['group-hover'],
    },
  },
}

