#!/bin/bash
set -e # stop on error
if [[ -f .venv/bin/activate ]]; then source .venv/bin/activate; fi

echo "generate prompts.json from prompts"
prompts/gen.py prompts > backend/prompts.json
prompts/gen.py prompts > worker/prompts.json

echo "generate python client from database schema and queries: sqlc generate"
sqlc generate --file=repository/sqlc.yaml
repository/fix.py backend/repository/*.py
repository/fix.py worker/repository/*.py

echo "generate OpenAPI spec from FastAPI app: python -m backend.gen"
export SECRET_KEY=foo
export DOCS_PASSWORD=bar
export TOKEN_ISSUER=beep
export TOKEN_AUDIENCE=boop
python -m backend.gen

#echo "generate dart client from OpenAPI spec"
#openapi-generator-cli generate --input-spec=api/spec.json --generator-name=dart --output=mobile/lib/inherit_api --additional-properties=pubLibrary=inherit_api,pubName=inherit_api

echo "prepare to generate Django models from database schema: docker compose up, repository/apply.sh"
export BACKEND_HOST=localhost
export BACKEND_USERNAME=postgres
export BACKEND_PASSWORD=password
export BACKEND_DATABASE=postgres
export SECRET_KEY=foo
opts="--project-directory=. --project-name=inherit --file=repository/compose.yaml"
docker compose $opts up --detach
trap "docker compose $opts down" EXIT
source repository/apply.sh
echo "generate Django models in frontend/core/psql_models.py: python manage.py inspectdb"
python frontend/manage.py inspectdb --database=backend user username user_appointment_support_request user_health_event user_screening_alert user_screening user_share expert expert_answer_tag expert_answer user_question daily_user_count > frontend/core/psql_models.py
docker compose $opts down

echo "generate Django static files: frontend/manage.py collectstatic"
export DJANGO_SETTINGS_MODULE=inherit.docker_settings
python frontend/manage.py collectstatic --noinput
