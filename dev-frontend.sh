#!/bin/bash
set -e # stop on error

opts="--project-directory=. --project-name=inherit --file=repository/compose.yaml --file=backend/compose.yaml --file=frontend/compose-db.yaml"
docker compose $opts up --detach
trap "docker compose $opts down" EXIT
source repository/apply.sh
source .venv/bin/activate
export POSTGRES_HOST=localhost
export POSTGRES_USERNAME=postgres
export POSTGRES_PASSWORD=password
export POSTGRES_DATABASE=postgres
export POSTGRES_SSLMODE=disable
export POSTGRES_PORT=5433
export BACKEND_HOST=localhost
export BACKEND_USERNAME=postgres
export BACKEND_PASSWORD=password
export BACKEND_DATABASE=postgres
export BACKEND_SSLMODE=disable
export GOOGLE_CLIENT_ID=318012792699-l64l6qaobsuf77ngvlod65h5n67nu5si.apps.googleusercontent.com
export INHERIT_SUPERUSER_PASSWORD=admin
url="postgresql://${POSTGRES_USERNAME}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DATABASE}?sslmode=${POSTGRES_SSLMODE}"
echo -n "waiting for frontend database to be ready ..."
iteration=0
timeout=30
ready=0
until [[ $ready -eq 1 ]]; do
  if psql $url -c 'SELECT 1;' &> /dev/null; then
    ready=1
  else
    iteration=$(( iteration + 1 ))
    if [[ $iteration -gt $timeout ]]; then
      echo " timed out after $timeout seconds"
      exit 1
    fi
    sleep 1
    echo -n "."
  fi
done
echo " ok"
python frontend/manage.py makemigrations
python frontend/manage.py migrate
python frontend/manage.py createsuperuser_from_env
python frontend/manage.py createinheritteamusers
python frontend/manage.py runserver 127.0.0.1:8001
