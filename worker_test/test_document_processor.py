import pytest
import requests
import time


@pytest.mark.dependency()
def test_process_document(base_url: str, sub: str, session: requests.Session) -> None:
  # get upload url + document_id
  url = f"{base_url}/user/{sub}/document/start"
  response = session.get(url)
  assert response.status_code == 200
  blob_url = response.json()["blob_url"]
  document_id = response.json()["document_id"]
  assert blob_url.startswith("https://")
  # upload file to url
  with open("worker_test/test_document.pdf", mode="rb") as fh:
    response = requests.put(
      blob_url,
      data=fh,
      headers={
        'x-ms-blob-type': 'BlockBlob'
      },
    )
  assert response.status_code == 201
  # finish document creation
  payload = {
    "sub": sub,
    "document_id": document_id,
    "label": document_id,
    "type": "Clinician Letter",
    "mime_type": "application/pdf"
  }
  url = f"{base_url}/user/{sub}/document/finish"
  response = session.post(url, json=payload)
  assert response.status_code == 200
  assert "document_id" in response.json()
  assert document_id == response.json()["document_id"]
  url = f"{base_url}/user/{sub}/document/{document_id}"
  iteration = 0
  while iteration < 30:
    response = session.get(url)
    assert response.status_code == 200
    if response.json()["json_output"] is not None:
      return
    iteration += 1
    time.sleep(1)
  raise TimeoutError("timed out waiting for json_output")
