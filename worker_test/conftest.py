import pytest
import subprocess
import time
from datetime import timedelta, datetime
import jwt
import requests
from typing import Any
from collections.abc import Generator

BASE_URL = "http://localhost:8000"
TEST_SUB = "main-character"  # Pre-generated sub in test data
Session = None


def create_access_token(data: dict[str, Any], lifetime: timedelta = timedelta(seconds=3600)) -> str:
  """Create a signed access token from the provided data"""
  _data = data.copy()
  expires = datetime.now() + lifetime
  _data.update({'exp': expires})
  return jwt.encode(_data, 'foo', algorithm='HS256')  # where 'foo' is the secret key used in the backend


@pytest.fixture(scope="session", autouse=True)
def base_url() -> str:
  return BASE_URL


@pytest.fixture(scope="session", autouse=True)
def sub() -> str:
  """sub for the main test user with pre-generated test data"""
  return TEST_SUB


@pytest.fixture(scope="session")
def session_data() -> dict[str, Any]:
  """session data for the main test user with pre-generated test data"""
  return {}


@pytest.fixture(scope="session", autouse=True)
def session() -> Generator[requests.Session, None, None]:
  """session for the main test user with pre-generated test data"""
  global Session
  Session = requests.Session()
  Session.headers.update({"Authorization": f"Bearer {create_access_token(data={'sub': TEST_SUB})}"})
  yield Session
  Session.close()


@pytest.fixture(scope="session", autouse=True)
def setup_and_teardown() -> Generator[None, None, None]:
  # Start Docker Compose
  subprocess.run([
    "docker",
    "compose",
    "--project-directory=.",
    "--project-name=inherit",
    "--file=repository/compose.yaml",
    "--file=backend/compose.yaml",
    "--file=worker/compose.yaml",
    "up",
    "--detach"
  ], check=True)
  # Wait for the service to be ready
  iteration = 0
  while True:
    try:
      response = requests.get(f"{BASE_URL}/healthz", timeout=1)
      response.raise_for_status()
      print(response.text)
      break
    except (requests.exceptions.ConnectionError, requests.exceptions.HTTPError) as err:
      print(err)
      time.sleep(1)
      iteration += 1
      if iteration > 30:
        raise TimeoutError("Service did not start in time")

  # Apply schema
  subprocess.run([
    "atlas",
    "schema",
    "apply",
    "--auto-approve",
    "--url=postgresql://postgres:password@localhost:5432/postgres?sslmode=disable",
    "--to=file://repository/schema/schema.sql",
    "--dev-url=docker://postgres/16"
  ], check=True)

  # Insert test data
  subprocess.run([
    "psql",
    "-v",
    "ON_ERROR_STOP=1",
    "--file=repository/testdata/testdata.sql",
    "postgresql://postgres:password@localhost:5432/postgres?sslmode=disable"
  ], check=True)

  yield

  subprocess.run(["docker", "logs", "inherit-backend-1"])
  subprocess.run(["docker", "logs", "inherit-worker-1"])

  # Stop Docker Compose
  subprocess.run(["docker", "compose", "--project-name=inherit", "down"], check=True)
