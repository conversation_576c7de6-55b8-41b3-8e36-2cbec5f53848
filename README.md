# Inherit

Inherit backend using [Python 3.12](https://docs.python.org/3.12/), [FastAPI](https://fastapi.tiangolo.com/), [Postgres 16](https://www.postgresql.org/docs/16/index.html), [OpenAPI](https://spec.openapis.org/oas/v3.1.0.html), [sqlc](https://sqlc.dev/), and [Atlas](https://atlasgo.io/). Local runtime uses [Docker](https://docs.docker.com/engine/install/). Cloud runtime in [Azure](https://azure.microsoft.com/en-gb/explore/) tbd.


## Code generation chains

This project makes use of generated code to speed up development and increase quality. See [`docs/codegen`](docs/codegen.md) for the details.


## Repository structure

The below is not exhaustive but gives an overview of where you can find the various components.

| | |
| --- | --- |
| `.github/workflows` | Github Actions workflows - see [docs/cicd](docs/cicd.md) for details
| `api/`              | Generated OpenAPI spec
| `backend/`          | FastAPI backend
| &nbsp;&nbsp;&nbsp;&nbsp;`repository/` | Generated database client and data models
| &nbsp;&nbsp;&nbsp;&nbsp;`routers/`    | API implementation
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;`auth.py` | Token exchange
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;`user.py` | Routes for user domain
| &nbsp;&nbsp;&nbsp;&nbsp;`main.py` | FastAPI entrypoint
| &nbsp;&nbsp;&nbsp;&nbsp;`gen.py`  | OpenAPI spec generator
| `worker/`          | Azure Function worker (Python)
| &nbsp;&nbsp;&nbsp;&nbsp;`repository/` | Generated database client and data models
| &nbsp;&nbsp;&nbsp;&nbsp;`function_app.py` | Main entrypoint
| `infra/` | Azure infrastructure
| &nbsp;&nbsp;&nbsp;&nbsp;`lib/` |
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;`inherit_api/` | Generated API client
| &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;`main.dart`    | Mobile app entrypoint
| `repository/` | Data model - see [docs/repository](docs/repository.md) for details
| &nbsp;&nbsp;&nbsp;&nbsp;`queries/`  | Database queries
| &nbsp;&nbsp;&nbsp;&nbsp;`schema/`   | Database schema
| &nbsp;&nbsp;&nbsp;&nbsp;`testdata/` | Sample data for testing
| `bootstrap.sh`    | Set up Python virtualenv and packages
| `gen.sh `         | Trigger code generation
| `dev-backend.sh`  | Run database in docker, and backend locally on http://localhost:8000/
| `dev-frontend.sh` | Run database and backend in docker, with backend exposed on http://localhost:8000/, and frontend locally on http://localhost:8001/
| `start.sh`        | Run everything in docker, with backend exposed on http://localhost:8000/ and front end on http://localhost/
| `test.sh`         | Run tests


## Get started

* Install [`python3.12`](https://docs.python.org/3.12/), [`sqlc`](https://sqlc.dev/), [`atlas`](https://atlasgo.io/), [`psql`](https://www.postgresql.org/docs/16/app-psql.html), `docker`, and [Azure Functions Core Tools 4](https://github.com/Azure/azure-functions-core-tools)
  * OS X:
    * Install [Homebrew](https://brew.sh), then run `brew install python@3.12 sqlc ariga/tap/atlas postgresql@16 openjdk@17 azure/functions/azure-functions-core-tools@4`
    * [Docker Desktop](https://www.docker.com/products/docker-desktop/) is the simplest way to enable docker, but anything that provides docker is fine
  * Ubuntu:
    * Run `apt-get install python3.12 postgresql-client openjdk-17-jre-headless`
    * Run `snap install sqlc`
    * Run `curl -sSf https://atlasgo.sh | sh`
    * Install [Docker CE](https://docs.docker.com/engine/install/ubuntu/)
    * Install [Azure Functions Core Tools 4](https://github.com/Azure/azure-functions-core-tools/blob/v4.x/README.md#linux)
* Run `bootstrap.sh` to set up your local environment
* Run `dev-backend.sh` to start the database and backend


## Contributing

* Use a meaningful branch name e.g. `feat/my-new-feature`
* Use [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/#summary) where possible
* Open a PR when you're happy with your changes
* Ensure [CI](docs/cicd.md) is passing before merging your PR
* Use squash merge when merging a PR to `main`


## Frontend Guidance
* Login is managed through oauth(Google Accounts only), registration has been disabled. New users need to be added via the admin panel or in the management commands scripts. in order to be able to Login with their inherit email address.

* Access to different areas is managed via decorators on the views. The role of the user is set on the user model. 

* Class based views are used to simplify the code long term, in core/role.py are the subclassed generic views where the role context is added.

* Testing needs to be handled carefully, the main models file core/psql_models is managed via atlas/sqlalchemy. As such they are set to false and Django ignores them when runnign testing. To overcome this the core/runners file converts the models to managed, to allow testing to take place. debugging statements from here provide info on the status. Currently the testing db is retained.

* Ensure that any new project apps are added to the tailwind config to ensure that templates are included correctly.

* The data for the graphs on the home page may become expensive to compute, A redis instance holding the data as a cache will solve for slow page load times. There is a pull request setup with these changes. Any new fucntions or pieces of data will need to be added to the worker.

# Expert Answer View
* Currently Expert users are requried to answer questions, this means they need to have an expert_id value against the user in order to submit.
* Staff users can edit submitted answers if neccesary. To change this the answer view needs to be amended in the form_valid function in the same way that staff are allowed access in the get method