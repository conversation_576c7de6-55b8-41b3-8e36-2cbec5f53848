{"openapi": "3.1.0", "info": {"title": "Inherit API", "version": "0.1.0"}, "paths": {"/token": {"post": {"summary": "<PERSON><PERSON>", "description": "Exchange a valid ID token for an access token. Note that the user may not exist in our database.", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user": {"post": {"summary": "Create User", "operationId": "create_user", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserParams"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/user/{sub}": {"get": {"summary": "Get User", "operationId": "get_user", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete User", "operationId": "delete_user", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/instagram/{instagram_handle}": {"put": {"summary": "Update User Instagram Handle", "operationId": "update_user_instagram_handle", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "instagram_handle", "in": "path", "required": true, "schema": {"type": "string", "title": "Instagram Handle"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/home": {"get": {"summary": "Get User Home", "operationId": "get_user_home", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHome"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/referral": {"get": {"summary": "Get User Referral", "operationId": "get_user_referral", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReferral"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/impact": {"get": {"summary": "Get User Impact", "operationId": "get_user_impact", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserImpact"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/appointment_support_request": {"post": {"summary": "Create Appointment Support Request", "operationId": "create_appointment_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAppointmentSupportRequestParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAppointmentSupportRequest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"summary": "List Appointment Support Request", "operationId": "list_appointment_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserAppointmentSupportRequest"}, "title": "Response List Appointment Support Request User  Sub  Appointment Support Request Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/appointment_support_request/{request_id}": {"get": {"summary": "Get Appointment Support Request", "operationId": "get_appointment_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "request_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Request Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAppointmentSupportRequest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete Appointment Support Request", "operationId": "delete_appointment_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "request_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Request Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAppointmentSupportRequest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/appointment_support_request/{request_id}/rating/{rating}": {"put": {"summary": "Update Appointment Support Request Rating", "operationId": "update_appointment_support_request_rating", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "request_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Request Id"}}, {"name": "rating", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rating"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/document/start": {"get": {"summary": "Start User Document Upload", "operationId": "start_user_document_upload", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartUserDocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/document/finish": {"post": {"summary": "Finish User Document Upload", "operationId": "finish_user_document_upload", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDocumentParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/document": {"get": {"summary": "List User Documents", "operationId": "list_user_documents", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDocument"}, "title": "Response List User Documents User  Sub  Document Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/document/{document_id}": {"get": {"summary": "Get User Document", "operationId": "get_user_document", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete User Document", "operationId": "delete_user_document", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDocument"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/document/type": {"get": {"summary": "List Document Types", "operationId": "list_document_types", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response List Document Types Document Type Get"}}}}}}}, "/user/{sub}/health_event": {"get": {"summary": "List User Health Events", "operationId": "list_user_health_events", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "default": "created_date", "title": "Sort"}}, {"name": "order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserHealthEvent"}, "title": "Response List User Health Events User  Sub  Health Event Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"summary": "Create User Health Event", "operationId": "create_user_health_event", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserHealthEventParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHealthEvent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/health_event/{event_id}": {"get": {"summary": "Get User Health Event", "operationId": "get_user_health_event", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "event_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Event Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHealthEvent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"summary": "Update User Health Event", "operationId": "update_user_health_event", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "event_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Event Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserHealthEventParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHealthEvent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete User Health Event", "operationId": "delete_user_health_event", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "event_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Event Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHealthEvent"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health_event/type": {"get": {"summary": "List Health Event Types", "operationId": "list_health_event_types", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response List Health Event Types Health Event Type Get"}}}}}}}, "/magic": {"get": {"summary": "Get Magic Link", "operationId": "get_magic_link", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string", "title": "Token"}}], "responses": {"200": {"description": "Successful Response", "content": {"text/html": {"schema": {"type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening": {"get": {"summary": "List User Screenings", "operationId": "list_user_screenings", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserScreening"}, "title": "Response List User Screenings User  Sub  Screening Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"summary": "Create User Screening", "operationId": "create_user_screening", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserScreeningParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreening"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening/{screening_id}": {"get": {"summary": "Get User Screening", "operationId": "get_user_screening", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "screening_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Screening Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreening"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"summary": "Update User Screening", "operationId": "update_user_screening", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "screening_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Screening Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserScreeningParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreening"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete User Screening", "operationId": "delete_user_screening", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "screening_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Screening Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreening"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening/{screening_id}/attended": {"post": {"summary": "Update User Screening Attended", "operationId": "update_user_screening_attended", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "screening_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Screening Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserScreeningAttendedParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreening"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening_alert": {"get": {"summary": "List User Screening Alerts", "operationId": "list_user_screening_alerts", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserScreeningAlert"}, "title": "Response List User Screening Alerts User  Sub  Screening Alert Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening_alert/new": {"get": {"summary": "List New User Screening Alerts", "operationId": "list_new_user_screening_alerts", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserScreeningAlert"}, "title": "Response List New User Screening Alerts User  Sub  Screening Alert New Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening_alert/{alert_id}": {"get": {"summary": "Get User Screening Alert", "operationId": "get_user_screening_alert", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScreeningAlert"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/screening_alert/{alert_id}/status/{input_status}": {"put": {"summary": "Update User Screening Alert Status", "operationId": "update_user_screening_alert_status", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}, {"name": "input_status", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/UserScreeningAlertStatus"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share/outgoing": {"get": {"summary": "List Outgoing User Shares", "operationId": "list_outgoing_user_shares", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserShare"}, "title": "Response List Outgoing User Shares User  Sub  Share Outgoing Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share/incoming": {"get": {"summary": "List Incoming User Shares", "operationId": "list_incoming_user_shares", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserShare"}, "title": "Response List Incoming User Shares User  Sub  Share Incoming Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share/{share_id}": {"get": {"summary": "Get User Share", "operationId": "get_user_share", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "share_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Share Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserShare"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete User Share", "operationId": "delete_user_share", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "share_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Share Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserShare"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share": {"post": {"summary": "Create User Share", "operationId": "create_user_share", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserShareParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserShare"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share/{share_id}/approve": {"put": {"summary": "Approve User Share", "operationId": "approve_user_share", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "share_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Share Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/share/{share_id}/label/{label}": {"put": {"summary": "Label User Share", "operationId": "label_user_share", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "share_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Share Id"}}, {"name": "label", "in": "path", "required": true, "schema": {"type": "string", "title": "Label"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey": {"get": {"summary": "List Surveys", "description": "List all surveys the user is eligible to take", "operationId": "list_surveys", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Survey"}, "title": "Response List Surveys User  Sub  Survey Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey/{survey_id}": {"get": {"summary": "Get Survey", "description": "Get a survey by ID", "operationId": "get_survey", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "survey_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Survey Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Survey"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey/{survey_id}/next": {"post": {"summary": "Submit Response", "description": "Accept a response to a survey question and ask another question", "operationId": "submit_response", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "survey_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Survey Id"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/Response"}, {"type": "null"}], "title": "Input"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health": {"get": {"summary": "Get User Survey Response Personal Health", "operationId": "get_user_survey_response_personal_health", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health/1": {"post": {"summary": "Update User Survey Response Personal Health One", "operationId": "update_user_survey_response_personal_health_one", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyPersonalHealthOneParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health/2": {"post": {"summary": "Update User Survey Response Personal Health Two", "operationId": "update_user_survey_response_personal_health_two", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyPersonalHealthTwoParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health/3": {"post": {"summary": "Update User Survey Response Personal Health Three", "operationId": "update_user_survey_response_personal_health_three", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyPersonalHealthThreeParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health/4": {"post": {"summary": "Update User Survey Response Personal Health Four", "operationId": "update_user_survey_response_personal_health_four", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyPersonalHealthFourParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/personal_health/5": {"post": {"summary": "Update User Survey Response Personal Health Five", "operationId": "update_user_survey_response_personal_health_five", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyPersonalHealthFiveParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponsePersonalHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health": {"get": {"summary": "Get User Survey Response Reproductive Health", "operationId": "get_user_survey_response_reproductive_health", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/1": {"post": {"summary": "Update User Survey Response Reproductive Health One", "operationId": "update_user_survey_response_reproductive_health_one", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthOneParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/2": {"post": {"summary": "Update User Survey Response Reproductive Health Two", "operationId": "update_user_survey_response_reproductive_health_two", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthTwoParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/3": {"post": {"summary": "Update User Survey Response Reproductive Health Three", "operationId": "update_user_survey_response_reproductive_health_three", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthThreeParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/4": {"post": {"summary": "Update User Survey Response Reproductive Health Four", "operationId": "update_user_survey_response_reproductive_health_four", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthFourParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/5": {"post": {"summary": "Update User Survey Response Reproductive Health Five", "operationId": "update_user_survey_response_reproductive_health_five", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthFiveParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/reproductive_health/6": {"post": {"summary": "Update User Survey Response Reproductive Health Six", "operationId": "update_user_survey_response_reproductive_health_six", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyReproductiveHealthSixParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseReproductiveHealth"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/lifestyle": {"get": {"summary": "Get User Survey Response Lifestyle", "operationId": "get_user_survey_response_lifestyle", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseLifestyle"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/lifestyle/1": {"post": {"summary": "Update User Survey Response Lifestyle One", "operationId": "update_user_survey_response_lifestyle_one", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyLifestyleOneParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseLifestyle"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/lifestyle/2": {"post": {"summary": "Update User Survey Response Lifestyle Two", "operationId": "update_user_survey_response_lifestyle_two", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyLifestyleTwoParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseLifestyle"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/lifestyle/3": {"post": {"summary": "Update User Survey Response Lifestyle Three", "operationId": "update_user_survey_response_lifestyle_three", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyLifestyleThreeParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseLifestyle"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/survey_response/lifestyle/4": {"post": {"summary": "Update User Survey Response Lifestyle Four", "operationId": "update_user_survey_response_lifestyle_four", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSurveyLifestyleFourParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSurveyResponseLifestyle"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/question_support_request": {"post": {"summary": "Create User Question Support Request", "operationId": "create_user_question_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserQuestionSupportRequestParams"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserQuestionSupportRequest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"summary": "List Question Support Requests", "operationId": "list_question_support_requests", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserQuestionSupportRequest"}, "title": "Response List Question Support Requests User  Sub  Question Support Request Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/question_support_request/{request_id}": {"get": {"summary": "Get Question Support Request", "operationId": "get_question_support_request", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "request_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Request Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserQuestionSupportRequest"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/{sub}/question_support_request/{request_id}/rating/{rating}": {"put": {"summary": "Update Question Support Request Rating", "operationId": "update_question_support_request_rating", "security": [{"HTTPBearer": []}], "parameters": [{"name": "sub", "in": "path", "required": true, "schema": {"type": "string", "title": "Sub"}}, {"name": "request_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Request Id"}}, {"name": "rating", "in": "path", "required": true, "schema": {"type": "integer", "title": "Rating"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/healthz": {"get": {"summary": "Healthz", "operationId": "healthz_healthz_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response Healthz Healthz Get"}}}}}}}, "/app/version": {"get": {"summary": "App Version", "operationId": "app_version_app_version_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "string", "title": "Response App Version App Version Get"}}}}}}}}, "components": {"schemas": {"CreateAppointmentSupportRequestParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "appointment_type": {"type": "string", "title": "Appointment Type"}, "appointment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Appointment Details"}, "rating": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["sub", "appointment_type"], "title": "CreateAppointmentSupportRequestParams"}, "CreateUserHealthEventParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "type": {"$ref": "#/components/schemas/UserHealthEventType"}, "details": {"type": "string", "title": "Details"}, "notes": {"anyOf": [{"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "start_date": {"type": "string", "format": "date", "title": "Start Date"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "End Date"}, "ongoing": {"type": "boolean", "title": "Ongoing"}}, "type": "object", "required": ["sub", "type", "details", "start_date", "ongoing"], "title": "CreateUserHealthEventParams"}, "CreateUserParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "email": {"type": "string", "title": "Email"}, "birth_month": {"type": "integer", "title": "Birth Month"}, "birth_year": {"type": "integer", "title": "Birth Year"}, "sex_assigned_at_birth": {"$ref": "#/components/schemas/UserSexAssignedAtBirth"}, "referrer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["sub", "email", "birth_month", "birth_year", "sex_assigned_at_birth"], "title": "CreateUserParams"}, "CreateUserQuestionSupportRequestParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "question": {"type": "string", "title": "Question"}, "response_output": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response Output"}, "rating": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["sub", "question"], "title": "CreateUserQuestionSupportRequestParams"}, "CreateUserScreeningParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "type": {"$ref": "#/components/schemas/UserScreeningType"}, "subtype": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subtype"}, "next_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Next Date"}, "last_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Last Date"}, "attended_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Attended Date"}, "months_between_appointments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Months Between Appointments"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "status": {"$ref": "#/components/schemas/UserScreeningStatus"}, "user_managed_schedule": {"type": "boolean", "title": "User Managed Schedule"}}, "type": "object", "required": ["sub", "type", "status", "user_managed_schedule"], "title": "CreateUserScreeningParams"}, "CreateUserShareParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "username": {"type": "string", "title": "Username"}, "email": {"type": "string", "title": "Email"}, "sharee_relationship_to_sharer": {"$ref": "#/components/schemas/UserRelationship"}, "label_for_sharer": {"type": "string", "title": "Label For Sharer"}, "label_for_sharee": {"type": "string", "title": "Label For Sharee"}}, "type": "object", "required": ["sub", "username", "email", "sharee_relationship_to_sharer", "label_for_sharer", "label_for_sharee"], "title": "CreateUserShareParams"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HeightWeightUnitType": {"type": "string", "enum": ["Metric", "Imperial"], "title": "HeightWeightUnitType"}, "Question": {"properties": {"question_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Question Id"}, "key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key"}, "question": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Question"}, "type": {"$ref": "#/components/schemas/QuestionType", "default": "none"}, "options": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Options"}}, "type": "object", "title": "Question", "description": "Model for question response"}, "QuestionType": {"type": "string", "enum": ["text", "array", "multiarray", "none"], "title": "QuestionType"}, "Response": {"properties": {"question_id": {"type": "string", "title": "Question Id"}, "value": {"$ref": "#/components/schemas/ResponseValue"}}, "type": "object", "required": ["question_id", "value"], "title": "Response", "description": "Model for question response submission"}, "ResponseValue": {"properties": {"selected_options": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Selected Options"}, "answer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Answer"}}, "type": "object", "title": "ResponseValue"}, "SortOrder": {"type": "string", "enum": ["asc", "desc"], "title": "SortOrder"}, "StartUserDocumentResponse": {"properties": {"document_id": {"type": "string", "title": "Document Id"}, "blob_url": {"type": "string", "title": "Blob Url"}}, "type": "object", "required": ["document_id", "blob_url"], "title": "StartUserDocumentResponse"}, "Survey": {"properties": {"survey_id": {"type": "string", "title": "Survey Id"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "criteria": {"type": "string", "title": "Criteria"}, "progress_percentage": {"type": "number", "title": "Progress Percentage"}, "answered_count": {"type": "integer", "title": "Answered Count"}, "question_count": {"type": "integer", "title": "Question Count"}}, "type": "object", "required": ["survey_id", "title", "description", "criteria", "progress_percentage", "answered_count", "question_count"], "title": "Survey", "description": "Model for survey with progress information"}, "TokenRequest": {"properties": {"id_token": {"type": "string", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["id_token"], "title": "TokenRequest"}, "TokenResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}, "expires_in": {"type": "integer", "title": "Expires In"}}, "type": "object", "required": ["access_token", "token_type", "expires_in"], "title": "TokenResponse"}, "UpdateUserDocumentParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "document_id": {"type": "string", "title": "Document Id"}, "label": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Label"}, "type": {"anyOf": [{"$ref": "#/components/schemas/UserDocumentType"}, {"type": "null"}]}, "mime_type": {"anyOf": [{"$ref": "#/components/schemas/UserDocumentMimeType"}, {"type": "null"}]}}, "type": "object", "required": ["sub", "document_id"], "title": "UpdateUserDocumentParams"}, "UpdateUserHealthEventParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "event_id": {"type": "string", "title": "Event Id"}, "type": {"$ref": "#/components/schemas/UserHealthEventType"}, "details": {"type": "string", "title": "Details"}, "notes": {"anyOf": [{"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "start_date": {"type": "string", "format": "date", "title": "Start Date"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "End Date"}, "ongoing": {"type": "boolean", "title": "Ongoing"}}, "type": "object", "required": ["sub", "event_id", "type", "details", "start_date", "ongoing"], "title": "UpdateUserHealthEventParams"}, "UpdateUserScreeningAttendedParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "screening_id": {"type": "string", "title": "Screening Id"}, "attended_date": {"type": "string", "format": "date", "title": "Attended Date"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["sub", "screening_id", "attended_date"], "title": "UpdateUserScreeningAttendedParams"}, "UpdateUserScreeningParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "screening_id": {"type": "string", "title": "Screening Id"}, "next_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Next Date"}, "last_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Last Date"}, "attended_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "string"}, {"type": "null"}], "title": "Attended Date"}, "months_between_appointments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Months Between Appointments"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "status": {"$ref": "#/components/schemas/UserScreeningStatus"}, "user_managed_schedule": {"type": "boolean", "title": "User Managed Schedule"}}, "type": "object", "required": ["sub", "screening_id", "status", "user_managed_schedule"], "title": "UpdateUserScreeningParams"}, "UpdateUserSurveyLifestyleFourParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "sleep_hours": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sleep Hours"}, "stress_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stress Level"}, "alcohol_frequency": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alcohol Frequency"}, "nicotine_use": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Nicotine Use"}, "nicotine_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "mindfulness_practice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mindfulness Practice"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyLifestyleFourParams"}, "UpdateUserSurveyLifestyleOneParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "general_health": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "General Health"}, "health_vs_last_year": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Health Vs Last Year"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height_weight_unit_type": {"anyOf": [{"$ref": "#/components/schemas/HeightWeightUnitType"}, {"type": "null"}]}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyLifestyleOneParams"}, "UpdateUserSurveyLifestyleThreeParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "special_diet": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Diet"}, "special_diet_other": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Diet Other"}, "regular_diet_quality": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Regular Diet Quality"}, "supplements": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supplements"}, "supplements_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supplements Details"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyLifestyleThreeParams"}, "UpdateUserSurveyLifestyleTwoParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "daily_routine_activity": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Daily Routine Activity"}, "strength_training": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Strength Training"}, "cardio_exercise": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cardio Exercise"}, "brisk_walking": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brisk Walking"}, "hours_sitting_per_day": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Hours Sitting Per Day"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyLifestyleTwoParams"}, "UpdateUserSurveyPersonalHealthFiveParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "disability": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Disability"}, "disability_needs_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Disability Needs Details"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyPersonalHealthFiveParams"}, "UpdateUserSurveyPersonalHealthFourParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "family_health_notes": {"type": "string", "title": "Family Health Notes"}}, "type": "object", "required": ["sub", "family_health_notes"], "title": "UpdateUserSurveyPersonalHealthFourParams"}, "UpdateUserSurveyPersonalHealthOneParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "gp_postcode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gp Postcode"}, "ethnicity": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ethnicity"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender"}, "country": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyPersonalHealthOneParams"}, "UpdateUserSurveyPersonalHealthThreeParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "routine_vaccines": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Routine Vaccines"}, "vaccines": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Vaccines"}, "childhood_vaccinations_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Childhood Vaccinations Status"}, "additional_vaccine_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Additional Vaccine Notes"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyPersonalHealthThreeParams"}, "UpdateUserSurveyPersonalHealthTwoParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "current_health_condition": {"type": "boolean", "title": "Current Health Condition"}, "historic_health_condition": {"type": "boolean", "title": "Historic Health Condition"}, "injuries": {"type": "boolean", "title": "Injuries"}, "allergies": {"type": "boolean", "title": "Allergies"}, "medications": {"type": "boolean", "title": "Medications"}}, "type": "object", "required": ["sub", "current_health_condition", "historic_health_condition", "injuries", "allergies", "medications"], "title": "UpdateUserSurveyPersonalHealthTwoParams"}, "UpdateUserSurveyReproductiveHealthFiveParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "diagnosed_conditions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Diagnosed Conditions"}, "other_conditions_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Other Conditions Details"}, "pcos_screener_irregular_periods": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Irregular Periods"}, "pcos_screener_excessive_hair_growth": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Excessive Hair Growth"}, "pcos_screener_overweight_16_40": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Overweight 16 40"}, "pcos_screener_nipple_discharge": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Nipple Discharge"}, "endopain_period_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Period Pain"}, "endopain_pain_between_periods": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Pain Between Periods"}, "endopain_worsening_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Worsening Pain"}, "endopain_prolonged_period_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Prolonged Period Pain"}, "endopain_stabbing_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON>ain Stabbing Pain"}, "endopain_radiating_back_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Radiating Back Pain"}, "endopain_hip_or_leg_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Hip Or Leg Pain"}, "endopain_limits_daily_activities": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Limits Daily Activities"}, "endopain_disabling_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Disabling Pain"}, "endopain_sexual_severe_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Severe Pain"}, "endopain_sexual_position_specific_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Position Specific Pain"}, "endopain_sexual_interrupts_sex": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Interrupts Sex"}, "endopain_bowel_and_bladder_pain_bowel_movements": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Pain Bowel Movements"}, "endopain_bowel_and_bladder_diarrhoea_constipation": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> And Bladder Diarrhoea Constipation"}, "endopain_bowel_and_bladder_bowel_cramps": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Bowel Cramps"}, "endopain_bowel_and_bladder_urination_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>el And Bladder Urination Pain"}, "endopain_bowel_and_bladder_bladder_discomfort": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Bladder Discomfort"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthFiveParams"}, "UpdateUserSurveyReproductiveHealthFourParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "currently_pregnant": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Pregnant"}, "pregnancies_total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Total"}, "pregnancies_live_births": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Live Births"}, "pregnancies_stillbirths": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Stillbirths"}, "pregnancies_ectopics": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Ectopics"}, "pregnancies_miscarriages": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Miscarriages"}, "pregnancies_terminations": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Terminations"}, "currently_breastfeeding": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Breastfeeding"}, "tried_to_conceive_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tried To Conceive 12 Months"}, "fertility_testing": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fertility Testing"}, "fertility_testing_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fertility Testing Details"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthFourParams"}, "UpdateUserSurveyReproductiveHealthOneParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "reproductive_organs": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reproductive Organs"}, "reproductive_organ_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reproductive Organ Details"}, "reproductive_surgeries": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Reproductive Surgeries"}, "surgery_other_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Surgery Other Details"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthOneParams"}, "UpdateUserSurveyReproductiveHealthSixParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "cycles_irregular_past_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cycles Irregular Past 12 Months"}, "symptoms": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Symptoms"}, "menopause_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menopause Status"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthSixParams"}, "UpdateUserSurveyReproductiveHealthThreeParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "currently_using_contraception": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Using Contraception"}, "current_contraception_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Contraception Details"}, "ever_used_contraception": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ever Used Contraception"}, "past_contraception_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Past Contraception Details"}, "currently_using_hrt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Using Hrt"}, "current_hrt_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Hrt Details"}, "menstrual_status_at_hrt_start": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstrual Status At Hrt Start"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthThreeParams"}, "UpdateUserSurveyReproductiveHealthTwoParams": {"properties": {"sub": {"type": "string", "title": "Sub"}, "ever_menstruated": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> Menstruated"}, "menarche_age": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menarche Age"}, "menstruated_last_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstruated Last 12 Months"}, "last_period_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Period Date"}, "cycle_length": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cycle Length"}, "menstrual_symptoms": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Menstrual Symptoms"}, "menstrual_symptoms_other": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstrual Symptoms Other"}}, "type": "object", "required": ["sub"], "title": "UpdateUserSurveyReproductiveHealthTwoParams"}, "User": {"properties": {"user_id": {"type": "string", "title": "User Id"}, "sub": {"type": "string", "title": "Sub"}, "username": {"type": "string", "title": "Username"}, "email": {"type": "string", "title": "Email"}, "birth_month": {"type": "integer", "title": "Birth Month"}, "birth_year": {"type": "integer", "title": "Birth Year"}, "sex_assigned_at_birth": {"$ref": "#/components/schemas/UserSexAssignedAtBirth"}, "reward_points": {"type": "integer", "title": "Reward Points"}, "reward_point_awarded_for_instagram": {"type": "boolean", "title": "Reward Point Awarded For Instagram"}, "reward_point_awarded_for_personal_health_survey": {"type": "boolean", "title": "Reward Point Awarded For Personal Health Survey"}, "reward_point_awarded_for_reproductive_health_survey": {"type": "boolean", "title": "Reward Point Awarded For Reproductive Health Survey"}, "reward_point_awarded_for_lifestyle_survey": {"type": "boolean", "title": "Reward Point Awarded For Lifestyle Survey"}, "reward_point_awarded_for_sharing": {"type": "boolean", "title": "<PERSON>ward Point Awarded For Sharing"}, "reward_point_awarded_to_referrer": {"type": "boolean", "title": "Reward Point Awarded To Re<PERSON>rer"}, "referral_code": {"type": "string", "title": "Referral Code"}, "referrer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "instagram_handle": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instagram Handle"}, "impact_points": {"type": "integer", "title": "Impact Points"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}, "reward_progress": {"type": "number", "title": "Reward Progress"}}, "type": "object", "required": ["user_id", "sub", "username", "email", "birth_month", "birth_year", "sex_assigned_at_birth", "reward_points", "reward_point_awarded_for_instagram", "reward_point_awarded_for_personal_health_survey", "reward_point_awarded_for_reproductive_health_survey", "reward_point_awarded_for_lifestyle_survey", "reward_point_awarded_for_sharing", "reward_point_awarded_to_referrer", "referral_code", "impact_points", "created_date", "is_deleted", "reward_progress"], "title": "User"}, "UserAppointmentSupportRequest": {"properties": {"request_id": {"type": "string", "title": "Request Id"}, "user_id": {"type": "string", "title": "User Id"}, "response_output": {"type": "string", "title": "Response Output"}, "appointment_type": {"type": "string", "title": "Appointment Type"}, "appointment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Appointment Details"}, "rating": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}}, "type": "object", "required": ["request_id", "user_id", "response_output", "appointment_type", "created_date", "is_deleted"], "title": "UserAppointmentSupportRequest"}, "UserDocument": {"properties": {"document_id": {"type": "string", "title": "Document Id"}, "user_id": {"type": "string", "title": "User Id"}, "label": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Label"}, "type": {"anyOf": [{"$ref": "#/components/schemas/UserDocumentType"}, {"type": "null"}]}, "mime_type": {"anyOf": [{"$ref": "#/components/schemas/UserDocumentMimeType"}, {"type": "null"}]}, "json_output": {"anyOf": [{}, {"type": "null"}], "title": "Json Output"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}}, "type": "object", "required": ["document_id", "user_id", "created_date", "is_deleted"], "title": "UserDocument"}, "UserDocumentMimeType": {"type": "string", "enum": ["application/pdf"], "title": "UserDocumentMimeType"}, "UserDocumentType": {"type": "string", "enum": ["Test Results", "Clinician Letter", "Other"], "title": "UserDocumentType"}, "UserHealthEvent": {"properties": {"event_id": {"type": "string", "title": "Event Id"}, "user_id": {"type": "string", "title": "User Id"}, "type": {"$ref": "#/components/schemas/UserHealthEventType"}, "details": {"type": "string", "title": "Details"}, "notes": {"anyOf": [{}, {"type": "null"}], "title": "Notes"}, "start_date": {"type": "string", "format": "date", "title": "Start Date"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "End Date"}, "ongoing": {"type": "boolean", "title": "Ongoing"}, "genetic": {"type": "boolean", "title": "Genetic"}, "is_reviewed": {"type": "boolean", "title": "Is Reviewed"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}}, "type": "object", "required": ["event_id", "user_id", "type", "details", "start_date", "ongoing", "genetic", "is_reviewed", "created_date", "is_deleted"], "title": "UserHealthEvent"}, "UserHealthEventType": {"type": "string", "enum": ["Diagnosis", "Injury", "Prescription", "Life Event", "Procedure", "Appointment", "Health Update", "Vaccination", "Screening", "Health Check", "Mental Health Event", "Allergy", "Other"], "title": "UserHealthEventType"}, "UserHome": {"properties": {"reward_points": {"type": "integer", "title": "Reward Points"}, "count_outgoing_user_shares": {"type": "integer", "title": "Count Outgoing User Shares"}, "count_new_user_screening_alerts": {"type": "integer", "title": "Count New User Screening Alerts"}, "completion_percentage_personal_health_survey": {"type": "number", "title": "Completion Percentage Personal Health Survey"}, "completion_percentage_reproductive_health_survey": {"type": "number", "title": "Completion Percentage Reproductive Health Survey"}, "completion_percentage_lifestyle_survey": {"type": "number", "title": "Completion Percentage Lifestyle Survey"}, "count_user_referrals": {"type": "integer", "title": "Count User Referrals"}, "impact_points": {"type": "integer", "title": "Impact Points"}, "reward_point_awarded_for_instagram": {"type": "boolean", "title": "Reward Point Awarded For Instagram"}, "instagram_handle": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instagram Handle"}, "reward_progress": {"type": "number", "title": "Reward Progress"}}, "type": "object", "required": ["reward_points", "count_outgoing_user_shares", "count_new_user_screening_alerts", "completion_percentage_personal_health_survey", "completion_percentage_reproductive_health_survey", "completion_percentage_lifestyle_survey", "count_user_referrals", "impact_points", "reward_point_awarded_for_instagram", "instagram_handle", "reward_progress"], "title": "UserHome"}, "UserImpact": {"properties": {"count_research_projects": {"type": "integer", "title": "Count Research Projects"}, "count_research_queries": {"type": "integer", "title": "Count Research Queries"}}, "type": "object", "required": ["count_research_projects", "count_research_queries"], "title": "UserImpact"}, "UserQuestionSupportRequest": {"properties": {"request_id": {"type": "string", "title": "Request Id"}, "user_id": {"type": "string", "title": "User Id"}, "question": {"type": "string", "title": "Question"}, "response_output": {"type": "string", "title": "Response Output"}, "rating": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rating"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}}, "type": "object", "required": ["request_id", "user_id", "question", "response_output", "created_date", "is_deleted"], "title": "UserQuestionSupportRequest"}, "UserReferral": {"properties": {"referral_code": {"type": "string", "title": "Referral Code"}, "count_user_referrals": {"type": "integer", "title": "Count User Referrals"}}, "type": "object", "required": ["referral_code", "count_user_referrals"], "title": "UserReferral"}, "UserRelationship": {"type": "string", "enum": ["Mother", "Father", "Grandmother", "Grandfather", "Daughter", "Son", "Sister", "Brother", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Aunt", "Uncle", "Granddaughter", "<PERSON><PERSON>", "Cousin", "Parent", "Child", "Sibling", "Grandparent", "Grandchild", "<PERSON><PERSON>'s Sibling", "<PERSON><PERSON>'s Child", "Half-Brother", "Half-Sister", "Half-Sibling"], "title": "UserRelationship"}, "UserScreening": {"properties": {"screening_id": {"type": "string", "title": "Screening Id"}, "user_id": {"type": "string", "title": "User Id"}, "type": {"$ref": "#/components/schemas/UserScreeningType"}, "subtype": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subtype"}, "next_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Next Date"}, "last_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Last Date"}, "attended_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Attended Date"}, "months_between_appointments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Months Between Appointments"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "status": {"$ref": "#/components/schemas/UserScreeningStatus"}, "user_managed_schedule": {"type": "boolean", "title": "User Managed Schedule"}, "alert_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}, "label": {"type": "string", "title": "Label"}}, "type": "object", "required": ["screening_id", "user_id", "type", "status", "user_managed_schedule", "created_date", "is_deleted", "label"], "title": "UserScreening"}, "UserScreeningAlert": {"properties": {"alert_id": {"type": "string", "title": "<PERSON><PERSON>"}, "user_id": {"type": "string", "title": "User Id"}, "type": {"$ref": "#/components/schemas/UserScreeningType"}, "subtype": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subtype"}, "next_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Next Date"}, "suggested_months_between_appointments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Suggested Months Between Appointments"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "status": {"$ref": "#/components/schemas/UserScreeningAlertStatus"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}, "label": {"type": "string", "title": "Label"}}, "type": "object", "required": ["alert_id", "user_id", "type", "status", "created_date", "is_deleted", "label"], "title": "UserScreeningAlert"}, "UserScreeningAlertStatus": {"type": "string", "enum": ["New", "<PERSON>rged", "Ignored"], "title": "UserScreeningAlertStatus"}, "UserScreeningStatus": {"type": "string", "enum": ["Not yet attended", "Attended", "Requires update"], "title": "UserScreeningStatus"}, "UserScreeningType": {"type": "string", "enum": ["Cervical Smear", "Bowel Screening", "Mammogram", "<PERSON><PERSON>", "Blood Test", "Blood Pressure", "Consultation", "Abdominal Aortic Aneurysm Screening", "Diabetic Eye Screening", "Routine Health Check", "Lung Cancer Screening", "Other"], "title": "UserScreeningType"}, "UserSexAssignedAtBirth": {"type": "string", "enum": ["Female", "Male", "Intersex"], "title": "UserSexAssignedAtBirth"}, "UserShare": {"properties": {"share_id": {"type": "string", "title": "Share Id"}, "sharer_id": {"type": "string", "title": "Sharer Id"}, "sharee_id": {"type": "string", "title": "Sharee Id"}, "sharee_relationship_to_sharer": {"$ref": "#/components/schemas/UserRelationship"}, "label_for_sharer": {"type": "string", "title": "Label For Sharer"}, "label_for_sharee": {"type": "string", "title": "Label For Sharee"}, "approved": {"type": "boolean", "title": "Approved"}, "approved_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Approved Date"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}, "deleted_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted Date"}, "sharer_relationship_to_sharee": {"$ref": "#/components/schemas/UserRelationship"}}, "type": "object", "required": ["share_id", "sharer_id", "sharee_id", "sharee_relationship_to_sharer", "label_for_sharer", "label_for_sharee", "approved", "created_date", "is_deleted", "sharer_relationship_to_sharee"], "title": "UserShare"}, "UserSurveyResponseLifestyle": {"properties": {"response_id": {"type": "string", "title": "Response Id"}, "user_id": {"type": "string", "title": "User Id"}, "general_health": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "General Health"}, "health_vs_last_year": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Health Vs Last Year"}, "height": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Height"}, "weight": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight"}, "height_weight_unit_type": {"anyOf": [{"$ref": "#/components/schemas/HeightWeightUnitType"}, {"type": "null"}]}, "daily_routine_activity": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Daily Routine Activity"}, "strength_training": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Strength Training"}, "cardio_exercise": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cardio Exercise"}, "brisk_walking": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Brisk Walking"}, "hours_sitting_per_day": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Hours Sitting Per Day"}, "special_diet": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Diet"}, "special_diet_other": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Diet Other"}, "regular_diet_quality": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Regular Diet Quality"}, "supplements": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supplements"}, "supplements_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supplements Details"}, "sleep_hours": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sleep Hours"}, "stress_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stress Level"}, "alcohol_frequency": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alcohol Frequency"}, "nicotine_use": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Nicotine Use"}, "nicotine_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "mindfulness_practice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mindfulness Practice"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}}, "type": "object", "required": ["response_id", "user_id", "created_date"], "title": "UserSurveyResponseLifestyle"}, "UserSurveyResponsePersonalHealth": {"properties": {"response_id": {"type": "string", "title": "Response Id"}, "user_id": {"type": "string", "title": "User Id"}, "gp_postcode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gp Postcode"}, "ethnicity": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ethnicity"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender"}, "country": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country"}, "current_health_condition": {"type": "boolean", "title": "Current Health Condition"}, "historic_health_condition": {"type": "boolean", "title": "Historic Health Condition"}, "injuries": {"type": "boolean", "title": "Injuries"}, "allergies": {"type": "boolean", "title": "Allergies"}, "medications": {"type": "boolean", "title": "Medications"}, "routine_vaccines": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Routine Vaccines"}, "vaccines": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Vaccines"}, "childhood_vaccinations_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Childhood Vaccinations Status"}, "additional_vaccine_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Additional Vaccine Notes"}, "family_health_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Family Health Notes"}, "disability": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Disability"}, "disability_needs_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Disability Needs Details"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}}, "type": "object", "required": ["response_id", "user_id", "current_health_condition", "historic_health_condition", "injuries", "allergies", "medications", "created_date"], "title": "UserSurveyResponsePersonalHealth"}, "UserSurveyResponseReproductiveHealth": {"properties": {"response_id": {"type": "string", "title": "Response Id"}, "user_id": {"type": "string", "title": "User Id"}, "reproductive_organs": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reproductive Organs"}, "reproductive_organ_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reproductive Organ Details"}, "reproductive_surgeries": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Reproductive Surgeries"}, "surgery_other_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Surgery Other Details"}, "ever_menstruated": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> Menstruated"}, "menarche_age": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menarche Age"}, "menstruated_last_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstruated Last 12 Months"}, "last_period_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Period Date"}, "cycle_length": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cycle Length"}, "menstrual_symptoms": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Menstrual Symptoms"}, "menstrual_symptoms_other": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstrual Symptoms Other"}, "currently_using_contraception": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Using Contraception"}, "current_contraception_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Contraception Details"}, "ever_used_contraception": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ever Used Contraception"}, "past_contraception_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Past Contraception Details"}, "currently_using_hrt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Using Hrt"}, "current_hrt_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Hrt Details"}, "menstrual_status_at_hrt_start": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menstrual Status At Hrt Start"}, "currently_pregnant": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Pregnant"}, "pregnancies_total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Total"}, "pregnancies_live_births": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Live Births"}, "pregnancies_stillbirths": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Stillbirths"}, "pregnancies_ectopics": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Ectopics"}, "pregnancies_miscarriages": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Miscarriages"}, "pregnancies_terminations": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pregnancies Terminations"}, "currently_breastfeeding": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currently Breastfeeding"}, "tried_to_conceive_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tried To Conceive 12 Months"}, "fertility_testing": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fertility Testing"}, "fertility_testing_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Fertility Testing Details"}, "diagnosed_conditions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Diagnosed Conditions"}, "other_conditions_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Other Conditions Details"}, "pcos_screener_irregular_periods": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Irregular Periods"}, "pcos_screener_excessive_hair_growth": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Excessive Hair Growth"}, "pcos_screener_overweight_16_40": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Overweight 16 40"}, "pcos_screener_nipple_discharge": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Pcos Screener Nipple Discharge"}, "endopain_period_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Period Pain"}, "endopain_pain_between_periods": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Pain Between Periods"}, "endopain_worsening_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Worsening Pain"}, "endopain_prolonged_period_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Prolonged Period Pain"}, "endopain_stabbing_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON>ain Stabbing Pain"}, "endopain_radiating_back_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Radiating Back Pain"}, "endopain_hip_or_leg_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Hip Or Leg Pain"}, "endopain_limits_daily_activities": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Limits Daily Activities"}, "endopain_disabling_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Disabling Pain"}, "endopain_sexual_severe_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Severe Pain"}, "endopain_sexual_position_specific_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Position Specific Pain"}, "endopain_sexual_interrupts_sex": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Endopain Sexual Interrupts Sex"}, "endopain_bowel_and_bladder_pain_bowel_movements": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Pain Bowel Movements"}, "endopain_bowel_and_bladder_diarrhoea_constipation": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> And Bladder Diarrhoea Constipation"}, "endopain_bowel_and_bladder_bowel_cramps": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Bowel Cramps"}, "endopain_bowel_and_bladder_urination_pain": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>el And Bladder Urination Pain"}, "endopain_bowel_and_bladder_bladder_discomfort": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON> Bowel And Bladder Bladder Discomfort"}, "cycles_irregular_past_12_months": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cycles Irregular Past 12 Months"}, "symptoms": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Symptoms"}, "menopause_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Menopause Status"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date"}}, "type": "object", "required": ["response_id", "user_id", "created_date"], "title": "UserSurveyResponseReproductiveHealth"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "HTTPBasic": {"type": "http", "scheme": "basic"}}}}