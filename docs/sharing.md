# Sharing

Users can share data with family members. This sequence diagram shows how the process works.

```mermaid
sequenceDiagram
  Sharer->>API: I want to share with this person
  API->>DB: Store the unapproved share
  API->>Sharer: Here is the unapproved share
  API->>Sharee: (email or in-app) Do you want to approve this share?
  Sharee->>API: Yes, I approve
  API->>DB: Change the share to approved
  API->>Worker: Process this share
```

```mermaid
sequenceDiagram
  when a share is created or when a user_health_event is created or changed
  assess whether a new screening alert should be created for connected users
```
