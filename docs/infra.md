# Infrastructure

## Bootstrap

The `bootstrap` module in `infra/bootstrap` creates:
- The `terraform` Azure resource group, which contains the storage account for the state of the other Terraform modules (`core`, `nonproduction`, etc.)
- Workload identity federation between Github Actions and Azure.

If you need to make changes to `bootstrap`, you will need the following installed:
- Install [`terraform`](https://developer.hashicorp.com/terraform/install).
- Install [`az`](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-linux?pivots=apt)

The state for `bootstrap` is stored in this repository. Make sure you commit and push the new state file as part of your change. Make sure the state does not contain any sensitive data e.g. key material.


## Core

The `core` module in `infra/core` creates the `core` resource group, which contains an Azure Container Registry to store container images built by the CD pipeline.

Changes to `core` will be deployed by the CD pipeline. See [CICD](./cicd.md) for details.


## Nonproduction

The `nonproduction` module in `infra/nonproduction` uses the [`azure`](../infra/modules/azure/) module to create the `nonproduction-backend` resource group, which contains:
- An Azure Container App to run the `backend` API service
- An Azure Postgresql database for the `backend` API service
- An Azure Storage account, currently not used

Changes to `nonproduction` will be deployed by the CD pipeline. See [CICD](./cicd.md) for details.


## Production

There is currently no `production` module. When we create it, it will use the [`azure`](../infra/modules/azure/) module so that our environments have identical infrastructure. We will likely need to modify the module to account for variations in sizing between the environments.
