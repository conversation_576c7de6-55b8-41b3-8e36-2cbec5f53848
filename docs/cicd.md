# CI/CD

## CI

The CI pipeline defined in [`.github/workflows/ci.yml`](../.github/workflows/ci.yml) checks the following:
- The `backend` service passes `flake8` and can be built into a Docker image
- The `mobile` app passes tests and can be built for Android
- #TODO: iOS builds
- All generated code is up to date (see [codegen](./codegen.md))

These tests should all be passing before code is merged to `main`. Note that currently on the Github free plan the branch protection rule cannot actually be enforced.

## CD

The CD pipeline defined in [`.github/workflows/cd.yml`](../.github/workflows/cd.yml) is triggered by pushes to `main` (i.e. when a PR is merged) and triggers a deployment to the Azure nonproduction environment.
