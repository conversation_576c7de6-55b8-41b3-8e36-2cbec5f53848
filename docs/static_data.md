# Static data

The following application data is pre-generated by `repository/static/gen.py`:
- Usernames: each username is a sequence of 5 numbers between 1 and 25 formatted like "1-2-3-4-5"
- Referral codes: each referral code is a sequence of 3 words from `repository/static/wordlist.json` formatted like "apple-banana-carrot"

The pseudorandom generator uses a fixed seed so the generated data will remain consistent between runs. The first 500 lines are captured in `repository/testdata/generated.sql` for use in local and nonproduction environments.
