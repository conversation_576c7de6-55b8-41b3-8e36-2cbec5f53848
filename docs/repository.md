# Repository schema

This document lays out naming conventions and approaches that we should use throughout Inherit systems.


## API verbs

The following verbs should be used consistently across the database and API layers.

| Verb | Usage
| --- | ---
| get | Get one specific thing e.g. get the details of a single user
| list | Get one or more things that match a criteria e.g. get the sharing relationships for a particular user
| update | Change one specific thing e.g. modify a user
| delete | Delete one specific thing e.g. delete a user


## Soft deletion

Instead of deleting rows from database tables using `DELETE FROM`, we perform a "soft delete" using `UPDATE "<table>" SET "is_deleted" = TRUE, "deleted_on" = now()`. This has some advantages: for example it means we don't break any foreign key relationships. It also has some disadvantages: we have to be careful about how and when we use `UNIQUE` constraints if a record might be deleted and later created again.

The main things to keep in mind are:
- When returning data, we should always add `"is_deleted" = FALSE` to the `WHERE` clause (unless we specifically want to deal with deleted rows)
- If a column or set of columns should be unique, add `WHERE "is_deleted" = FALSE` to the index specification so that "deleted" rows don't prevent the creation of new rows
