# Document Upload

Users can upload documents. This sequence diagram shows how the process works.


```mermaid
sequenceDiagram
  User->>API: I want to upload a document
  API->>User: Upload the document to Azure Storage using this URL
  User->>Azure Storage: Upload the document
  User->>API: I have uploaded the document
  API->>Azure Storage: Do you have the document?
  Azure Storage->>API: Yes
  API->>DB: Store metadata about the document
  API->>Worker: (via queue) Process the document
  Worker->>OpenAI: Extract the document text
  OpenAI->>Worker: Here is the document text
  Worker->>OpenAI: Interpret the document text
  OpenAI->>Worker: Here is structured data based on the document
  Worker->>DB: Store the structured data
```
