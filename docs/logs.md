# Viewing API logs

You can view live logs from the backend API using the [Log Stream](https://portal.azure.com/#@phoebeplee.onmicrosoft.com/resource/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/nonproduction-backend/providers/Microsoft.App/containerApps/nonproduction-backend-api/logstream) feature of the Container App in Azure.

You can view older logs using the [Logs](https://portal.azure.com/#@phoebeplee.onmicrosoft.com/resource/subscriptions/adb3dfb9-7c0f-4f79-b9b7-9abe1c7cf054/resourceGroups/nonproduction-backend/providers/Microsoft.App/containerApps/nonproduction-backend-api/logs) feature. You can use the following query to see only the log messages and timestamps:
```
ContainerAppConsoleLogs_CL | project time_t, Log_s | order by time_t asc 
```
