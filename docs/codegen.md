# Generated Code

This project makes use of generated code to speed up development and increase quality.

## Generators
### sqlc

Firstly, [sqlc](https://sqlc.dev/) takes the database schema and queries in [`repository`](../repository/) and generates a Python database client and [Pydantic](https://docs.pydantic.dev/latest/) models in [`backend/repository`](../backend/repository/). 

### FastAPI

The `backend` service is based on [FastAPI](https://fastapi.tiangolo.com/). We can use the database client produced by sqlc to query the database using type-safe methods. The data models are automatically used by FastAPI to validate data in API requests and responses.

FastAPI can also generate an OpenAPI specification to match our implementation. This means we end up with an OpenAPI specification that reflects both the data model defined in the database and the API implementation we write in `backend`. The helper script [`backend/gen.py`](../backend/gen.py) uses FastAPI to generate [`api/spec.json`](../api/spec.json).

### openapi-generator-cli

Finally, the [openapi-generator-cli](https://openapi-generator.tech/docs/installation#pypi) tool generates a Dart client for the API in [`mobile/lib/inherit_api`](../mobile/lib/inherit_api/), which we can use in the Flutter mobile app.

## Considerations

### Database schema

The generated Dart client does not handle optional parameters correctly. As a result, we need all columns in our database schema to be `NOT NULL`; we can instead use `DEFAULT` to provide an initial value where required.

### OpenAPI tweaks

The default spec generated by FastAPI includes a `HTTPValidationResponse` type that results in invalid code in the generated Dart client. There is code in [`backend/gen.py`](../backend/gen.py) which removes the relevant responses and schemas from the OpenAPI spec. This can be removed when the bug in the generator is fixed (if that ever happens...).
