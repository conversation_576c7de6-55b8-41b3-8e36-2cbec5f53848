#!/bin/bash
set -e # stop on error
export TOKEN_AUDIENCE=beep
export TOKEN_ISSUER=boop
opts="--project-directory=. --project-name=inherit --file=repository/compose.yaml --file=backend/compose.yaml"
docker compose $opts up --detach
trap "docker compose $opts down" EXIT
source repository/apply.sh
source .venv/bin/activate
pushd worker
export AzureWebJobsStorage=$AZURE_STORAGE_CONNECTION_STRING
func start --port 7071
popd
