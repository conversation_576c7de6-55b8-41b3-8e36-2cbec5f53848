---
type: "always_apply"
---

# API

To serve our API we are using FastAPI. The code for this is in the backend directory. If you make changes, you must run the following script to refresh the OpenAPI spec:
```
export SECRET_KEY=foo
export DOCS_PASSWORD=bar
export TOKEN_ISSUER=beep
export TOKEN_AUDIENCE=boop
python -m backend.gen
```

# Database

Our database is Postgres and we're using sqlc to generate the database client. The database schema is located in repository/schema/schema.sql. The queries are located in repository/queries/queries.sql. If you make changes to these files, run the following script in the workspace root to refresh the generated clients:
```
sqlc generate --file=repository/sqlc.yaml
repository/fix.py backend/repository/*.py
repository/fix.py worker/repository/*.py
```

