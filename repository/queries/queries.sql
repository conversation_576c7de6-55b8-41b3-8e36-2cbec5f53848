-------
-- User
-------

-- name: ValidUser :one
SELECT
  "sub"
FROM
  "user"
WHERE
  "sub" = $1
  AND is_deleted = FALSE;

-- name: GetUserBySub :one
SELECT
  *
FROM
  "user"
WHERE
  "sub" = $1
  AND "is_deleted" = FALSE;

-- name: GetUserById :one
SELECT
  *
FROM
  "user"
WHERE
  "user_id" = $1
  AND "is_deleted" = FALSE;

-- name: GetUserByReferralCode :one
SELECT
  *
FROM
  "user"
WHERE
  "referral_code" = $1
  AND "is_deleted" = FALSE;

-- name: CreateUser :one
INSERT INTO "user" (
  "sub",
  "username",
  "email",
  "birth_month",
  "birth_year",
  "sex_assigned_at_birth",
  "referral_code",
  "referrer"
)
VALUES (
  $1,
  $2,
  $3,
  $4,
  $5,
  $6,
  $7,
  $8
) RETURNING *;

-- name: DeleteUser :one
UPDATE
  "user"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "sub" = $1
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AssignUsername :one
UPDATE
  "username"
SET
  "assigned" = TRUE
WHERE
  "username" = (SELECT "username" FROM "username" WHERE "assigned" = FALSE LIMIT 1)
RETURNING *;

-- name: UpdateUserInstagramHandle :one
UPDATE
  "user"
SET
  "instagram_handle" = $2
WHERE
  "sub" = $1
  AND "is_deleted" = FALSE
RETURNING *;

-- name: CountUserReferrals :one
SELECT
  COUNT(*)
FROM
  "user"
WHERE
  "referrer" = (SELECT "referral_code" FROM "user" AS "referrer" WHERE "referrer"."sub" = $1 AND "is_deleted" = FALSE)
  AND "reward_point_awarded_for_personal_health_survey" = TRUE
  AND "reward_point_awarded_for_reproductive_health_survey" = TRUE
  AND "reward_point_awarded_for_lifestyle_survey" = TRUE
  AND "is_deleted" = FALSE;

------------
-- UserShare
------------

-- name: CreateUserShare :one
INSERT INTO "user_share" (
  "sharer_id",
  "sharee_id",
  "sharee_relationship_to_sharer",
  "label_for_sharer",
  "label_for_sharee"
)
VALUES (
  (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = $1 AND "is_deleted" = FALSE),
  (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."username" = $2 AND "sharee"."email" = $3 AND "is_deleted" = FALSE),
  $4,
  $5,
  $6
)
RETURNING *;

-- name: ApproveUserShare :one
UPDATE
  "user_share"
SET
  "approved" = TRUE,
  "approved_date" = now()
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "share_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: MagicLinkApproveUserShare :one
UPDATE
  "user_share"
SET
  "approved" = TRUE,
  "approved_date" = now()
WHERE
  "share_id" = $1
  AND "is_deleted" = FALSE
RETURNING *;

-- name: UpdateUserShareSharerLabel :one
UPDATE
  "user_share"
SET
  "label_for_sharer" = $3
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "share_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: UpdateUserShareShareeLabel :one
UPDATE
  "user_share"
SET
  "label_for_sharee" = $3
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "share_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: GetUserShare :one
SELECT
  *
FROM
  "user_share"
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = $1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = $1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "share_id" = $2
  AND "is_deleted" = FALSE;

-- name: ListOutgoingUserShares :many
SELECT
  *
FROM
  "user_share"
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: CountOutgoingUserShares :one
SELECT
  COUNT(*)
FROM
  "user_share"
WHERE
  "sharer_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: ListIncomingUserShares :many
SELECT
  *
FROM
  "user_share"
WHERE
  "sharee_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "user_share"."is_deleted" = FALSE;

-- name: DeleteUserShare :one
UPDATE
  "user_share"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = $1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = $1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "share_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: DeleteAllUserShares :many
UPDATE
  "user_share"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  (
    "sharer_id" = (SELECT "user_id" FROM "user" AS "sharer" WHERE "sharer"."sub" = $1 AND "sharer"."is_deleted" = FALSE)
    OR "sharee_id" = (SELECT "user_id" FROM "user" AS "sharee" WHERE "sharee"."sub" = $1 AND "sharee"."is_deleted" = FALSE)
  )
  AND "is_deleted" = FALSE
RETURNING *;

-- name: WorkerListSharesBySharerId :many
SELECT
  *
FROM
  "user_share"
WHERE
  "sharer_id" = $1
  AND "approved" = TRUE
  AND "is_deleted" = FALSE;

----------------
-- UserScreening
----------------

-- name: ListUserScreenings :many
SELECT
  *
FROM
  "user_screening"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: GetUserScreening :one
SELECT
  *
FROM
  "user_screening"
WHERE
  "screening_id" = $1
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $2 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: ListMatchingScreenings :many
SELECT
  *
FROM
  "user_screening"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "type" = $2
  AND ("subtype" = $3 OR ("subtype" IS NULL AND $3 IS NULL))
  AND "status" = $4
  AND "is_deleted" = FALSE;

-- name: CreateUserScreening :one
INSERT INTO "user_screening" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "last_date",
  "attended_date",
  "months_between_appointments",
  "notes",
  "status",
  "user_managed_schedule",
  "alert_id"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
  $2,
  $3,
  $4,
  $5,
  $6,
  $7,
  $8,
  $9,
  $10,
  $11
) RETURNING *;

-- name: UpdateUserScreening :one
UPDATE
  "user_screening"
SET
  "next_date" = $3,
  "last_date" = $4,
  "attended_date" = $5,
  "months_between_appointments" = $6,
  "notes" = $7,
  "status" = $8,
  "user_managed_schedule" = $9
WHERE
  "screening_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING *;

-- name: UpdateUserScreeningAttended :one
UPDATE
  "user_screening"
SET
  "attended_date" = $3,
  "notes" = $4,
  "status" = $5
WHERE
  "screening_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING *;

-- name: DeleteUserScreening :one
UPDATE
  "user_screening"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "screening_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

---------------------
-- UserScreeningAlert
---------------------

-- name: CreateUserScreeningAlert :one
INSERT INTO "user_screening_alert" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "suggested_months_between_appointments",
  "notes"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
  $2,
  $3,
  $4,
  $5,
  $6
) RETURNING *;

-- name: WorkerCreateUserScreeningAlert :one
INSERT INTO "user_screening_alert" (
  "user_id",
  "type",
  "subtype",
  "next_date",
  "suggested_months_between_appointments",
  "notes"
)
VALUES (
  $1,
  $2,
  $3,
  $4,
  $5,
  $6
) RETURNING *;

-- name: ListUserScreeningAlerts :many
SELECT
  *
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: ListNewUserScreeningAlerts :many
SELECT
  *
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "status" = 'New'
  AND "is_deleted" = FALSE;

-- name: CountNewUserScreeningAlerts :one
SELECT
  COUNT(*)
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "status" = 'New'
  AND "is_deleted" = FALSE;

-- name: GetUserScreeningAlert :one
SELECT
  *
FROM
  "user_screening_alert"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "alert_id" = $2
  AND "is_deleted" = FALSE;

-- name: UpdateUserScreeningAlertStatus :one
UPDATE
  "user_screening_alert"
SET
  "status" = $3
WHERE 
  "alert_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING *;

------------------
-- UserHealthEvent
------------------

-- name: CreateUserHealthEvent :one
INSERT INTO "user_health_event" (
  "user_id",
  "type",
  "details",
  "notes",
  "start_date",
  "end_date",
  "ongoing",
  "genetic"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
  $2,
  $3,
  $4,
  $5,
  $6,
  $7,
  $8
) RETURNING *;

-- name: ListUserHealthEvents :many
SELECT
  *
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: WorkerListUserHealthEvents :many
SELECT
  *
FROM
  "user_health_event"
WHERE
  "user_id" = $1
  AND "is_deleted" = FALSE;

-- name: GetUserHealthEvent :one
SELECT
  *
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "event_id" = $2
  AND "is_deleted" = FALSE;

-- name: ListUserHealthEventsByType :many
SELECT
  *
FROM
  "user_health_event"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "type" = $2
  AND "is_deleted" = FALSE;

-- name: UpdateUserHealthEvent :one
UPDATE
  "user_health_event"
SET
  "type" = $3,
  "details" = $4,
  "notes" = $5,
  "start_date" = $6,
  "end_date" = $7,
  "ongoing" = $8
WHERE 
  "event_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
RETURNING *;

-- name: DeleteUserHealthEvent :one
UPDATE
  "user_health_event"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "event_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

---------------
-- UserDocument
---------------

-- name: CreateUserDocument :one
INSERT INTO "user_document" (
  "user_id"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
) RETURNING *;

-- name: UpdateUserDocument :one
UPDATE
  "user_document"
SET 
  "label" = $3,
  "type" = $4,
  "mime_type" = $5
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "document_id" = $2
  AND "label" IS NULL
  AND "is_deleted" = FALSE
RETURNING *;

-- name: GetUserDocument :one
SELECT
  *
FROM
  "user_document"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "document_id" = $2
  AND "is_deleted" = FALSE;

-- name: ListUserDocuments :many
SELECT
  *
FROM
  "user_document"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "label" IS NOT NULL
  AND "is_deleted" = FALSE;

-- name: DeleteUserDocument :one
UPDATE
  "user_document"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "document_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: WorkerGetUserDocumentById :one
SELECT
  *
FROM
  "user_document"
WHERE
  "document_id" = $1
  AND "is_deleted" = FALSE;

-- name: WorkerUpdateUserDocumentById :one
UPDATE
  "user_document"
SET
  "json_output" = $2
WHERE
  "document_id" = $1
  AND "is_deleted" = FALSE
RETURNING "document_id";

----------------------------
-- AppointmentSupportRequest
----------------------------

-- name: ListSharedHealthEvents :many
SELECT
  "user_health_event".*,
  "user_share".*
FROM
  "user_health_event"
  INNER JOIN "user_share" ON "user_health_event"."user_id" = "user_share"."sharer_id"
WHERE
  "user_share"."sharee_id" = (SELECT "user_id" FROM "user" WHERE "user"."sub" = $1 AND "user"."is_deleted" = FALSE)
  AND "user_health_event"."genetic" = TRUE
  AND "user_share"."is_deleted" = FALSE
  AND "user_health_event"."is_deleted" = FALSE;

-- name: WorkerListSharedHealthEvents :many
SELECT
  "user_health_event".*,
  "user_share".*
FROM
  "user_health_event"
  INNER JOIN "user_share" ON "user_health_event"."user_id" = "user_share"."sharer_id"
WHERE
  "user_share"."sharee_id" = $1
  AND "user_health_event"."genetic" = TRUE
  AND "user_share"."is_deleted" = FALSE
  AND "user_health_event"."is_deleted" = FALSE;

-- name: GetAppointmentSupportRequest :one
SELECT
  *
FROM
  "user_appointment_support_request"
WHERE
  "request_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: ListUserAppointmentSupportRequests :many
SELECT
  *
FROM
  "user_appointment_support_request"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
ORDER BY
  "created_date" DESC;

-- name: CreateAppointmentSupportRequest :one
INSERT INTO
  "user_appointment_support_request" (
    "user_id",
    "response_output",
    "appointment_type",
    "appointment_details",
    "rating"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5
  ) RETURNING *;

-- name: UpdateAppointmentSupportRequestRating :one
UPDATE
  "user_appointment_support_request"
SET
  "rating" = $3
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "request_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-- name: DeleteAppointmentSupportRequest :one
UPDATE
  "user_appointment_support_request"
SET
  "is_deleted" = TRUE,
  "deleted_date" = now()
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "request_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

----------
-- Surveys
----------

-- Personal Health Survey

-- name: GetUserSurveyResponsePersonalHealth :one
SELECT
  *
FROM
  "user_survey_response_personal_health"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE);

-- name: UpdateUserSurveyPersonalHealthOne :one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "gp_postcode",
    "ethnicity",
    "gender",
    "country"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "gp_postcode" = EXCLUDED."gp_postcode",
  "ethnicity" = EXCLUDED."ethnicity",
  "country" = EXCLUDED."country",
  "gender" = EXCLUDED."gender"
RETURNING *;

-- name: UpdateUserSurveyPersonalHealthTwo :one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "current_health_condition",
    "historic_health_condition",
    "injuries",
    "allergies",
    "medications"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "current_health_condition" = EXCLUDED."current_health_condition",
  "historic_health_condition" = EXCLUDED."historic_health_condition",
  "injuries" = EXCLUDED."injuries",
  "allergies" = EXCLUDED."allergies",
  "medications" = EXCLUDED."medications"
RETURNING *;

-- name: UpdateUserSurveyPersonalHealthThree :one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "routine_vaccines",
    "vaccines",
    "childhood_vaccinations_status",
    "additional_vaccine_notes"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "routine_vaccines" = EXCLUDED."routine_vaccines",
  "vaccines" = EXCLUDED."vaccines",
  "childhood_vaccinations_status" = EXCLUDED."childhood_vaccinations_status",
  "additional_vaccine_notes" = EXCLUDED."additional_vaccine_notes"
RETURNING *;

-- name: UpdateUserSurveyPersonalHealthFour :one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "family_health_notes"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "family_health_notes" = EXCLUDED."family_health_notes"
RETURNING *;

-- name: UpdateUserSurveyPersonalHealthFive :one
INSERT INTO
  "user_survey_response_personal_health" (
    "user_id",
    "disability",
    "disability_needs_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "disability" = EXCLUDED."disability",
  "disability_needs_details" = EXCLUDED."disability_needs_details"
RETURNING *;


-- Reproductive Health Survey

-- name: GetUserSurveyResponseReproductiveHealth :one
SELECT
  *
FROM
  "user_survey_response_reproductive_health"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE);

-- name: UpdateUserSurveyReproductiveHealthOne :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "reproductive_organs",
    "reproductive_organ_details",
    "reproductive_surgeries",
    "surgery_other_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "reproductive_organs" = EXCLUDED."reproductive_organs",
  "reproductive_organ_details" = EXCLUDED."reproductive_organ_details",
  "reproductive_surgeries" = EXCLUDED."reproductive_surgeries",
  "surgery_other_details" = EXCLUDED."surgery_other_details"
RETURNING *;

-- name: UpdateUserSurveyReproductiveHealthTwo :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "ever_menstruated",
    "menarche_age",
    "menstruated_last_12_months",
    "last_period_date",
    "cycle_length",
    "menstrual_symptoms",
    "menstrual_symptoms_other"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "ever_menstruated" = EXCLUDED."ever_menstruated",
  "menarche_age" = EXCLUDED."menarche_age",
  "menstruated_last_12_months" = EXCLUDED."menstruated_last_12_months",
  "last_period_date" = EXCLUDED."last_period_date",
  "cycle_length" = EXCLUDED."cycle_length",
  "menstrual_symptoms" = EXCLUDED."menstrual_symptoms",
  "menstrual_symptoms_other" = EXCLUDED."menstrual_symptoms_other"
RETURNING *;

-- name: UpdateUserSurveyReproductiveHealthThree :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "currently_using_contraception",
    "current_contraception_details",
    "ever_used_contraception",
    "past_contraception_details",
    "currently_using_hrt",
    "current_hrt_details",
    "menstrual_status_at_hrt_start"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "currently_using_contraception" = EXCLUDED."currently_using_contraception",
  "current_contraception_details" = EXCLUDED."current_contraception_details",
  "ever_used_contraception" = EXCLUDED."ever_used_contraception",
  "past_contraception_details" = EXCLUDED."past_contraception_details",
  "currently_using_hrt" = EXCLUDED."currently_using_hrt",
  "current_hrt_details" = EXCLUDED."current_hrt_details",
  "menstrual_status_at_hrt_start" = EXCLUDED."menstrual_status_at_hrt_start"
RETURNING *;

-- name: UpdateUserSurveyReproductiveHealthFour :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "currently_pregnant",
    "pregnancies_total",
    "pregnancies_live_births",
    "pregnancies_stillbirths",
    "pregnancies_ectopics",
    "pregnancies_miscarriages",
    "pregnancies_terminations",
    "currently_breastfeeding",
    "tried_to_conceive_12_months",
    "fertility_testing",
    "fertility_testing_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "currently_pregnant" = EXCLUDED."currently_pregnant",
  "pregnancies_total" = EXCLUDED."pregnancies_total",
  "pregnancies_live_births" = EXCLUDED."pregnancies_live_births",
  "pregnancies_stillbirths" = EXCLUDED."pregnancies_stillbirths",
  "pregnancies_ectopics" = EXCLUDED."pregnancies_ectopics",
  "pregnancies_miscarriages" = EXCLUDED."pregnancies_miscarriages",
  "pregnancies_terminations" = EXCLUDED."pregnancies_terminations",
  "currently_breastfeeding" = EXCLUDED."currently_breastfeeding",
  "tried_to_conceive_12_months" = EXCLUDED."tried_to_conceive_12_months",
  "fertility_testing" = EXCLUDED."fertility_testing",
  "fertility_testing_details" = EXCLUDED."fertility_testing_details"
RETURNING *;

-- name: UpdateUserSurveyReproductiveHealthFive :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "diagnosed_conditions",
    "other_conditions_details",
    "pcos_screener_irregular_periods",
    "pcos_screener_excessive_hair_growth",
    "pcos_screener_overweight_16_40",
    "pcos_screener_nipple_discharge",
    "endopain_period_pain",
    "endopain_pain_between_periods",
    "endopain_worsening_pain",
    "endopain_prolonged_period_pain",
    "endopain_stabbing_pain",
    "endopain_radiating_back_pain",
    "endopain_hip_or_leg_pain",
    "endopain_limits_daily_activities",
    "endopain_disabling_pain",
    "endopain_sexual_severe_pain",
    "endopain_sexual_position_specific_pain",
    "endopain_sexual_interrupts_sex",
    "endopain_bowel_and_bladder_pain_bowel_movements",
    "endopain_bowel_and_bladder_diarrhoea_constipation",
    "endopain_bowel_and_bladder_bowel_cramps",
    "endopain_bowel_and_bladder_urination_pain",
    "endopain_bowel_and_bladder_bladder_discomfort"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14,
    $15,
    $16,
    $17,
    $18,
    $19,
    $20,
    $21,
    $22,
    $23,
    $24
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "diagnosed_conditions" = EXCLUDED."diagnosed_conditions",
  "other_conditions_details" = EXCLUDED."other_conditions_details",
  "pcos_screener_irregular_periods" = EXCLUDED."pcos_screener_irregular_periods",
  "pcos_screener_excessive_hair_growth" = EXCLUDED."pcos_screener_excessive_hair_growth",
  "pcos_screener_overweight_16_40" = EXCLUDED."pcos_screener_overweight_16_40",
  "pcos_screener_nipple_discharge" = EXCLUDED."pcos_screener_nipple_discharge",
  "endopain_period_pain" = EXCLUDED."endopain_period_pain",
  "endopain_pain_between_periods" = EXCLUDED."endopain_pain_between_periods",
  "endopain_worsening_pain" = EXCLUDED."endopain_worsening_pain",
  "endopain_prolonged_period_pain" = EXCLUDED."endopain_prolonged_period_pain",
  "endopain_stabbing_pain" = EXCLUDED."endopain_stabbing_pain",
  "endopain_radiating_back_pain" = EXCLUDED."endopain_radiating_back_pain",
  "endopain_hip_or_leg_pain" = EXCLUDED."endopain_hip_or_leg_pain",
  "endopain_limits_daily_activities" = EXCLUDED."endopain_limits_daily_activities",
  "endopain_disabling_pain" = EXCLUDED."endopain_disabling_pain",
  "endopain_sexual_severe_pain" = EXCLUDED."endopain_sexual_severe_pain",
  "endopain_sexual_position_specific_pain" = EXCLUDED."endopain_sexual_position_specific_pain",
  "endopain_sexual_interrupts_sex" = EXCLUDED."endopain_sexual_interrupts_sex",
  "endopain_bowel_and_bladder_pain_bowel_movements" = EXCLUDED."endopain_bowel_and_bladder_pain_bowel_movements",
  "endopain_bowel_and_bladder_diarrhoea_constipation" = EXCLUDED."endopain_bowel_and_bladder_diarrhoea_constipation",
  "endopain_bowel_and_bladder_bowel_cramps" = EXCLUDED."endopain_bowel_and_bladder_bowel_cramps",
  "endopain_bowel_and_bladder_urination_pain" = EXCLUDED."endopain_bowel_and_bladder_urination_pain",
  "endopain_bowel_and_bladder_bladder_discomfort" = EXCLUDED."endopain_bowel_and_bladder_bladder_discomfort"
RETURNING *;


-- name: UpdateUserSurveyReproductiveHealthSix :one
INSERT INTO
  "user_survey_response_reproductive_health" (
    "user_id",
    "cycles_irregular_past_12_months",
    "symptoms",
    "menopause_status"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "cycles_irregular_past_12_months" = EXCLUDED."cycles_irregular_past_12_months",
  "symptoms" = EXCLUDED."symptoms",
  "menopause_status" = EXCLUDED."menopause_status"
RETURNING *;

-- Lifestyle Survey

-- name: GetUserSurveyResponseLifestyle :one
SELECT
  *
FROM
  "user_survey_response_lifestyle"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE);

-- name: UpdateUserSurveyLifestyleOne :one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "general_health",
    "health_vs_last_year",
    "height",
    "weight",
    "height_weight_unit_type"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "general_health" = EXCLUDED."general_health",
  "health_vs_last_year" = EXCLUDED."health_vs_last_year",
  "height" = EXCLUDED."height",
  "weight" = EXCLUDED."weight",
  "height_weight_unit_type" = EXCLUDED."height_weight_unit_type"
RETURNING *;

-- name: UpdateUserSurveyLifestyleTwo :one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "daily_routine_activity",
    "strength_training",
    "cardio_exercise",
    "brisk_walking",
    "hours_sitting_per_day"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "daily_routine_activity" = EXCLUDED."daily_routine_activity",
  "strength_training" = EXCLUDED."strength_training",
  "cardio_exercise" = EXCLUDED."cardio_exercise",
  "brisk_walking" = EXCLUDED."brisk_walking",
  "hours_sitting_per_day" = EXCLUDED."hours_sitting_per_day"
RETURNING *;

-- name: UpdateUserSurveyLifestyleThree :one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "special_diet",
    "special_diet_other",
    "regular_diet_quality",
    "supplements",
    "supplements_details"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "special_diet" = EXCLUDED."special_diet",
  "special_diet_other" = EXCLUDED."special_diet_other",
  "regular_diet_quality" = EXCLUDED."regular_diet_quality",
  "supplements" = EXCLUDED."supplements",
  "supplements_details" = EXCLUDED."supplements_details"
RETURNING *;

-- name: UpdateUserSurveyLifestyleFour :one
INSERT INTO
  "user_survey_response_lifestyle" (
    "user_id",
    "sleep_hours",
    "stress_level",
    "alcohol_frequency",
    "nicotine_use",
    "nicotine_details",
    "mindfulness_practice"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4,
    $5,
    $6,
    $7
  )
ON CONFLICT (user_id) DO UPDATE
SET
  "sleep_hours" = EXCLUDED."sleep_hours",
  "stress_level" = EXCLUDED."stress_level",
  "alcohol_frequency" = EXCLUDED."alcohol_frequency",
  "nicotine_use" = EXCLUDED."nicotine_use",
  "nicotine_details" = EXCLUDED."nicotine_details",
  "mindfulness_practice" = EXCLUDED."mindfulness_practice"
RETURNING *;

----------------
-- Reward Points
----------------

-- name: CreateUserRewardPointLog :one
INSERT INTO
  "user_reward_point_log" (
    "user_id",
    "points",
    "reason"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3
  ) RETURNING *;

-- name: AddUserRewardPoint :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1
WHERE
  "sub" = $1
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AddUserRewardPointForInstagram :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_instagram" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_for_instagram" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AddUserRewardPointForPersonalHealthSurvey :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_personal_health_survey" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_for_personal_health_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AddUserRewardPointForReproductiveHealthSurvey :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_reproductive_health_survey" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_for_reproductive_health_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AddUserRewardPointForLifestyleSurvey :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_lifestyle_survey" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_for_lifestyle_survey" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;

-- name: AddUserRewardPointForSharing :one
UPDATE
  "user"
SET
  "reward_points" = "reward_points" + 1,
  "reward_point_awarded_for_sharing" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_for_sharing" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;


-- name: MarkUserRewardPointForReferral :one
UPDATE
  "user"
SET
  "reward_point_awarded_to_referrer" = TRUE
WHERE
  "sub" = $1
  AND "reward_point_awarded_to_referrer" = FALSE
  AND "is_deleted" = FALSE
RETURNING *;


--------------
-- Magic Links
--------------

-- name: CreateMagicLink :one
INSERT INTO
  "magic_link" (
    "token",
    "metadata"
  )
VALUES
  (
    $1,
    $2
  ) RETURNING *;

-- name: GetMagicLink :one
SELECT
  *
FROM
  "magic_link"
WHERE
  "token" = $1;

-------------------
-- Daily User Count
-------------------
-- name: CreateDailyUserCount :one
SELECT 
    @date::date AS date,
    @d_new_user_count::integer AS d_new_user_count,
    @d_total_user_count::integer AS d_total_user_count,
    @d_growth::numeric AS d_growth,
    @wk_new_user_count::integer AS wk_new_user_count,
    @wk_total_user_count::integer AS wk_cur_total_user_count,
    @wk_growth::numeric AS wk_growth,
    @mth_new_user_count::integer AS mth_new_user_count,
    @mth_total_user_count::integer AS mth_cur_total_user_count,
    @mth_growth::numeric AS mth_growth;
    
-- name: InsertDailyUserCount :one
INSERT INTO
  "daily_user_count" (
    "date",
    "d_new_user_count",
    "d_total_user_count",
    "d_growth_rate",
    "wk_new_user_count",
    "wk_growth_rate",
    "wk_total_user_count",
    "mth_new_user_count",
    "mth_total_user_count",
    "mth_growth_rate"
  )
VALUES
  (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10
  ) RETURNING *;

-- name: CountActiveUsersByDate :one
SELECT
  COUNT(*)
FROM
  "user"
WHERE
  "created_date" < @start_date::timestamp
  AND "is_deleted" = FALSE;

-- name: GetLastProcessedDate :one
SELECT MAX(date) AS last_date
FROM "daily_user_count";

-- name: CountUsersForDate :one
SELECT COUNT(user_id) AS user_count
FROM "user"
WHERE "created_date" >= @start_date::timestamp
  AND "created_date" < @end_date::timestamp
  AND "is_deleted" = FALSE;

---------
-- Impact
---------

-- name: ImpactFemale :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Female' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user" WHERE "sex_assigned_at_birth" = 'Female') AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Female' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

-- name: ImpactLiveBirths :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'LiveBirths' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "pregnancies_live_births" > 0) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'LiveBirths' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

-- name: ImpactMiscarriages :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Miscarriages' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "pregnancies_miscarriages" > 0) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Miscarriages' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

-- name: ImpactPCOS :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PCOS' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_health_event" WHERE "type" = 'Diagnosis' AND lower("details") IN ('pcos', 'polycystic ovary syndrome', 'poly-cystic ovary syndrome')) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PCOS' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

-- name: ImpactEndometriosis :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'Endometriosis' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_health_event" WHERE "type" = 'Diagnosis' AND lower("details") IN ('endometriosis')) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'Endometriosis' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

-- name: ImpactTryingToConceive :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'TryingToConceive' as "project",
  1 as "points"
FROM (SELECT "user_id" FROM "user_survey_response_reproductive_health" WHERE "tried_to_conceive_12_months" = 'Yes') AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'TryingToConceive' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project

--and then PCOS equals false but PCOS screener is 75% or greater, same for endometriosis
-- name: ImpactPossibleUndiagnosedPCOS :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PossibleUndiagnosedPCOS' as "project",
  1 as "points"
FROM (
  SELECT "user_id"
  FROM "user_survey_response_reproductive_health"
  WHERE (
    coalesce("pcos_screener_irregular_periods", false)::int +
    coalesce("pcos_screener_excessive_hair_growth", false)::int +
    coalesce("pcos_screener_overweight_16_40", false)::int +
    coalesce("pcos_screener_nipple_discharge", false)::int
  ) >= 3 -- At least 75% of the PCOS screener questions answered positively
) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PossibleUndiagnosedPCOS' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project


-- name: ImpactPossibleUndiagnosedEndometriosis :exec
INSERT INTO "user_impact_log" (
  "user_id",
  "project",
  "points"
)
SELECT 
  "base"."user_id",
  'PossibleUndiagnosedEndometriosis' as "project",
  1 as "points"
FROM (
  SELECT "user_id"
  FROM "user_survey_response_reproductive_health"
  WHERE (
    coalesce("endopain_period_pain", false)::int +
    coalesce("endopain_pain_between_periods", false)::int +
    coalesce("endopain_worsening_pain", false)::int +
    coalesce("endopain_prolonged_period_pain", false)::int +
    coalesce("endopain_stabbing_pain", false)::int +
    coalesce("endopain_radiating_back_pain", false)::int +
    coalesce("endopain_hip_or_leg_pain", false)::int +
    coalesce("endopain_limits_daily_activities", false)::int +
    coalesce("endopain_disabling_pain", false)::int +
    coalesce("endopain_sexual_severe_pain", false)::int +
    coalesce("endopain_sexual_position_specific_pain", false)::int +
    coalesce("endopain_sexual_interrupts_sex", false)::int +
    coalesce("endopain_bowel_and_bladder_pain_bowel_movements", false)::int +
    coalesce("endopain_bowel_and_bladder_diarrhoea_constipation", false)::int +
    coalesce("endopain_bowel_and_bladder_bowel_cramps", false)::int +
    coalesce("endopain_bowel_and_bladder_urination_pain", false)::int +
    coalesce("endopain_bowel_and_bladder_bladder_discomfort", false)::int
  ) > 12 -- At least 75% of the Endometriosis screener questions answered positively
) AS "base" -- The table containing the criteria
LEFT JOIN "user_impact_log" ON -- Join on user_id filtered for the relevant project
  "base"."user_id" = "user_impact_log"."user_id" 
  AND "user_impact_log"."project" = 'PossibleUndiagnosedEndometriosis' 
WHERE "user_impact_log"."project" IS NULL; -- Exclude users who already have the points for this project


-- name: ImpactUpdatePoints :exec
UPDATE "user"
SET "impact_points" = (SELECT coalesce(sum("points"), 0) FROM "user_impact_log" WHERE "user_impact_log"."user_id" = "user"."user_id");


-- name: GetUserDOB :many
SELECT
  "birth_year",
  "birth_month"

FROM 
  "user"
WHERE
  "is_deleted" = FALSE;

-- name: GetDailyUserCount :many
SELECT
  *
FROM
  "daily_user_count"
WHERE
  "date" <= $1;

-- name: CreateQuestionSupportRequest :one
INSERT INTO
  "user_question_support_request" (
    "user_id",
    "question",
    "response_output",
    "rating"
  )
VALUES
  (
    (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
    $2,
    $3,
    $4
  ) RETURNING *;

-- name: GetQuestionSupportRequest :one
SELECT
  *
FROM
  "user_question_support_request"
WHERE
  "request_id" = $2
  AND "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE;

-- name: ListQuestionSupportRequests :many
SELECT
  *
FROM
  "user_question_support_request"
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "is_deleted" = FALSE
ORDER BY
  "created_date" DESC;

-- name: UpdateQuestionSupportRequestRating :one
UPDATE
  "user_question_support_request"
SET
  "rating" = $3
WHERE
  "user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND "request_id" = $2
  AND "is_deleted" = FALSE
RETURNING *;

-------
-- Survey
-------

-- name: GetAllSurveys :many
SELECT
  s.*
FROM
  "survey" s
WHERE
  s."is_deleted" = FALSE
ORDER BY s."created_date" DESC;

-- name: GetSurveyById :one
SELECT
  s.*
FROM
  "survey" s
WHERE
  s."survey_id" = $1
  AND s."is_deleted" = FALSE;

-- name: GetSurveyQuestions :many
SELECT
  q.*
FROM
  "question" q
  JOIN "survey_question" sq ON q."question_id" = sq."question_id"
WHERE
  sq."survey_id" = $1
  AND q."is_deleted" = FALSE
ORDER BY q."created_date";

-- name: GetUserSurveyProgress :one
SELECT
  COUNT(DISTINCT usr."question_id") as answered_count,
  COUNT(DISTINCT sq."question_id") as total_count
FROM
  "survey_question" sq
  LEFT JOIN "user_survey_response" usr ON sq."question_id" = usr."question_id"
    AND usr."is_deleted" = FALSE
    AND usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $2 AND "is_deleted" = FALSE)
WHERE
  sq."survey_id" = $1;

-- name: GetUnansweredQuestionsForSurvey :many
SELECT
  q.*
FROM
  "question" q
  JOIN "survey_question" sq ON q."question_id" = sq."question_id"
  LEFT JOIN "user_survey_response" usr ON q."question_id" = usr."question_id"
    AND usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $2 AND "is_deleted" = FALSE)
    AND usr."is_deleted" = FALSE
WHERE
  sq."survey_id" = $1
  AND q."is_deleted" = FALSE
  AND usr."response_id" IS NULL
ORDER BY q."created_date";

-- name: CreateUserSurveyResponse :one
INSERT INTO "user_survey_response" (
  "user_id",
  "survey_id",
  "question_id",
  "selected_options",
  "answer"
)
VALUES (
  (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE),
  $2,
  $3,
  $4,
  $5
) RETURNING *;

-- name: GetUserResponsesForSurvey :many
SELECT
  usr.*
FROM
  "user_survey_response" usr
WHERE
  usr."user_id" = (SELECT "user_id" FROM "user" WHERE "sub" = $1 AND "is_deleted" = FALSE)
  AND usr."survey_id" = $2
  AND usr."is_deleted" = FALSE
ORDER BY usr."created_date";

-- name: GetQuestionById :one
SELECT
  q.*
FROM
  "question" q
WHERE
  q."question_id" = $1
  AND q."is_deleted" = FALSE;

-- name: ValidSurveyQuestion :one
SELECT
  q.*
FROM
  "question" q
  INNER JOIN "survey_question" sq ON q."question_id" = sq."question_id"
WHERE
  sq."survey_id" = $1
  AND sq."question_id" = $2;
