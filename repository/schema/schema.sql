-- This table stores pregenerated usernames and referral codes, see docs/static_data.md
CREATE TABLE "username" (
  "username" text NOT NULL PRIMARY KEY,
  "referral_code" text NOT NULL UNIQUE,
  "assigned" boolean NOT NULL DEFAULT FALSE
);

-- Main user table
CREATE TYPE "user_sex_assigned_at_birth" AS ENUM ('Female', 'Male', 'Intersex');
CREATE TYPE "user_country_of_residence" AS ENUM ('England', 'Scotland', 'Wales', 'Northern Ireland', 'Other');

CREATE TYPE "user_ethnicity" AS ENUM (
  'White - English, Welsh, Scottish, Northern Irish, or British',
  'White - Irish',
  'White - Gypsy or Irish Traveller',
  'White - Roma',
  'Any other White background',
  'Mixed or Multiple Ethnic Groups - White and Black Caribbean',
  'Mixed or Multiple Ethnic Groups - White and Black African',
  'Mixed or Multiple Ethnic Groups - White and Asian',
  'Any other Mixed or Multiple ethnic background',
  'Asian or Asian British - Indian',
  'Asian or Asian British - Pakistani',
  'Asian or Asian British - Bangladeshi',
  'Asian or Asian British - Chinese',
  'Any other Asian background',
  'Black, Black British, Caribbean, or African - Caribbean',
  'Black, Black British, Caribbean, or African - African',
  'Any other Black, Black British, or Caribbean background',
  'Other Ethnic Group - Arab',
  'Any other ethnic group'
);

CREATE TYPE "user_risk_factors" AS ENUM('Diabetes Diagnosis', 'History of Smoking', 'Family History of Type 2 Diabetes', 'History of Gestational Diabetes', 'Hypertension', 'PCOS', 'Prediabetes', 'History of Heart Disease', 'History of Stroke');
CREATE TABLE "user" (
  "user_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "sub" text NOT NULL, -- Identifier from Firebase
  "username" text NOT NULL UNIQUE, -- Username must be unique i.e. usernames cannot be reused
  "email" text NOT NULL,
  "birth_month" int NOT NULL CHECK ("birth_month" BETWEEN 1 AND 12),
  "birth_year" int NOT NULL,
  "sex_assigned_at_birth" user_sex_assigned_at_birth NOT NULL,
  "reward_points" int NOT NULL DEFAULT 0,
  "reward_point_awarded_for_instagram" boolean NOT NULL DEFAULT FALSE,
  "reward_point_awarded_for_personal_health_survey" boolean NOT NULL DEFAULT FALSE,
  "reward_point_awarded_for_reproductive_health_survey" boolean NOT NULL DEFAULT FALSE,
  "reward_point_awarded_for_lifestyle_survey" boolean NOT NULL DEFAULT FALSE,
  "reward_point_awarded_for_sharing" boolean NOT NULL DEFAULT FALSE,
  "reward_point_awarded_to_referrer" boolean NOT NULL DEFAULT FALSE,
  "referral_code" text NOT NULL UNIQUE,
  "referrer" text NULL,
  "instagram_handle" text NULL,
  "impact_points" int NOT NULL DEFAULT 0,
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);
-- These indexes express that sub/email must be unique for active (i.e. not deleted) users
CREATE UNIQUE INDEX ON "user" ("sub") WHERE "is_deleted" = FALSE;
CREATE UNIQUE INDEX ON "user" ("email") WHERE "is_deleted" = FALSE;

-- Log of reward points awarded to or spent by users
CREATE TYPE "user_reward_point_log_reason" AS ENUM ('Instagram', 'Personal Health Survey', 'Reproductive Health Survey', 'Lifestyle Survey', 'Survey', 'Sharing', 'Referral', 'Being Referred', 'Redemption');
CREATE TABLE "user_reward_point_log" (
  "log_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"),
  "points" int NOT NULL,
  "reason" user_reward_point_log_reason NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Sharing relationships between users
CREATE TYPE "user_relationship" AS ENUM ('Mother', 'Father', 'Grandmother', 'Grandfather', 'Daughter', 'Son', 'Sister', 'Brother', 'Niece', 'Nephew', 'Aunt', 'Uncle', 'Granddaughter', 'Grandson', 'Cousin', 'Parent', 'Child', 'Sibling', 'Grandparent', 'Grandchild', 'Parent''s Sibling', 'Sibling''s Child', 'Half-Brother', 'Half-Sister', 'Half-Sibling');
CREATE TABLE "user_share" (
  "share_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "sharer_id" text NOT NULL REFERENCES "user" ("user_id"),
  "sharee_id" text NOT NULL REFERENCES "user" ("user_id"),
  "sharee_relationship_to_sharer" user_relationship NOT NULL, -- relationship of the sharee to the sharer (e.g. daughter)
  "label_for_sharer" text NOT NULL, -- label entered by sharer so they can identify the sharee in the app (e.g. annabelle)
  "label_for_sharee" text NOT NULL, -- label entered by sharer and displayed to sharee (e.g. mum)
  "approved" boolean NOT NULL DEFAULT FALSE, -- indicates whether the sharee has approved the sharing
  "approved_date" timestamp NULL, -- indicates when the sharee approved the sharing
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp DEFAULT NULL
);
-- This index expresses that any active sharing relationship must be unique
CREATE UNIQUE INDEX ON "user_share" ("sharer_id", "sharee_id") WHERE "is_deleted" = FALSE;

-- Documents uploaded by users. Most fields are nullable so that placeholders can be created during document upload.
CREATE TYPE "user_document_type" AS ENUM ('Test Results', 'Clinician Letter', 'Other');
CREATE TYPE "user_document_mime_type" AS ENUM ('application/pdf');
CREATE TABLE "user_document" (
  "document_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "label" text NULL, -- User provided name for easy identification
  "type" user_document_type NULL, -- User provided document type
  "mime_type" user_document_mime_type NULL, -- User provided mime type
  "json_output" jsonb NULL, -- JSON output for structured data (TODO generate health events from document)
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);
-- This index expresses that document labels must be unique for a user
CREATE UNIQUE INDEX ON "user_document" ("user_id", "label") WHERE "is_deleted" = FALSE;

-- Health events TODO are these system generated, user generated, or both?
CREATE TYPE "user_health_event_type" AS ENUM ('Diagnosis', 'Injury', 'Prescription', 'Life Event', 'Procedure', 'Appointment', 'Health Update', 'Vaccination', 'Screening', 'Health Check', 'Mental Health Event', 'Allergy', 'Other');
CREATE TABLE "user_health_event" (
  "event_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "type" user_health_event_type NOT NULL,
  "details" text NOT NULL, -- Look up from type or free text entry if 'other' 
  "notes" jsonb NULL, -- Optional extra notes
  "start_date" date NOT NULL, -- Start date of the event
  "end_date" date NULL, -- End date (if applicable)
  "ongoing" boolean NOT NULL DEFAULT FALSE,
  "genetic" boolean NOT NULL DEFAULT FALSE, -- If true, this event is genetic and can be shared with family members
  "is_reviewed" boolean NOT NULL DEFAULT FALSE, -- If true, this event has been reviewed by a health expert
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Health updates TODO describe these
CREATE TABLE "user_health_update" (
  "update_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "text" text NOT NULL, -- Free-text input or output from document upload.
  "json_output" jsonb NULL, -- JSON-formatted structured data where a health event has been identified by AI (creates Health Event row)
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- User Screening Type is shared between alerts and screenings
CREATE TYPE "user_screening_type" AS ENUM ('Cervical Smear', 'Bowel Screening', 'Mammogram', 'Scan', 'Blood Test', 'Blood Pressure', 'Consultation', 'Abdominal Aortic Aneurysm Screening', 'Diabetic Eye Screening', 'Routine Health Check', 'Lung Cancer Screening', 'Other');

-- User Screening Alerts (created when family members alert of genetic illness or when user flags something that suggests new screening schedule)
CREATE TYPE "user_screening_alert_status" AS ENUM ('New', 'Merged', 'Ignored');
CREATE TABLE "user_screening_alert" (
  "alert_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "type" user_screening_type NOT NULL, 
  "subtype" text NULL, -- Optional, user can pick from UI suggestions (e.g. blood test types when 'Blood Test' is selected)
  "next_date" date NULL, -- Date of next screening
  "suggested_months_between_appointments" int NULL, -- Number of months between appointments (for recurring screenings)
  "notes" text NULL, -- Additional user notes
  "status" user_screening_alert_status NOT NULL DEFAULT 'New',
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);
CREATE UNIQUE INDEX ON "user_screening_alert" ("user_id", "type", "subtype", "status") WHERE "is_deleted" = FALSE;

-- Screening schedules e.g women aged 25 to 49 typically have cervical screening every 36 months
-- Some screening schedules are created by the system
-- Users can add their own, or modify the schedule of the system-generated ones
-- When an appointment is attended, status is set to Attended AND a new screening record is created for the next date according to the schedule
CREATE TYPE "user_screening_status" AS ENUM ('Not yet attended', 'Attended', 'Requires update');
CREATE TABLE "user_screening" (
  "screening_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "type" user_screening_type NOT NULL, 
  "subtype" text NULL, -- Optional, user can pick from UI suggestions (e.g. blood test types when 'Blood Test' is selected)
  "next_date" date NULL, -- Date of next screening
  "last_date" date NULL, -- Date of last screening
  "attended_date" date NULL, -- Date the screening was attended
  "months_between_appointments" int NULL, -- Number of months between appointments (for recurring screenings)
  "notes" text NULL, -- Additional user notes
  "status" user_screening_status NOT NULL,
  "user_managed_schedule" boolean NOT NULL DEFAULT FALSE, --Don't automatically update months_between_appointments
  "alert_id" text NULL REFERENCES "user_screening_alert" ("alert_id"), -- The alert this screening was created from, if any
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);
-- This index expresses that screening names must be unique for a given user. 
-- For example this allows for a user to have more than one blood test but not 2 cervical smears for instance. 
-- Name = Type and Type 2 (Blood test - cholesterol; and Blood test - folate))
CREATE UNIQUE INDEX ON "user_screening" ("user_id", "type", "subtype") WHERE "status" = 'Not yet attended' AND "is_deleted" = FALSE;


-- Requests from user for support with medical appointments
CREATE TABLE "user_appointment_support_request" (
  "request_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "response_output" text NOT NULL, -- Copy from the GPT output
  "appointment_type" text NOT NULL, -- User sets the primary reason for their appointment
  "appointment_details" text NULL, -- User can provide additional details about the appointment
  "rating" int NULL CHECK ("rating" BETWEEN 1 AND 5), -- Optional rating given by user at bottom of page
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Main expert table
CREATE TABLE "expert" (
  "expert_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "sub" text NOT NULL, -- Identifier from Firebase
  "name" text NOT NULL,
  "qualifications" text NOT NULL, -- The qualifications the expert holds e.g. MD, PhD, DO
  "email" text NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);
-- These indexes express that sub/email must be unique for active (i.e. not deleted) users
CREATE UNIQUE INDEX ON "expert" ("sub") WHERE "is_deleted" = FALSE;
CREATE UNIQUE INDEX ON "expert" ("email") WHERE "is_deleted" = FALSE;

-- Tags created by experts to group answers
CREATE TABLE "expert_answer_tag" (
  "tag_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "expert_id" text NOT NULL REFERENCES "expert" ("expert_id"),
  "tag_name" text NOT NULL, -- Name of the tag
  "created_date" TIMESTAMP NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Answers created by health experts
CREATE TABLE "expert_answer" (
  "answer_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "expert_id" text NOT NULL REFERENCES "expert" ("expert_id"),
  "question_text" text NOT NULL,
  "answer_text" text NOT NULL,
  "parent_answer_id" text NULL REFERENCES "expert_answer" ("answer_id"), --Nullable, not always linked but this denotes a sub question
  "position_in_tree" integer NOT NULL DEFAULT 1, -- Position in list of Qs and sub Qs
  "answer_tag" text NULL REFERENCES "expert_answer_tag" ("tag_id"), -- Tag assigned to the answer by the expert
  "created_date" TIMESTAMP NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp 
);

-- Questions asked by users
CREATE TYPE "user_question_moderation_status" AS ENUM ('Pending', 'Approved', 'Answered', 'Rejected');
CREATE TABLE "user_question" (
  "question_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"),
  "question_text" text NOT NULL,
  "moderation_status" user_question_moderation_status NOT NULL, -- Assumption: questions become visible to other users for voting after approval
  "votes" int NOT NULL DEFAULT 1, -- Once approved, other users can vote for the question to be answered
  "answer_id" text NULL REFERENCES "expert_answer" ("answer_id"), -- Link to expert_answer that answers this question
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp 
);

-- User Survey Responses

-- {
--   "survey_id": "string (UUID)",
--   "survey_type": "Personal Health Survey",
--   "child_surveys": [
CREATE TABLE "user_survey_response_personal_health" (
  "response_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL UNIQUE REFERENCES "user" ("user_id"), -- Relates to User table
-- -- API CALL 1 (BASIC INFO):
-- {
--       "survey_name": "Basic Info",
--       "notes": {
--         "gp_postcode": "string",
--         "ethnicity": "string",
--         "gender": "string",
--         "country": "string"
--       }
--     },
  "gp_postcode" text NULL,
  "ethnicity" text NULL, -- TODO use user_ethnicity
  "gender" text NULL,
  "country" text NULL, --TODO use user_country_of_residence
-- --API CALL 2 (HEALTH EVENTS):
--     {
--       "survey_name": "Health Events",
--       "notes": {
--         "current_health_condition": null,
--         "historic_health_condition": "true",
--         "injuries": "true",
--         "allergies": "true",
--         "medications": "false"
--       }
--     },
  "current_health_condition" boolean NOT NULL DEFAULT FALSE, -- Indicates if the user has completed the current health condition section, NOT whether they have a current health condition
  "historic_health_condition" boolean NOT NULL DEFAULT FALSE, -- Indicates if the user has completed the historic health conditions section, NOT whether they have historic health conditions
  "injuries" boolean NOT NULL DEFAULT FALSE, -- Indicates if the user has completed the injuries section, NOT whether they have injuries
  "allergies" boolean NOT NULL DEFAULT FALSE, -- Indicates if the user has completed the allergies section, NOT whether they have allergies
  "medications" boolean NOT NULL DEFAULT FALSE, -- Indicates if the user has completed the medications section, NOT whether they take medications
-- --API CALL 3 (VACCINATIONS):
-- {
--       "survey_name": "Vaccination History",
--       "notes": {
--         "routine_vaccines": ["Flu", "Covid", "RSV", "None"],
--         "vaccines": ["HPV", "Shingles"],
--         "childhood_vaccinations_status": "Yes | No | I don't know",
--         "additional_vaccine_notes": "string or null"
--       }
--     },
  "routine_vaccines" text[] NULL,  -- Multi-pick (e.g., ['Flu', 'Covid'])
  "vaccines" text[] NULL,          -- Multi-pick (e.g., ['HPV', 'Shingles'])
  "childhood_vaccinations_status" text NULL, -- "Yes" | "No" | "I don't know"
  "additional_vaccine_notes" text NULL,
-- --API CALL 4 (FAMILY)
--     {
--       "survey_name": "Family Issue",
--       "notes": "string or null"  // from family_health_notes
--     },
  "family_health_notes" text NULL,
-- --API CALL 5 (DISABILITIES)
--     {
--       "survey_name": "Disabilities",
--       "disability": true,
--       "notes": "string or null"  // from disability_needs_details
--     },
  "disability" boolean NULL,
  "disability_needs_details" text NULL,
--     }
--   ],
--   "created_date": "ISO 8601 timestamp",
--   "is_deleted": false,
--   "deleted_date": "ISO 8601 timestamp or null"
  "created_date" timestamp NOT NULL DEFAULT now()
);
-- }


-- {
--   "survey_id": "string (UUID)",
--   "survey_type": "Reproductive Health Survey",
--   "child_surveys": [
--    
CREATE TABLE "user_survey_response_reproductive_health" (
  "response_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL UNIQUE REFERENCES "user" ("user_id"), -- Relates to User table
-- -- API CALL 1 (REPRODUCTIVE ANATOMY)
--  {
--       "survey_name": "Reproductive Anatomy",
--       "notes": {
--         "reproductive_organs": "Both ovaries and a uterus | One ovary and a uterus | ...",
--         "reproductive_organ_details": "string or null",
--         "reproductive_surgeries": [
--           "Hysterectomy",
--           "Oophorectomy",
--           "Tubal ligation",
--           "Other",
--           "No surgeries"
--         ],
--         "surgery_other_details": "string or null"
--       },
--     },
  "reproductive_organs" text NULL,
  "reproductive_organ_details" text NULL,
  "reproductive_surgeries" text[] NULL,
  "surgery_other_details" text NULL,
-- -- API CALL 2 (MENSTRUAL HEALTH)
--     {
--       "survey_name": "Menstrual and Hormonal Health",
--       "notes": {
--         "ever_menstruated": "Yes | No",
--         "menarche_age": "Under 10 years | 10–15 years | 16 years or older | n/a",
--         "menstruated_last_12_months": "Yes | No | n/a",
--         "last_period_date": "MM/YYYY or n/a",
--         "cycle_length": "Less than 24 days | 24–38 days | More than 38 days | Too irregular | n/a",
--         "menstrual_symptoms": [
--           "Heavy menstrual bleeding affecting quality of life",
--           "Painful periods",
--           "Spotting",
--           "No period 3+ months",
--           "Prolonged bleeding",
--           "Other irregularities"
--         ]
--       },
--     },
  "ever_menstruated" text NULL,
  "menarche_age" text NULL,
  "menstruated_last_12_months" text NULL,
  "last_period_date" text NULL,
  "cycle_length" text NULL,
  "menstrual_symptoms" text[] NULL,
  "menstrual_symptoms_other" text NULL,
-- -- API CALL 3 (HORMONAL CONTRACEPTION & HRT)
--     {
--       "survey_name": "Hormonal Contraception and HRT",
--       "notes": {
--         "currently_using_contraception": "Yes | No",
--         "current_contraception_details": "string or null",
--         "ever_used_contraception": "Yes | No",
--         "past_contraception_details": "string or null",
--         "currently_using_hrt": "Yes | No",
--         "current_hrt_details": "string or null",
--         "menstrual_status_at_hrt_start": "No period for 12+ months | Irregular periods | Regular periods"
--       },
--     },
  "currently_using_contraception" text NULL,
  "current_contraception_details" text NULL,
  "ever_used_contraception" text NULL,
  "past_contraception_details" text NULL,
  "currently_using_hrt" text NULL,
  "current_hrt_details" text NULL,
  "menstrual_status_at_hrt_start" text NULL,
-- -- API CALL 4 (PREGNANCY AND BIRTH HISTORY)
--     {
--       "survey_name": "Pregnancy and Birth History",
--       "notes": {
--         "currently_pregnant": "Yes | No",
--         "pregnancies": {
--           "total": "number",
--           "live_births": "number",
--           "stillbirths": "number",
--           "ectopics": "number",
--           "miscarriages": "number",
--           "terminations": "number"
--         },
--         "currently_breastfeeding": "Yes | No",
--         "tried_to_conceive_12_months": "Yes | No",
--         "fertility_testing": "Yes | No",
--         "fertility_testing_details": "string or null"
--       },
--     },
  "currently_pregnant" text NULL,
  "pregnancies_total" int NULL,
  "pregnancies_live_births" int NULL,
  "pregnancies_stillbirths" int NULL,
  "pregnancies_ectopics" int NULL,
  "pregnancies_miscarriages" int NULL,
  "pregnancies_terminations" int NULL,
  "currently_breastfeeding" text NULL,
  "tried_to_conceive_12_months" text NULL,
  "fertility_testing" text NULL,
  "fertility_testing_details" text NULL,
-- -- API CALL 5 (GYNAECOLOGICAL CONDITIONS)
--     {
--       "survey_name": "Gynaecological Conditions",
--       "notes": {
--         "diagnosed_conditions": [
--           "PCOS",
--           "Endometriosis",
--           "Fibroids",
--           "Adenomyosis",
--           "Pituitary dysfunction",
--           "Premature ovarian insufficiency",
--           "Other"
--         ],
--         "other_conditions_details": "string or null",
--         "pcos_screener": {
--           "irregular_periods": true,
--           "excessive_hair_growth": false,
--           "overweight_16_40": true,
--           "nipple_discharge": false
--         },
--         "endopain": {
--           "period_pain": true,
--           "pain_between_periods": false,
--           "worsening_pain": false,
--           "prolonged_period_pain": true,
--           "stabbing_pain": false,
--           "radiating_back_pain": true,
--           "hip_or_leg_pain": false,
--           "limits_daily_activities": true,
--           "disabling_pain": false,
--           "sexual_pain": {
--             "severe_pain": true,
--             "position_specific_pain": false,
--             "interrupts_sex": true
--           },
--           "bowel_and_bladder": {
--             "pain_bowel_movements": true,
--             "diarrhoea_constipation": false,
--             "bowel_cramps": true,
--             "urination_pain": false,
--             "bladder_discomfort": true
--           }
--         }
--       },
--     },
  "diagnosed_conditions" text[] NULL,
  "other_conditions_details" text NULL,
  "pcos_screener_irregular_periods" boolean NULL,
  "pcos_screener_excessive_hair_growth" boolean NULL,
  "pcos_screener_overweight_16_40" boolean NULL,
  "pcos_screener_nipple_discharge" boolean NULL,
  "endopain_period_pain" boolean NULL,
  "endopain_pain_between_periods" boolean NULL,
  "endopain_worsening_pain" boolean NULL,
  "endopain_prolonged_period_pain" boolean NULL,
  "endopain_stabbing_pain" boolean NULL,
  "endopain_radiating_back_pain" boolean NULL,
  "endopain_hip_or_leg_pain" boolean NULL,
  "endopain_limits_daily_activities" boolean NULL,
  "endopain_disabling_pain" boolean NULL,
  "endopain_sexual_severe_pain" boolean NULL,
  "endopain_sexual_position_specific_pain" boolean NULL,
  "endopain_sexual_interrupts_sex" boolean NULL,
  "endopain_bowel_and_bladder_pain_bowel_movements" boolean NULL,
  "endopain_bowel_and_bladder_diarrhoea_constipation" boolean NULL,
  "endopain_bowel_and_bladder_bowel_cramps" boolean NULL,
  "endopain_bowel_and_bladder_urination_pain" boolean NULL,
  "endopain_bowel_and_bladder_bladder_discomfort" boolean NULL,
-- -- API CALL 6 (PERI & MENOPAUSE)
--     {
--       "survey_name": "Perimenopause and Menopause",
--       "notes": {
--         "cycles_irregular_past_12_months": "Yes | No",
--         "symptoms": [
--           "Hot flushes/night sweats",
--           "Vaginal dryness",
--           "Mood changes",
--           "Sleep disturbance",
--           "Reduced libido"
--         ],
--         "menopause_status": "Perimenopause | Menopause | Post-menopause"
--       },
--     }
  "cycles_irregular_past_12_months" text NULL,
  "symptoms" text[] NULL,
  "menopause_status" text NULL,
--   ]
-- }
  "created_date" timestamp NOT NULL DEFAULT now()
);

CREATE TYPE "height_weight_unit_type" AS ENUM ('Metric', 'Imperial');
-- 
-- {
--   "survey_id": "string (UUID)",
--   "survey_type": "Lifestyle Survey",
--   "child_surveys": [
CREATE TABLE "user_survey_response_lifestyle" (
  "response_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL UNIQUE REFERENCES "user" ("user_id"), -- Relates to User table
-- --API CALL 1 (GENERAL HEALTH):
-- {      "survey_name": "General Health",
--       "notes": {
--         "general_health": "Excellent | Very good | Good | Fair | Poor",
--         "health_vs_last_year": "Much better | Somewhat better | About the same | Somewhat worse | Much worse"
--       },
--     },
  "general_health" text NULL,
  "health_vs_last_year" text NULL,
  "height" float NULL,
  "weight" float NULL,
  "height_weight_unit_type" height_weight_unit_type NULL,
-- --API CALL 2 (EXERCISE):
--     {
--       "survey_name": "Physical Activity and Exercise",
--       "notes": {
--         "daily_routine_activity": "Mostly sitting | Standing/walking | Moderate effort | Heavy work",
--         "strength_training": "Daily | 3–5x | 2x | <2x | Never",
--         "cardio_exercise": "Daily | Few times/wk | Weekly | Rarely | Never",
--         "brisk_walking": "Daily | Few times/wk | Weekly | Rarely | Never",
--         "hours_sitting_per_day": "Less than 3 | 3–6 | 6–9 | More than 9"
--       },
--     },
  "daily_routine_activity" text NULL,
  "strength_training" text NULL,
  "cardio_exercise" text NULL,
  "brisk_walking" text NULL,
  "hours_sitting_per_day" text NULL,
-- --API CALL 3 (NUTRITION):
--     {
--       "survey_name": "Nutrition and Hydration",
--       "notes": {
--         "special_diet": "No special diet | Vegetarian | Vegan | Pescatarian | Low-carb/Keto | Other",
--         "regular_diet_quality": "Very healthy | Somewhat healthy | Neutral | Unhealthy | Very unhealthy",
--         "supplements": "Yes | No",
--         "supplements_details": "string or null"
--       },
--     },
  "special_diet" text NULL,
  "special_diet_other" text NULL,
  "regular_diet_quality" text NULL,
  "supplements" text NULL,
  "supplements_details" text NULL,
-- --API CALL 4 (LIFESTYLE & WELLBEING):
--     {
--       "survey_name": "Lifestyle and Wellbeing",
--       "notes": {
--         "sleep_hours": "<5 | 5–6 | 7–8 | >8",
--         "stress_level": "Very low | Low | Moderate | High | Very high",
--         "alcohol_frequency": "Never | <1/mo | 1–4/mo | 2–3/wk | >4/wk",
--         "nicotine_use": "Yes | No",
--         "nicotine_details": "string or null",
--         "mindfulness_practice": "Yes (regularly) | Occasionally | No"
--       },
--     }
  "sleep_hours" text NULL,
  "stress_level" text NULL,
  "alcohol_frequency" text NULL,
  "nicotine_use" text NULL,
  "nicotine_details" text NULL,
  "mindfulness_practice" text NULL,
--   ]
-- }
  "created_date" timestamp NOT NULL DEFAULT now()
);

-- Magic links
CREATE TABLE "magic_link" (
  "link_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "token" text NOT NULL UNIQUE,
  "metadata" jsonb NOT NULL,
  "expires_at" timestamp NOT NULL DEFAULT now() + interval '48 hours'
);

-- User Count
CREATE TABLE "daily_user_count"(
  "date_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "date" date NOT NULL,
  "d_new_user_count" int NOT NULL,
  "d_total_user_count" int NOT NULL,
  "d_growth_rate" float NOT NULL,
  "wk_new_user_count" int NOT NULL,
  "wk_growth_rate" float NOT NULL,
  "wk_total_user_count" int NOT NULL,
  "mth_new_user_count" int NOT NULL,
  "mth_total_user_count" int NOT NULL,
  "mth_growth_rate" float NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT now()
);

CREATE TYPE "user_impact_project" AS ENUM ('Female', 'LiveBirths', 'Miscarriages', 'PCOS', 'Endometriosis', 'TryingToConceive', 'PossibleUndiagnosedPCOS', 'PossibleUndiagnosedEndometriosis');
CREATE TABLE "user_impact_log" (
  "impact_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "project" user_impact_project NOT NULL, -- In future we might migrate this to a foreign key to a project table
  "points" int NOT NULL,
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Dr Google requests
CREATE TABLE "user_question_support_request" (
  "request_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"), -- Relates to User table
  "question" text NOT NULL, -- User's question
  "response_output" text NOT NULL, -- Copy from the GPT output
  "rating" int NULL CHECK ("rating" BETWEEN 1 AND 5), -- Optional rating given by user at bottom of page
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Survey system tables
CREATE TYPE "question_type" AS ENUM ('text', 'array', 'multiarray', 'none');

-- Surveys table
CREATE TABLE "survey" (
  "survey_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "title" text NOT NULL,
  "description" text NOT NULL,
  "criteria" text NOT NULL, -- Plain text criteria for survey eligibility
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Questions table
CREATE TABLE "question" (
  "question_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "key" text NOT NULL, -- Clinical identifier for the datum this question elicits
  "question" text NOT NULL, -- Text to display to user
  "type" question_type NOT NULL CHECK (type != 'none'),  -- None is useful in API responses but we should not store questions of that type
  "options" text[] NULL, -- Options for array/multiarray types
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Survey-Question relationship table (many-to-many)
CREATE TABLE "survey_question" (
  "survey_id" text NOT NULL REFERENCES "survey" ("survey_id"),
  "question_id" text NOT NULL REFERENCES "question" ("question_id"),
  "created_date" timestamp NOT NULL DEFAULT now(),
  PRIMARY KEY ("survey_id", "question_id")
);

-- User responses to questions
CREATE TABLE "user_survey_response" (
  "response_id" text NOT NULL PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "user_id" text NOT NULL REFERENCES "user" ("user_id"),
  "survey_id" text NOT NULL REFERENCES "survey" ("survey_id"),
  "question_id" text NOT NULL REFERENCES "question" ("question_id"),
  "selected_options" text[] NULL, -- For array/multiarray types
  "answer" text NULL, -- For text type or "Other" option
  "created_date" timestamp NOT NULL DEFAULT now(),
  "is_deleted" boolean NOT NULL DEFAULT FALSE,
  "deleted_date" timestamp NULL
);

-- Index to ensure a user can only respond once to each question in a survey
CREATE UNIQUE INDEX ON "user_survey_response" ("user_id", "survey_id", "question_id") WHERE "is_deleted" = FALSE;
