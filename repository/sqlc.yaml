version: '2'
plugins:
  - name: py
    wasm:
      url: https://downloads.sqlc.dev/plugin/sqlc-gen-python_1.3.0.wasm
      sha256: fbedae96b5ecae2380a70fb5b925fd4bff58a6cfb1f3140375d098fbab7b3a3c
sql:
- engine: postgresql
  queries: queries
  schema: schema/schema.sql
  codegen:
    - out: ../backend/repository
      plugin: py
      options:
        package: backend.repository
        emit_sync_querier: false
        emit_async_querier: true
        emit_str_enum: true
        emit_pydantic_models: true
        query_parameter_limit: 2
    - out: ../worker/repository
      plugin: py
      options:
        package: repository
        emit_sync_querier: true
        emit_async_querier: false
        emit_str_enum: true
        emit_pydantic_models: true
        query_parameter_limit: 2
    - out: ../manual/instagramcheck/repository
      plugin: py
      options:
        package: manual.instagramcheck.repository
        emit_sync_querier: true
        emit_async_querier: false
        emit_str_enum: true
        emit_pydantic_models: true
        query_parameter_limit: 2
