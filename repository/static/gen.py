import json
from random import Random

# Usage: python repository/static/gen.py, then 
# for file in repository/static/generated_????.csv; do
#   echo "importing $file..."
#   psql <database connection string> \
#     -c "\COPY username(username, referral_code) FROM '$file' WITH (FORMAT csv, HEADER false)"
#   mv $file imported/$file  # or rm $file
#   sleep 2
# done


random = Random("foo")  # We use a fixed seed so that the generated data is consistent

with open("repository/static/wordlist.json") as f:
  _wordlist: dict = json.loads(f.read())
wordlist = [k for k in _wordlist.keys()]

lines = []
for a in range(1, 25):
  for b in range(1, 25):
    for c in range(1, 25):
      for d in range(1, 25):
        for e in range(1, 25):
          lines.append(f"{a}-{b}-{c}-{d}-{e},{random.choice(wordlist)}-{random.choice(wordlist)}-{random.choice(wordlist)}\n")

iteration = 0
chunk = 0
chunk_size = 10000
random.shuffle(lines)
fh = None
for line in lines:
  if iteration % chunk_size == 0:
    print(f"start chunk {chunk} (iteration {iteration})")
    if fh:
      fh.close()
    fh = open(f"repository/static/generated_{chunk:04}.csv", "w")
    chunk += 1
  fh.write(line)
  iteration += 1
fh.close()
