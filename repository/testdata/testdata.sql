UPDATE
  "username"
SET
  "assigned" = TRUE
WHERE
  "username" IN (
    '21-13-1-16-2',   -- Ed
    '5-9-6-1-17',     -- <PERSON>
    '18-1-16-22-1',   -- <PERSON><PERSON><PERSON>
    '7-12-1-11-22',   -- Deleted user
    '4-20-9-13-2',    -- Mother
    '22-23-14-15-9',  -- Father
    '4-20-3-18-12',   -- Grandmother
    '5-2-11-8-18',    -- Grandfather
    '10-23-19-11-24', -- Daughter
    '15-22-2-4-5',    -- <PERSON>
    '6-15-15-24-7',   -- Sister
    '7-6-7-20-12',    -- Brother
    '24-2-4-5-2',     -- <PERSON><PERSON><PERSON>
    '1-18-17-17-6',   -- N<PERSON>hew
    '12-24-3-21-7',   -- Aunt
    '10-14-10-14-22', -- Uncle
    '19-14-23-3-15',  -- Granddaughter
    '16-2-14-16-18',  -- <PERSON><PERSON>
    '18-19-14-19-1',  -- Main character
    '16-10-21-19-20', -- Abrar
    '4-2-12-17-1',    -- Abrar mom
    '8-5-16-2-21'     -- A<PERSON>r kid
  );

-- Test users
INSERT INTO
  "user" (
    "user_id",
    "sub",
    "username",
    "email",
    "birth_month",
    "birth_year",
    "sex_assigned_at_birth",
    "referral_code",
    "referrer",
    "created_date"
  )
VALUES
  -- Ed
  (
    'ed',
    'wN4GgDVDoncUkv7znJk3g3s11Bc2',
    '21-13-1-16-2',
    '<EMAIL>',
    4,
    1988,
    'Male',
    'harmful-dragging-uplifted',
    NULL,
  '2025-04-30T00:00:00Z'
  ),
  -- Phoebe
  (
    'phoebe',
    'XkG5cTQnAlUVGdbKU0qHN0FtZv52',
    '5-9-6-1-17',
    '<EMAIL>',
    1,
    1999,
    'Female',
    'mayflower-countable-willing',
    'harmful-dragging-uplifted',
  '2025-05-06T00:00:00Z'
  ),
  -- Brendon
  (
    'brendon',
    'somerandomstring',
    '18-1-16-22-1',
    '<EMAIL>',
    8,
    1984,
    'Male',
    'sacred-croak-juvenile',
    NULL,
  '2025-05-13T00:00:00Z'
  ),
  --Abrar Kid
  (
    'kid',
    'aVW7QS6tmcPIFFpavgYIKa5RPnU2',
    '8-5-16-2-21',
    '<EMAIL>',
    7,
    2002,
    'Female',
    'lived-ceramics-flyaway',
    NULL,
  '2025-05-20T00:00:00Z'
  ),
  --Abrar Mom
  (
    'mom',
    '3vcJimp5rzR9ZgC8DnKLvhFY8iS2',
    '4-2-12-17-1',
    '<EMAIL>',
    1,
    1970,
    'Female',
    'move-armful-surreal',
    NULL,
  '2025-05-07T00:00:00Z'
  ),
  -- Abrar
  (
    'abrar',
    'fS82kYGCLHZK0uCxBmkeaFcvNgw1',
    '16-10-21-19-20',
    '<EMAIL>',
    9,
    1990,
    'Male',
    'buckwheat-curliness-flashy',
    NULL,
    '2025-05-15T00:00:00Z'
  );


-- Deleted user
INSERT INTO
  "user" (
    "sub",
    "username",
    "email",
    "birth_month",
    "birth_year",
    "sex_assigned_at_birth",
    "referral_code",
    "is_deleted",
    "deleted_date"
  )
VALUES
  (
    'XkG5cTQnAlUVGdbKU0qHN0FtZv52',
    '7-12-1-11-22',
    '<EMAIL>',
    1,
    1999,
    'Female',
    'moocher-pang-gloater',
    TRUE,
    now()
  );


-- Test family
INSERT INTO
  "user" (
    "user_id",
    "sub",
    "username",
    "email",
    "birth_month",
    "birth_year",
    "sex_assigned_at_birth",
    "reward_points",
    "referral_code"
  )
VALUES
  -- Mother
  (
    'mother',
    'mother',
    '4-20-9-13-2',
    '<EMAIL>',
    1,
    2006,
    'Female',
    15,
    'thinning-quilt-unfocused'
  ),
  -- Father
  (
    'father',
    'father',
    '22-23-14-15-9',
    '<EMAIL>',
    1,
    2006,
    'Male',
    1,
    'evaporate-uncross-whomever'
  ),
  -- Grandmother
  (
    'grandmother',
    'grandmother',
    '4-20-3-18-12',
    '<EMAIL>',
    1,
    2006,
    'Female',
    3,
    'fancied-flagpole-shallot'
  ),
  -- Grandfather
  (
    'grandfather',
    'grandfather',
    '5-2-11-8-18',
    '<EMAIL>',
    1,
    2006,
    'Male',
    0,
    'cognitive-diligence-hatchling'
  ),
  -- Daughter
  (
    'daughter',
    'daughter',
    '10-23-19-11-24',
    '<EMAIL>',
    1,
    2006,
    'Female',
    25,
    'waking-overtly-urgent'
  ),
  -- Son
  (
    'son',
    'son',
    '15-22-2-4-5',
    '<EMAIL>',
    1,
    2006,
    'Male',
    0,
    'procedure-egging-nuclei'
  ),
  -- Sister
  (
    'sister',
    'sister',
    '6-15-15-24-7',
    '<EMAIL>',
    1,
    2006,
    'Female',
    2,
    'staff-pointer-foyer'
  ),
  -- Brother
  (
    'brother',
    'brother',
    '7-6-7-20-12',
    '<EMAIL>',
    1,
    2006,
    'Male',
    4,
    'cinema-avatar-doable'
  ),
  -- Niece
  (
    'niece',
    'niece',
    '24-2-4-5-2',
    '<EMAIL>',
    1,
    2006,
    'Female',
    6,
    'aflutter-sizzle-tropics'
  ),
  -- Nephew
  (
    'nephew',
    'nephew',
    '1-18-17-17-6',
    '<EMAIL>',
    1,
    2006,
    'Male',
    5,
    'deceiving-diary-subpar'
  ),
  -- Aunt
  (
    'aunt',
    'aunt',
    '12-24-3-21-7',
    '<EMAIL>',
    1,
    2006,
    'Female',
    18,
    'shawl-glorifier-grimy'
  ),
  -- Uncle
  (
    'uncle',
    'uncle',
    '10-14-10-14-22',
    '<EMAIL>',
    1,
    2006,
    'Male',
    2,
    'blinks-strength-pronto'
  ),
  -- Granddaughter
  (
    'granddaughter',
    'granddaughter',
    '19-14-23-3-15',
    '<EMAIL>',
    1,
    2006,
    'Female',
    3,
    'matchless-carnivore-proofs'
  ),
  -- Grandson
  (
    'grandson',
    'grandson',
    '16-2-14-16-18',
    '<EMAIL>',
    1,
    2006,
    'Male',
    1,
    'saucy-aghast-greedy'
  ),
  -- Main character
  (
    'main-character',
    'main-character',
    '18-19-14-19-1',
    '<EMAIL>',
    1,
    2006,
    'Female',
    1,
    'seventeen-diffusive-transpose'
  );

-- Create a sharing relationship between Phoebe and Daughter
INSERT INTO "user_share" (
  "sharer_id",
  "sharee_id",
  "sharee_relationship_to_sharer",
  "label_for_sharer",
  "label_for_sharee"
)
VALUES (
  'ed',
  'phoebe',
  'Daughter',
  'My precious baby angel',
  'Mummy dearest'
);

-- Sharing for test family
INSERT INTO "user_share" (
  "sharer_id",
  "sharee_id",
  "sharee_relationship_to_sharer",
  "label_for_sharer",
  "label_for_sharee",
  "approved"
)
VALUES
  -- Mother
  (
    'mother',
    'main-character',
    'Daughter',
    'My precious baby angel',
    'Mummy dearest',
    FALSE
  ),
  -- Father
  (
    'father',
    'main-character',
    'Daughter',
    'My precious baby angel',
    'Daddy',
    TRUE
  ),
  -- Grandmother
  (
    'grandmother',
    'main-character',
    'Granddaughter',
    'foo',
    'bar',
    TRUE
  ),
  -- Grandfather
  (
    'grandfather',
    'main-character',
    'Granddaughter',
    'foo',
    'bar',
    TRUE
  ),
  -- Daughter
  (
    'daughter',
    'main-character',
    'Mother',
    'foo',
    'bar',
    TRUE
  ),
  -- Son
  (
    'son',
    'main-character',
    'Mother',
    'foo',
    'bar',
    TRUE
  ),
  -- Sister
  (
    'sister',
    'main-character',
    'Sister',
    'foo',
    'bar',
    TRUE
  ),
  -- Brother
  (
    'brother',
    'main-character',
    'Sister',
    'foo',
    'bar',
    TRUE
  ),
  -- Niece
  (
    'niece',
    'main-character',
    'Aunt',
    'foo',
    'bar',
    FALSE
  ),
  -- Nephew
  (
    'nephew',
    'main-character',
    'Aunt',
    'foo',
    'bar',
    TRUE
  ),
  -- Aunt
  (
    'aunt',
    'main-character',
    'Niece',
    'foo',
    'bar',
    TRUE
  ),
  -- Uncle
  (
    'uncle',
    'main-character',
    'Niece',
    'foo',
    'bar',
    TRUE
  ),
  -- Granddaughter
  (
    'granddaughter',
    'main-character',
    'Grandmother',
    'foo',
    'bar',
    TRUE
  ),
  -- Grandson
  (
    'grandson',
    'main-character',
    'Grandmother',
    'foo',
    'bar',
    TRUE
  );

-- Referrals
-- main-character is the referrer for mother and father
UPDATE "user"
SET
  "referrer" = (SELECT "referral_code" FROM "user" WHERE "user_id" = 'main-character')
WHERE
  "user_id" IN (
    'mother',
    'father'
  );
-- aunt is the referrer for main-character
UPDATE "user"
SET
  "referrer" = (SELECT "referral_code" FROM "user" WHERE "user_id" = 'aunt')
WHERE
  "user_id" IN (
    'main-character'
  );

-- Create screenings for both users
INSERT INTO
  "user_screening" (
    "user_id",
    "type",
    "subtype",
    "next_date",
    "last_date",
    "months_between_appointments",
    "notes",
    "status"
  )
VALUES
  (
    'ed',
    'Blood Test',
    'Cholesterol',
    '2025-05-10',
    '2024-05-10',
    12,
    'Annual check-up',
    'Not yet attended'
  ),
  (
    'phoebe',
    'Mammogram',
    NULL,
    '2026-06-15',
    '2024-06-15',
    24,
    'Routine screening',
    'Not yet attended'
  );

-- Create documents for both users
INSERT INTO
  "user_document" (
    "user_id",
    "label",
    "type",
    "mime_type",
    "json_output"
  )
VALUES
  (
    'ed',
    'Cholesterol Test Results',
    'Test Results',
    'application/pdf',
    '{"summary": "200mg/dL", "keywords": "cholesterol", "text_output": "full text of document", "health_alerts": [], "health_events": []}'
  ),
  (
    'phoebe',
    'Mammogram Report',
    'Clinician Letter',
    'application/pdf',
    NULL
  );

-- Document with fixed ID for testing document_processor
INSERT INTO
  "user_document" (
    "document_id",
    "user_id",
    "label",
    "type",
    "mime_type"
  )
VALUES
  (
    '25e63ca9-6335-4260-afe7-cd59a6cc281c',
    'main-character',
    'my test document',
    'Clinician Letter',
    'application/pdf'
  );

-- Create health events for both users
INSERT INTO
  "user_health_event" (
    "user_id",
    "type",
    "details",
    "notes",
    "start_date",
    "ongoing"
  )
VALUES
  (
    'ed',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE
  ),
  (
    'phoebe',
    'Screening',
    'Routine Mammogram',
    NULL,
    '2024-06-15',
    TRUE
  );

-- Create health events for test family
INSERT INTO
  "user_health_event" (
    "user_id",
    "type",
    "details",
    "notes",
    "start_date",
    "ongoing",
    "genetic"
  )
VALUES
  -- Mother
  (
    'mother',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Father
  (
    'father',
    'Diagnosis',
    'Heart Disease',
    NULL,
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Grandmother
  (
    'grandmother',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Grandfather
  (
    'grandfather',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Daughter
  (
    'daughter',
    'Diagnosis',
    'Type 2 Diabetes',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Son
  (
    'son',
    'Diagnosis',
    'Type 2 Diabetes',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Sister
  (
    'sister',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Brother
  (
    'brother',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Niece
  (
    'niece',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Nephew
  (
    'nephew',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Aunt
  (
    'aunt',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Uncle
  (
    'uncle',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Granddaughter
  (
    'granddaughter',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  ),
  -- Grandson
  (
    'grandson',
    'Diagnosis',
    'High Cholesterol',
    '[{"date":"2023-04-23", "text": "Needs diet adjustment"}]',
    '2023-05-10',
    TRUE,
    TRUE
  );

-- Create appointment support requests for both users
INSERT INTO
  "user_appointment_support_request" (
    "user_id",
    "response_output",
    "appointment_type",
    "rating"
  )
VALUES
  (
    'ed',
    'Your appointment is confirmed for cholesterol test.',
    'Blood Test',
    5
  ),
  (
    'phoebe',
    'Your mammogram appointment is scheduled.',
    'Mammogram',
    4
  );

-- Create questions for both users
-- Create --Dr. Placeholder
INSERT INTO
  "expert" (
    "sub",
    "name",
    "qualifications",
    "email"
  )
VALUES
  (
    'placeholder',
    'Dr. Placeholder',
    'MD',
    '<EMAIL>'
  );

-- Create Tags for Answers
INSERT INTO
  "expert_answer_tag" (
    "tag_id",
    "expert_id",
    "tag_name"
  )
VALUES
  (
    'cholesterol',
    (SELECT "expert_id" FROM "expert" WHERE "sub" = 'placeholder' AND "is_deleted" = FALSE), --Dr. Placeholder
    'Cholesterol'
  ),
  (
    'mammogram',
    (SELECT "expert_id" FROM "expert" WHERE "sub" = 'placeholder' AND "is_deleted" = FALSE), --Dr. Placeholder
    'Mammogram'
  );

-- Create Answers
INSERT INTO
  "expert_answer" (
    "answer_id",
    "expert_id",
    "question_text",
    "answer_text",
    "answer_tag"
  )
VALUES
  (
    'one',
    (SELECT "expert_id" FROM "expert" WHERE "sub" = 'placeholder' AND "is_deleted" = FALSE), --Dr. Placeholder
    'What does a high cholesterol level mean?',
    'It means you have an increased risk of heart disease.',
    'cholesterol'
  ),
  (
    'two',
    (SELECT "expert_id" FROM "expert" WHERE "sub" = 'placeholder' AND "is_deleted" = FALSE), --Dr. Placeholder
    'What to expect from a mammogram?',
    'A mammogram is an X-ray of the breast used to detect early signs of breast cancer. During the procedure, each breast is placed between two plates and gently compressed for a few seconds to get clear images. This may feel uncomfortable or slightly painful, but it is quick. The entire appointment typically takes about 20 minutes. Afterward, results are reviewed by a radiologist, and any abnormalities may require further tests. Regular mammograms are recommended for early detection, usually starting at age 40 or 50, depending on personal and family history.',
    'mammogram'
  );

INSERT INTO
  "user_question" (
    "user_id",
    "question_text",
    "moderation_status",
    "answer_id"
  )
VALUES
  (
    'ed',
    'What does a high cholesterol level mean?',
    'Answered',
    'one'
  ),
  (
    'phoebe',
    'What to expect from a mammogram?',
    'Answered',
    'two'
  ),
  (
    'phoebe',
    'How can I reduce my cholesterol?',
    'Pending',
    NULL
  ),
  (
    'phoebe',
    'What will the winning lottery numbers be tomorrow?',
    'Rejected',
    NULL
  ),
  (
    'phoebe',
    'What should I do when something happens?',
    'Pending',
    NULL
  );

INSERT INTO
  "user_screening_alert" (
    "alert_id",
    "user_id",
    "type",
    "subtype",
    "next_date",
    "suggested_months_between_appointments",
    "notes",
    "status"
  )
VALUES
  (
    'main_character_screening_alert', --Fixed ID for testing
    'main-character',
    'Blood Test',
    'Cholesterol',
    NULL,
    6,
    'Helpful notes',
    'New'
  ),
  (
    'main_character_remaining_screening_alert', --Fixed ID for testing
    'main-character',
    'Mammogram',
    NULL,
    NULL,
    6,
    'Helpful notes',
    'New'
  ),
  (
    'brendon_screening_alert', --Fixed ID for testing
    'brendon',
    'Mammogram',
    NULL,
    NULL,
    6,
    'Helpful notes',
    'New'
  ),
  (
    'cholesterol', --Fixed ID for testing
    'abrar',
    'Bowel Screening',
    NULL,
    NULL,
    6,
'This has been created as you have a family member who has indicated a genetic predisposition to XYZ. Speak to your healthcare professional.',
    'New'
  ),
  (
    'abrar_screening_alert', --Fixed ID for testing
    'abrar',
    'Blood Test',
    'Cholesterol',
    NULL,
    6,
    'Helpful notes',
    'New'
  );

-- Magic link stuff
INSERT INTO "user_share" (
  "share_id",
  "sharer_id",
  "sharee_id",
  "sharee_relationship_to_sharer",
  "label_for_sharer",
  "label_for_sharee"
)
VALUES (
  'magic-link-share',
  'mother',
  'aunt',
  'Sister',
  'sister',
  'sister'
);

INSERT INTO
  "magic_link" (
    "link_id",
    "token",
    "metadata",
    "expires_at"
  )
VALUES
  (
    'expired',
    'expired-token',
    '{}',
    '1970-10-01T00:00:00Z'
  ),
  (
    'share',
    'share-token',
    '{"type": "UserShare", "payload": {"share_id": "magic-link-share", "sharer_id": "mother", "sharee_id": "aunt", "sharee_relationship_to_sharer": "Sister", "label_for_sharer": "sister", "label_for_sharee": "sister", "approved": false, "approved_date": null, "created_date": "2023-10-01T00:00:00Z", "is_deleted": false, "deleted_date": null}}',
    '2040-10-01T00:00:00Z'
  );

INSERT INTO
  "survey" (
    "survey_id",
    "title",
    "description",
    "criteria"
  )
VALUES
  (
    'food-survey',
    'Food Preferences Survey',
    'Help us understand your favorite foods and dietary preferences',
    'placeholder'
  );

-- Create food survey questions
INSERT INTO
  "question" (
    "question_id",
    "key",
    "question",
    "type",
    "options"
  )
VALUES
  (
    'food-survey-q1',
    'placeholder',
    'What is your favorite cuisine?',
    'multiarray',
    ARRAY['Italian', 'Chinese', 'Indian', 'Mexican', 'Japanese', 'Other']
  ),
  (
    'food-survey-q2',
    'placeholder',
    'How spicy do you like your food?',
    'array',
    ARRAY['1 (Mild)', '2', '3', '4', '5 (Very Spicy)']
  ),
  (
    'food-survey-q3',
    'placeholder',
    'What is your comfort food?',
    'text',
    NULL
  );

INSERT INTO
  "survey_question" (
    "survey_id",
    "question_id"
  )
VALUES
  (
    'food-survey',
    'food-survey-q1'
  ),
  (
    'food-survey',
    'food-survey-q2'
  ),
  (
    'food-survey',
    'food-survey-q3'
  );
