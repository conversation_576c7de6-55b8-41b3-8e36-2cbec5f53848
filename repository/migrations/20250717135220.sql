-- Create enum type "question_type"
CREATE TYPE "public"."question_type" AS ENUM ('text', 'array', 'multiarray', 'none');
-- Create "question" table
CREATE TABLE "public"."question" ("question_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "key" text NOT NULL, "question" text NOT NULL, "type" "public"."question_type" NOT NULL, "options" text[] NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("question_id"), CONSTRAINT "question_type_check" CHECK (type <> 'none'::public.question_type));
-- Create "survey" table
CREATE TABLE "public"."survey" ("survey_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "title" text NOT NULL, "description" text NOT NULL, "criteria" text NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("survey_id"));
-- Create "survey_question" table
CREATE TABLE "public"."survey_question" ("survey_id" text NOT NULL, "question_id" text NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), PRIMARY KEY ("survey_id", "question_id"), CONSTRAINT "survey_question_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."question" ("question_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "survey_question_survey_id_fkey" FOREIGN KEY ("survey_id") REFERENCES "public"."survey" ("survey_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_survey_response" table
CREATE TABLE "public"."user_survey_response" ("response_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "survey_id" text NOT NULL, "question_id" text NOT NULL, "selected_options" text[] NULL, "answer" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("response_id"), CONSTRAINT "user_survey_response_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."question" ("question_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_survey_response_survey_id_fkey" FOREIGN KEY ("survey_id") REFERENCES "public"."survey" ("survey_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_survey_response_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create index "user_survey_response_user_id_survey_id_question_id_idx" to table: "user_survey_response"
CREATE UNIQUE INDEX "user_survey_response_user_id_survey_id_question_id_idx" ON "public"."user_survey_response" ("user_id", "survey_id", "question_id") WHERE (is_deleted = false);
