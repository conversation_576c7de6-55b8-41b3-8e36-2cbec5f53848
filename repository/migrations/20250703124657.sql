-- Create "user_question_support_request" table
CREATE TABLE "public"."user_question_support_request" ("request_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "question" text NOT NULL, "response_output" text NOT NULL, "rating" integer NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("request_id"), CONSTRAINT "user_question_support_request_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_question_support_request_rating_check" CHECK ((rating >= 1) AND (rating <= 5)));
