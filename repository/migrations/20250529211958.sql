-- Create "expert" table
CREATE TABLE "public"."expert" ("expert_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "sub" text NOT NULL, "name" text NOT NULL, "qualifications" text NOT NULL, "email" text NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("expert_id"));
-- Create index "expert_email_idx" to table: "expert"
CREATE UNIQUE INDEX "expert_email_idx" ON "public"."expert" ("email") WHERE (is_deleted = false);
-- Create index "expert_sub_idx" to table: "expert"
CREATE UNIQUE INDEX "expert_sub_idx" ON "public"."expert" ("sub") WHERE (is_deleted = false);
-- Create enum type "user_screening_type"
CREATE TYPE "public"."user_screening_type" AS ENUM ('Cervical Smear', 'Bowel Screening', 'Mammogram', 'Scan', 'Blood Test', 'Blood Pressure', 'Consultation', 'Abdominal Aortic Aneurysm Screening', 'Diabetic Eye Screening', 'Routine Health Check', 'Lung Cancer Screening', 'Other');
-- Create enum type "user_ethnicity"
CREATE TYPE "public"."user_ethnicity" AS ENUM ('White - English, Welsh, Scottish, Northern Irish, or British', 'White - Irish', 'White - Gypsy or Irish Traveller', 'White - Roma', 'Any other White background', 'Mixed or Multiple Ethnic Groups - White and Black Caribbean', 'Mixed or Multiple Ethnic Groups - White and Black African', 'Mixed or Multiple Ethnic Groups - White and Asian', 'Any other Mixed or Multiple ethnic background', 'Asian or Asian British - Indian', 'Asian or Asian British - Pakistani', 'Asian or Asian British - Bangladeshi', 'Asian or Asian British - Chinese', 'Any other Asian background', 'Black, Black British, Caribbean, or African - Caribbean', 'Black, Black British, Caribbean, or African - African', 'Any other Black, Black British, or Caribbean background', 'Other Ethnic Group - Arab', 'Any other ethnic group');
-- Create enum type "user_risk_factors"
CREATE TYPE "public"."user_risk_factors" AS ENUM ('Diabetes Diagnosis', 'History of Smoking', 'Family History of Type 2 Diabetes', 'History of Gestational Diabetes', 'Hypertension', 'PCOS', 'Prediabetes', 'History of Heart Disease', 'History of Stroke');
-- Create enum type "user_reward_point_log_reason"
CREATE TYPE "public"."user_reward_point_log_reason" AS ENUM ('Instagram', 'Personal Health Survey', 'Reproductive Health Survey', 'Lifestyle Survey', 'Survey', 'Sharing', 'Referral', 'Being Referred', 'Redemption');
-- Create enum type "user_relationship"
CREATE TYPE "public"."user_relationship" AS ENUM ('Mother', 'Father', 'Grandmother', 'Grandfather', 'Daughter', 'Son', 'Sister', 'Brother', 'Niece', 'Nephew', 'Aunt', 'Uncle', 'Granddaughter', 'Grandson', 'Cousin', 'Parent', 'Child', 'Sibling', 'Grandparent', 'Grandchild', 'Parent''s Sibling', 'Sibling''s Child', 'Half-Brother', 'Half-Sister', 'Half-Sibling');
-- Create enum type "user_document_type"
CREATE TYPE "public"."user_document_type" AS ENUM ('Test Results', 'Clinician Letter', 'Other');
-- Create enum type "user_document_mime_type"
CREATE TYPE "public"."user_document_mime_type" AS ENUM ('application/pdf');
-- Create enum type "user_health_event_type"
CREATE TYPE "public"."user_health_event_type" AS ENUM ('Diagnosis', 'Injury', 'Prescription', 'Life Event', 'Procedure', 'Appointment', 'Health Update', 'Vaccination', 'Screening', 'Health Check', 'Mental Health Event', 'Allergy', 'Other');
-- Create "username" table
CREATE TABLE "public"."username" ("username" text NOT NULL, "referral_code" text NOT NULL, "assigned" boolean NOT NULL DEFAULT false, PRIMARY KEY ("username"), CONSTRAINT "username_referral_code_key" UNIQUE ("referral_code"));
-- Create enum type "user_screening_alert_status"
CREATE TYPE "public"."user_screening_alert_status" AS ENUM ('New', 'Merged', 'Ignored');
-- Create enum type "user_screening_status"
CREATE TYPE "public"."user_screening_status" AS ENUM ('Not yet attended', 'Attended', 'Requires update');
-- Create enum type "user_question_moderation_status"
CREATE TYPE "public"."user_question_moderation_status" AS ENUM ('Pending', 'Approved', 'Answered', 'Rejected');
-- Create enum type "height_weight_unit_type"
CREATE TYPE "public"."height_weight_unit_type" AS ENUM ('Metric', 'Imperial');
-- Create enum type "user_impact_project"
CREATE TYPE "public"."user_impact_project" AS ENUM ('Female', 'LiveBirths', 'Miscarriages', 'PCOS', 'Endometriosis', 'TryingToConceive', 'PossibleUndiagnosedPCOS', 'PossibleUndiagnosedEndometriosis');
-- Create "daily_user_count" table
CREATE TABLE "public"."daily_user_count" ("date_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "date" date NOT NULL, "d_new_user_count" integer NOT NULL, "d_total_user_count" integer NOT NULL, "d_growth_rate" double precision NOT NULL, "wk_new_user_count" integer NOT NULL, "wk_growth_rate" double precision NOT NULL, "wk_total_user_count" integer NOT NULL, "mth_new_user_count" integer NOT NULL, "mth_total_user_count" integer NOT NULL, "mth_growth_rate" double precision NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), PRIMARY KEY ("date_id"));
-- Create enum type "user_country_of_residence"
CREATE TYPE "public"."user_country_of_residence" AS ENUM ('England', 'Scotland', 'Wales', 'Northern Ireland', 'Other');
-- Create enum type "user_sex_assigned_at_birth"
CREATE TYPE "public"."user_sex_assigned_at_birth" AS ENUM ('Female', 'Male', 'Intersex');
-- Create "magic_link" table
CREATE TABLE "public"."magic_link" ("link_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "token" text NOT NULL, "metadata" jsonb NOT NULL, "expires_at" timestamp NOT NULL DEFAULT (now() + '48:00:00'::interval), PRIMARY KEY ("link_id"), CONSTRAINT "magic_link_token_key" UNIQUE ("token"));
-- Create "expert_answer_tag" table
CREATE TABLE "public"."expert_answer_tag" ("tag_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "expert_id" text NOT NULL, "tag_name" text NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("tag_id"), CONSTRAINT "expert_answer_tag_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."expert" ("expert_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "expert_answer" table
CREATE TABLE "public"."expert_answer" ("answer_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "expert_id" text NOT NULL, "question_text" text NOT NULL, "answer_text" text NOT NULL, "parent_answer_id" text NULL, "position_in_tree" integer NOT NULL DEFAULT 1, "answer_tag" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("answer_id"), CONSTRAINT "expert_answer_answer_tag_fkey" FOREIGN KEY ("answer_tag") REFERENCES "public"."expert_answer_tag" ("tag_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "expert_answer_expert_id_fkey" FOREIGN KEY ("expert_id") REFERENCES "public"."expert" ("expert_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "expert_answer_parent_answer_id_fkey" FOREIGN KEY ("parent_answer_id") REFERENCES "public"."expert_answer" ("answer_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user" table
CREATE TABLE "public"."user" ("user_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "sub" text NOT NULL, "username" text NOT NULL, "email" text NOT NULL, "birth_month" integer NOT NULL, "birth_year" integer NOT NULL, "sex_assigned_at_birth" "public"."user_sex_assigned_at_birth" NOT NULL, "reward_points" integer NOT NULL DEFAULT 0, "reward_point_awarded_for_instagram" boolean NOT NULL DEFAULT false, "reward_point_awarded_for_personal_health_survey" boolean NOT NULL DEFAULT false, "reward_point_awarded_for_reproductive_health_survey" boolean NOT NULL DEFAULT false, "reward_point_awarded_for_lifestyle_survey" boolean NOT NULL DEFAULT false, "reward_point_awarded_for_sharing" boolean NOT NULL DEFAULT false, "reward_point_awarded_to_referrer" boolean NOT NULL DEFAULT false, "referral_code" text NOT NULL, "referrer" text NULL, "instagram_handle" text NULL, "impact_points" integer NOT NULL DEFAULT 0, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("user_id"), CONSTRAINT "user_referral_code_key" UNIQUE ("referral_code"), CONSTRAINT "user_username_key" UNIQUE ("username"), CONSTRAINT "user_birth_month_check" CHECK ((birth_month >= 1) AND (birth_month <= 12)));
-- Create index "user_email_idx" to table: "user"
CREATE UNIQUE INDEX "user_email_idx" ON "public"."user" ("email") WHERE (is_deleted = false);
-- Create index "user_sub_idx" to table: "user"
CREATE UNIQUE INDEX "user_sub_idx" ON "public"."user" ("sub") WHERE (is_deleted = false);
-- Create "user_appointment_support_request" table
CREATE TABLE "public"."user_appointment_support_request" ("request_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "response_output" text NOT NULL, "appointment_type" text NOT NULL, "appointment_details" text NULL, "rating" integer NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("request_id"), CONSTRAINT "user_appointment_support_request_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_appointment_support_request_rating_check" CHECK ((rating >= 1) AND (rating <= 5)));
-- Create "user_document" table
CREATE TABLE "public"."user_document" ("document_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "label" text NULL, "type" "public"."user_document_type" NULL, "mime_type" "public"."user_document_mime_type" NULL, "json_output" jsonb NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("document_id"), CONSTRAINT "user_document_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create index "user_document_user_id_label_idx" to table: "user_document"
CREATE UNIQUE INDEX "user_document_user_id_label_idx" ON "public"."user_document" ("user_id", "label") WHERE (is_deleted = false);
-- Create "user_health_event" table
CREATE TABLE "public"."user_health_event" ("event_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "type" "public"."user_health_event_type" NOT NULL, "details" text NOT NULL, "notes" jsonb NULL, "start_date" date NOT NULL, "end_date" date NULL, "ongoing" boolean NOT NULL DEFAULT false, "genetic" boolean NOT NULL DEFAULT false, "is_reviewed" boolean NOT NULL DEFAULT false, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("event_id"), CONSTRAINT "user_health_event_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_health_update" table
CREATE TABLE "public"."user_health_update" ("update_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "text" text NOT NULL, "json_output" jsonb NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("update_id"), CONSTRAINT "user_health_update_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_impact_log" table
CREATE TABLE "public"."user_impact_log" ("impact_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "project" "public"."user_impact_project" NOT NULL, "points" integer NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("impact_id"), CONSTRAINT "user_impact_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_question" table
CREATE TABLE "public"."user_question" ("question_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "question_text" text NOT NULL, "moderation_status" "public"."user_question_moderation_status" NOT NULL, "votes" integer NOT NULL DEFAULT 1, "answer_id" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("question_id"), CONSTRAINT "user_question_answer_id_fkey" FOREIGN KEY ("answer_id") REFERENCES "public"."expert_answer" ("answer_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_question_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_reward_point_log" table
CREATE TABLE "public"."user_reward_point_log" ("log_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "points" integer NOT NULL, "reason" "public"."user_reward_point_log_reason" NOT NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("log_id"), CONSTRAINT "user_reward_point_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_screening_alert" table
CREATE TABLE "public"."user_screening_alert" ("alert_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "type" "public"."user_screening_type" NOT NULL, "subtype" text NULL, "next_date" date NULL, "suggested_months_between_appointments" integer NULL, "notes" text NULL, "status" "public"."user_screening_alert_status" NOT NULL DEFAULT 'New', "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("alert_id"), CONSTRAINT "user_screening_alert_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create index "user_screening_alert_user_id_type_subtype_status_idx" to table: "user_screening_alert"
CREATE UNIQUE INDEX "user_screening_alert_user_id_type_subtype_status_idx" ON "public"."user_screening_alert" ("user_id", "type", "subtype", "status") WHERE (is_deleted = false);
-- Create "user_screening" table
CREATE TABLE "public"."user_screening" ("screening_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "type" "public"."user_screening_type" NOT NULL, "subtype" text NULL, "next_date" date NULL, "last_date" date NULL, "attended_date" date NULL, "months_between_appointments" integer NULL, "notes" text NULL, "status" "public"."user_screening_status" NOT NULL, "user_managed_schedule" boolean NOT NULL DEFAULT false, "alert_id" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("screening_id"), CONSTRAINT "user_screening_alert_id_fkey" FOREIGN KEY ("alert_id") REFERENCES "public"."user_screening_alert" ("alert_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_screening_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create index "user_screening_user_id_type_subtype_idx" to table: "user_screening"
CREATE UNIQUE INDEX "user_screening_user_id_type_subtype_idx" ON "public"."user_screening" ("user_id", "type", "subtype") WHERE ((status = 'Not yet attended'::public.user_screening_status) AND (is_deleted = false));
-- Create "user_share" table
CREATE TABLE "public"."user_share" ("share_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "sharer_id" text NOT NULL, "sharee_id" text NOT NULL, "sharee_relationship_to_sharer" "public"."user_relationship" NOT NULL, "label_for_sharer" text NOT NULL, "label_for_sharee" text NOT NULL, "approved" boolean NOT NULL DEFAULT false, "approved_date" timestamp NULL, "created_date" timestamp NOT NULL DEFAULT now(), "is_deleted" boolean NOT NULL DEFAULT false, "deleted_date" timestamp NULL, PRIMARY KEY ("share_id"), CONSTRAINT "user_share_sharee_id_fkey" FOREIGN KEY ("sharee_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION, CONSTRAINT "user_share_sharer_id_fkey" FOREIGN KEY ("sharer_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create index "user_share_sharer_id_sharee_id_idx" to table: "user_share"
CREATE UNIQUE INDEX "user_share_sharer_id_sharee_id_idx" ON "public"."user_share" ("sharer_id", "sharee_id") WHERE (is_deleted = false);
-- Create "user_survey_response_lifestyle" table
CREATE TABLE "public"."user_survey_response_lifestyle" ("response_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "general_health" text NULL, "health_vs_last_year" text NULL, "height" double precision NULL, "weight" double precision NULL, "height_weight_unit_type" "public"."height_weight_unit_type" NULL, "daily_routine_activity" text NULL, "strength_training" text NULL, "cardio_exercise" text NULL, "brisk_walking" text NULL, "hours_sitting_per_day" text NULL, "special_diet" text NULL, "special_diet_other" text NULL, "regular_diet_quality" text NULL, "supplements" text NULL, "supplements_details" text NULL, "sleep_hours" text NULL, "stress_level" text NULL, "alcohol_frequency" text NULL, "nicotine_use" text NULL, "nicotine_details" text NULL, "mindfulness_practice" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), PRIMARY KEY ("response_id"), CONSTRAINT "user_survey_response_lifestyle_user_id_key" UNIQUE ("user_id"), CONSTRAINT "user_survey_response_lifestyle_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_survey_response_personal_health" table
CREATE TABLE "public"."user_survey_response_personal_health" ("response_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "gp_postcode" text NULL, "ethnicity" text NULL, "gender" text NULL, "country" text NULL, "current_health_condition" boolean NULL, "historic_health_condition" boolean NULL, "injuries" boolean NULL, "allergies" boolean NULL, "medications" boolean NULL, "routine_vaccines" text[] NULL, "vaccines" text[] NULL, "childhood_vaccinations_status" text NULL, "additional_vaccine_notes" text NULL, "family_health_notes" text NULL, "disability" boolean NULL, "disability_needs_details" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), PRIMARY KEY ("response_id"), CONSTRAINT "user_survey_response_personal_health_user_id_key" UNIQUE ("user_id"), CONSTRAINT "user_survey_response_personal_health_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "user_survey_response_reproductive_health" table
CREATE TABLE "public"."user_survey_response_reproductive_health" ("response_id" text NOT NULL DEFAULT (gen_random_uuid())::text, "user_id" text NOT NULL, "reproductive_organs" text NULL, "reproductive_organ_details" text NULL, "reproductive_surgeries" text[] NULL, "surgery_other_details" text NULL, "ever_menstruated" text NULL, "menarche_age" text NULL, "menstruated_last_12_months" text NULL, "last_period_date" text NULL, "cycle_length" text NULL, "menstrual_symptoms" text[] NULL, "menstrual_symptoms_other" text NULL, "currently_using_contraception" text NULL, "current_contraception_details" text NULL, "ever_used_contraception" text NULL, "past_contraception_details" text NULL, "currently_using_hrt" text NULL, "current_hrt_details" text NULL, "menstrual_status_at_hrt_start" text NULL, "currently_pregnant" text NULL, "pregnancies_total" integer NULL, "pregnancies_live_births" integer NULL, "pregnancies_stillbirths" integer NULL, "pregnancies_ectopics" integer NULL, "pregnancies_miscarriages" integer NULL, "pregnancies_terminations" integer NULL, "currently_breastfeeding" text NULL, "tried_to_conceive_12_months" text NULL, "fertility_testing" text NULL, "fertility_testing_details" text NULL, "diagnosed_conditions" text[] NULL, "other_conditions_details" text NULL, "pcos_screener_irregular_periods" boolean NULL, "pcos_screener_excessive_hair_growth" boolean NULL, "pcos_screener_overweight_16_40" boolean NULL, "pcos_screener_nipple_discharge" boolean NULL, "endopain_period_pain" boolean NULL, "endopain_pain_between_periods" boolean NULL, "endopain_worsening_pain" boolean NULL, "endopain_prolonged_period_pain" boolean NULL, "endopain_stabbing_pain" boolean NULL, "endopain_radiating_back_pain" boolean NULL, "endopain_hip_or_leg_pain" boolean NULL, "endopain_limits_daily_activities" boolean NULL, "endopain_disabling_pain" boolean NULL, "endopain_sexual_severe_pain" boolean NULL, "endopain_sexual_position_specific_pain" boolean NULL, "endopain_sexual_interrupts_sex" boolean NULL, "endopain_bowel_and_bladder_pain_bowel_movements" boolean NULL, "endopain_bowel_and_bladder_diarrhoea_constipation" boolean NULL, "endopain_bowel_and_bladder_bowel_cramps" boolean NULL, "endopain_bowel_and_bladder_urination_pain" boolean NULL, "endopain_bowel_and_bladder_bladder_discomfort" boolean NULL, "cycles_irregular_past_12_months" text NULL, "symptoms" text[] NULL, "menopause_status" text NULL, "created_date" timestamp NOT NULL DEFAULT now(), PRIMARY KEY ("response_id"), CONSTRAINT "user_survey_response_reproductive_health_user_id_key" UNIQUE ("user_id"), CONSTRAINT "user_survey_response_reproductive_health_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION);
